FROM golang:1.24 AS builder

WORKDIR /app
COPY . .
ENV GOPROXY=https://goproxy.cn,direct
RUN ls -al
RUN mkdir -p build
RUN go mod tidy
# 管理后台
RUN go build -ldflags="-s -w" -o ./build/cms ./cmd/cms
# 万年历
RUN go build -ldflags="-s -w" -o ./build/calendar ./cmd/calendar
# 运势
RUN go build -ldflags="-s -w" -o ./build/fortune ./cmd/fortune
# 论财
RUN go build -ldflags="-s -w" -o ./build/wealth ./cmd/wealth
# 排盘专家版
RUN go build -ldflags="-s -w" -o ./build/master ./cmd/master
# 在线投放
RUN go build -ldflags="-s -w" -o ./build/adflow ./cmd/adflow
# 认证中心
RUN go build -ldflags="-s -w" -o ./build/authcenter ./cmd/authcenter

FROM debian:stable-slim

ENV TZ=Asia/Shanghai

RUN apt-get update && apt-get install -y --no-install-recommends \
        ca-certificates \
        tzdata \
        netbase \
    && ln -sf /usr/share/zoneinfo/${TZ} /etc/localtime \
    && echo ${TZ} > /etc/timezone \
    && dpkg-reconfigure -f noninteractive tzdata \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get autoremove -y && apt-get autoclean -y


WORKDIR /app
RUN mkdir -p /app/etc
RUN mkdir -p /app/assets
COPY --from=builder /app/etc /app/etc
#COPY --from=builder /app/assets /app/assets
COPY ./assets/GeoLite2-City.mmdb /app/assets/GeoLite2-City.mmdb
# 管理后台
COPY --from=builder /app/build/cms /app/cms
# 万年历
COPY --from=builder /app/build/calendar /app/calendar
# 运势
COPY --from=builder /app/build/fortune /app/fortune
# 论财
COPY --from=builder /app/build/wealth /app/wealth
# 排盘专家版
COPY --from=builder /app/build/master /app/master
# 在线投放
COPY --from=builder /app/build/adflow /app/adflow
# 认证中心
COPY --from=builder /app/build/authcenter /app/authcenter

# 管理后台
EXPOSE 60110
# 排盘（预留）
EXPOSE 60111
# 万年历
EXPOSE 60112
# 运势
EXPOSE 60113
# 论财
EXPOSE 60114
# 排盘专家版
EXPOSE 60115
# 在线投放
EXPOSE 60116
# 认证中心
EXPOSE 60000