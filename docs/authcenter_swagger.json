{"swagger": "2.0", "info": {"description": "This is a http server template.", "title": "AuthCenter API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0.0"}, "paths": {"/auth/login/umeng/mobile": {"post": {"description": "友盟一键登录", "produces": ["application/json"], "tags": ["auth"], "summary": "友盟一键登录", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.LoginByUMengMobileInfoRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.LoginByUMengMobileInfoResponse"}}}}}, "/user/deactivate": {"post": {"security": [{"BearerAuth": []}], "description": "注销", "produces": ["application/json"], "tags": ["auth"], "summary": "注销", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.DeactivateRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.DeactivateResponse"}}}}}, "/user/getImageCode": {"post": {"description": "生成图片验证码", "produces": ["application/json"], "tags": ["auth"], "summary": "生成图片验证码", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.GenImageCodeRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.GenImageCodeResponse"}}}}}, "/user/getUserInfoByToken": {"post": {"security": [{"BearerAuth": []}], "description": "通过Token获取用户信息", "produces": ["application/json"], "tags": ["auth"], "summary": "通过Token获取用户信息", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.GetUserInfoRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.GetUserInfoResponse"}}}}}, "/user/login": {"post": {"description": "管理后台登录", "produces": ["application/json"], "tags": ["auth"], "summary": "管理后台登录", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.AdminLoginRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.AdminLoginResponse"}}}}}, "/user/loginByWechat": {"post": {"description": "微信小程序登录", "produces": ["application/json"], "tags": ["auth"], "summary": "微信小程序登录", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.LoginByWechatMiniProgramRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.LoginByWechatMiniProgramResponse"}}}}}, "/user/phoneLogin": {"post": {"description": "手机号快速登录/一键登录（uniapp）", "produces": ["application/json"], "tags": ["auth"], "summary": "手机号快速登录/一键登录（uniapp）", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.LoginByPhoneQuickly4UniRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.LoginByPhoneQuickly4UniResponse"}}}}}, "/user/smsAuth": {"post": {"description": "发送短信验证码", "produces": ["application/json"], "tags": ["auth"], "summary": "发送短信验证码", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.SMSAuthRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.SMSAuthResponse"}}}}}, "/user/superLogin": {"post": {"description": "短信验证码登录", "produces": ["application/json"], "tags": ["auth"], "summary": "短信验证码登录", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.LoginByPhoneRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.LoginByPhoneResponse"}}}}}, "/user/wx/code2session": {"post": {"description": "微信js_code换取session（小程序/服务号）", "produces": ["application/json"], "tags": ["auth"], "summary": "微信js_code换取session（小程序/服务号）", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.WechatJsCode2SessionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.WechatCode2SessionResponse"}}}}}, "/user/wxLogin": {"post": {"description": "微信小程序code2session", "produces": ["application/json"], "tags": ["auth"], "summary": "微信小程序code2session", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.WechatMiniProgramJsCode2SessionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.WechatMiniProgramJsCode2SessionResponse"}}}}}}, "definitions": {"v1.AdminLoginRequest": {"type": "object", "required": ["password", "userName"], "properties": {"password": {"type": "string", "example": "123456"}, "userName": {"type": "string", "example": "admin"}}}, "v1.AdminLoginResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.AdminLoginResponseData"}, "message": {"type": "string"}}}, "v1.AdminLoginResponseData": {"type": "object", "properties": {"accessToken": {"description": "访问令牌", "type": "string"}, "expiresIn": {"description": "过期时间", "type": "integer"}, "refreshToken": {"description": "刷新令牌（客户端并未使用）", "type": "string"}}}, "v1.DeactivateRequest": {"type": "object"}, "v1.DeactivateResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.DeactivateResponseData"}, "message": {"type": "string"}}}, "v1.DeactivateResponseData": {"type": "object", "properties": {"success": {"type": "boolean"}}}, "v1.GenImageCodeRequest": {"type": "object", "properties": {"applicationId": {"description": "应用ID", "type": "string", "example": "admin/application_horoscope"}}}, "v1.GenImageCodeResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.GenImageCodeResponseData"}, "message": {"type": "string"}}}, "v1.GenImageCodeResponseData": {"type": "object", "properties": {"captchaId": {"type": "string"}, "captchaImage": {"type": "array", "items": {"type": "integer"}}}}, "v1.GetUserInfoRequest": {"type": "object"}, "v1.GetUserInfoResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.GetUserInfoResponseData"}, "message": {"type": "string"}}}, "v1.GetUserInfoResponseData": {"type": "object", "properties": {"displayName": {"description": "昵称", "type": "string"}, "expireTime": {"description": "过期时间（秒级时间戳）", "type": "integer"}, "id": {"description": "用户唯一ID", "type": "string"}, "isPaid": {"description": "是否付费", "type": "boolean"}}}, "v1.LoginByPhoneQuickly4UniRequest": {"type": "object", "properties": {"accessToken": {"description": "访问令牌", "type": "string"}, "application": {"description": "应用ID", "type": "string"}, "openid": {"description": "开放ID", "type": "string"}}}, "v1.LoginByPhoneQuickly4UniResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.LoginByPhoneQuickly4UniResponseData"}, "message": {"type": "string"}}}, "v1.LoginByPhoneQuickly4UniResponseData": {"type": "object", "properties": {"accessToken": {"description": "访问令牌", "type": "string"}, "expiresIn": {"description": "过期时间", "type": "integer"}, "refreshToken": {"description": "刷新令牌（客户端并未使用）", "type": "string"}}}, "v1.LoginByPhoneRequest": {"type": "object", "properties": {"application": {"description": "应用ID", "type": "string", "example": "application_horoscope"}, "clientId": {"description": "客户端ID", "type": "string", "example": "2c027e9509c551e0be62"}, "organization": {"description": "组织ID", "type": "string", "example": "organization_taisu"}, "password": {"description": "密码", "type": "string", "example": "751581"}, "username": {"description": "用户名", "type": "string", "example": "13069105206"}}}, "v1.LoginByPhoneResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.LoginByPhoneResponseData"}, "message": {"type": "string"}}}, "v1.LoginByPhoneResponseData": {"type": "object", "properties": {"accessToken": {"description": "访问令牌", "type": "string"}, "expiresIn": {"description": "过期时间", "type": "integer"}, "refreshToken": {"description": "刷新令牌（客户端并未使用）", "type": "string"}}}, "v1.LoginByUMengMobileInfoRequest": {"type": "object", "required": ["app", "token", "verifyId"], "properties": {"app": {"type": "string", "example": "android || ios"}, "token": {"type": "string"}, "verifyId": {"type": "string"}}}, "v1.LoginByUMengMobileInfoResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.LoginByUMengMobileInfoResponseData"}, "message": {"type": "string"}}}, "v1.LoginByUMengMobileInfoResponseData": {"type": "object", "properties": {"accessToken": {"description": "访问令牌", "type": "string"}, "expiresIn": {"description": "过期时间", "type": "integer"}, "refreshToken": {"description": "刷新令牌（客户端并未使用）", "type": "string"}}}, "v1.LoginByWechatMiniProgramRequest": {"type": "object", "properties": {"code": {"description": "微信code", "type": "string"}}}, "v1.LoginByWechatMiniProgramResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.LoginByWechatMiniProgramResponseData"}, "message": {"type": "string"}}}, "v1.LoginByWechatMiniProgramResponseData": {"type": "object", "properties": {"accessToken": {"description": "访问令牌", "type": "string"}, "expiresIn": {"description": "过期时间", "type": "integer"}, "refreshToken": {"description": "刷新令牌（客户端并未使用）", "type": "string"}}}, "v1.SMSAuthRequest": {"type": "object", "properties": {"captchaToken": {"description": "图片验证码code", "type": "string", "example": "1234"}, "clientSecret": {"description": "图片验证码id", "type": "string", "example": "lYGEQkzDN92FX6x3BVXc"}, "dest": {"description": "手机号", "type": "string", "example": "13069105206"}, "method": {"description": "方法：login-登录", "type": "string", "example": "login"}}}, "v1.SMSAuthResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "string"}, "message": {"type": "string"}}}, "v1.WechatCode2SessionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.WechatJsCode2SessionResponseData"}, "message": {"type": "string"}}}, "v1.WechatJsCode2SessionRequest": {"type": "object", "properties": {"application": {"description": "应用ID", "type": "string"}, "js_code": {"description": "微信code", "type": "string"}, "type": {"description": "类型：1-小程序、2-服务号", "type": "integer"}}}, "v1.WechatJsCode2SessionResponseData": {"type": "object", "properties": {"openid": {"type": "string"}, "session_key": {"type": "string"}, "unionid": {"type": "string"}}}, "v1.WechatMiniProgramJsCode2SessionRequest": {"type": "object", "properties": {"application": {"description": "应用ID", "type": "string"}, "js_code": {"description": "微信code", "type": "string"}}}, "v1.WechatMiniProgramJsCode2SessionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}