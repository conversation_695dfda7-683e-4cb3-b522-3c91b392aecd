definitions:
  v1.AdminLoginRequest:
    properties:
      password:
        example: "123456"
        type: string
      userName:
        example: admin
        type: string
    required:
    - password
    - userName
    type: object
  v1.AdminLoginResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.AdminLoginResponseData'
      message:
        type: string
    type: object
  v1.AdminLoginResponseData:
    properties:
      accessToken:
        description: 访问令牌
        type: string
      expiresIn:
        description: 过期时间
        type: integer
      refreshToken:
        description: 刷新令牌（客户端并未使用）
        type: string
    type: object
  v1.DeactivateRequest:
    type: object
  v1.DeactivateResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.DeactivateResponseData'
      message:
        type: string
    type: object
  v1.DeactivateResponseData:
    properties:
      success:
        type: boolean
    type: object
  v1.GenImageCodeRequest:
    properties:
      applicationId:
        description: 应用ID
        example: admin/application_horoscope
        type: string
    type: object
  v1.GenImageCodeResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.GenImageCodeResponseData'
      message:
        type: string
    type: object
  v1.GenImageCodeResponseData:
    properties:
      captchaId:
        type: string
      captchaImage:
        items:
          type: integer
        type: array
    type: object
  v1.GetUserInfoRequest:
    type: object
  v1.GetUserInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.GetUserInfoResponseData'
      message:
        type: string
    type: object
  v1.GetUserInfoResponseData:
    properties:
      displayName:
        description: 昵称
        type: string
      expireTime:
        description: 过期时间（秒级时间戳）
        type: integer
      id:
        description: 用户唯一ID
        type: string
      isPaid:
        description: 是否付费
        type: boolean
    type: object
  v1.LoginByPhoneQuickly4UniRequest:
    properties:
      accessToken:
        description: 访问令牌
        type: string
      application:
        description: 应用ID
        type: string
      openid:
        description: 开放ID
        type: string
    type: object
  v1.LoginByPhoneQuickly4UniResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.LoginByPhoneQuickly4UniResponseData'
      message:
        type: string
    type: object
  v1.LoginByPhoneQuickly4UniResponseData:
    properties:
      accessToken:
        description: 访问令牌
        type: string
      expiresIn:
        description: 过期时间
        type: integer
      refreshToken:
        description: 刷新令牌（客户端并未使用）
        type: string
    type: object
  v1.LoginByPhoneRequest:
    properties:
      application:
        description: 应用ID
        example: application_horoscope
        type: string
      clientId:
        description: 客户端ID
        example: 2c027e9509c551e0be62
        type: string
      organization:
        description: 组织ID
        example: organization_taisu
        type: string
      password:
        description: 密码
        example: "751581"
        type: string
      username:
        description: 用户名
        example: "13069105206"
        type: string
    type: object
  v1.LoginByPhoneResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.LoginByPhoneResponseData'
      message:
        type: string
    type: object
  v1.LoginByPhoneResponseData:
    properties:
      accessToken:
        description: 访问令牌
        type: string
      expiresIn:
        description: 过期时间
        type: integer
      refreshToken:
        description: 刷新令牌（客户端并未使用）
        type: string
    type: object
  v1.LoginByWechatMiniProgramRequest:
    properties:
      application:
        description: 应用ID
        type: string
      code:
        description: 微信code
        type: string
    type: object
  v1.LoginByWechatMiniProgramResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.LoginByWechatMiniProgramResponseData'
      message:
        type: string
    type: object
  v1.LoginByWechatMiniProgramResponseData:
    properties:
      accessToken:
        description: 访问令牌
        type: string
      expiresIn:
        description: 过期时间
        type: integer
      refreshToken:
        description: 刷新令牌（客户端并未使用）
        type: string
    type: object
  v1.SMSAuthRequest:
    properties:
      captchaToken:
        description: 图片验证码code
        example: "1234"
        type: string
      clientSecret:
        description: 图片验证码id
        example: lYGEQkzDN92FX6x3BVXc
        type: string
      dest:
        description: 手机号
        example: "13069105206"
        type: string
      method:
        description: 方法：login-登录
        example: login
        type: string
    type: object
  v1.SMSAuthResponse:
    properties:
      code:
        type: integer
      data:
        type: string
      message:
        type: string
    type: object
  v1.WechatCode2SessionResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.WechatJsCode2SessionResponseData'
      message:
        type: string
    type: object
  v1.WechatJsCode2SessionRequest:
    properties:
      application:
        description: 应用ID
        type: string
      js_code:
        description: 微信code
        type: string
      type:
        description: 类型：1-小程序、2-服务号
        type: integer
    type: object
  v1.WechatJsCode2SessionResponseData:
    properties:
      openid:
        type: string
      session_key:
        type: string
      unionid:
        type: string
    type: object
  v1.WechatMiniProgramJsCode2SessionRequest:
    properties:
      application:
        description: 应用ID
        type: string
      js_code:
        description: 微信code
        type: string
    type: object
  v1.WechatMiniProgramJsCode2SessionResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a http server template.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: AuthCenter API
  version: 1.0.0
paths:
  /user/deactivate:
    post:
      description: 注销
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DeactivateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.DeactivateResponse'
      security:
      - BearerAuth: []
      summary: 注销
      tags:
      - auth
  /user/getImageCode:
    post:
      description: 生成图片验证码
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.GenImageCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.GenImageCodeResponse'
      summary: 生成图片验证码
      tags:
      - auth
  /user/getUserInfoByToken:
    post:
      description: 通过Token获取用户信息
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.GetUserInfoRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.GetUserInfoResponse'
      security:
      - BearerAuth: []
      summary: 通过Token获取用户信息
      tags:
      - auth
  /user/login:
    post:
      description: 管理后台登录
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.AdminLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.AdminLoginResponse'
      summary: 管理后台登录
      tags:
      - auth
  /user/loginByWechat:
    post:
      description: 微信小程序登录
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.LoginByWechatMiniProgramRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.LoginByWechatMiniProgramResponse'
      summary: 微信小程序登录
      tags:
      - auth
  /user/phoneLogin:
    post:
      description: 手机号快速登录/一键登录（uniapp）
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.LoginByPhoneQuickly4UniRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.LoginByPhoneQuickly4UniResponse'
      summary: 手机号快速登录/一键登录（uniapp）
      tags:
      - auth
  /user/smsAuth:
    post:
      description: 发送短信验证码
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.SMSAuthRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.SMSAuthResponse'
      summary: 发送短信验证码
      tags:
      - auth
  /user/superLogin:
    post:
      description: 短信验证码登录
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.LoginByPhoneRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.LoginByPhoneResponse'
      summary: 短信验证码登录
      tags:
      - auth
  /user/wx/code2session:
    post:
      description: 微信js_code换取session（小程序/服务号）
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.WechatJsCode2SessionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.WechatCode2SessionResponse'
      summary: 微信js_code换取session（小程序/服务号）
      tags:
      - auth
  /user/wxLogin:
    post:
      description: 微信小程序code2session
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.WechatMiniProgramJsCode2SessionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.WechatMiniProgramJsCode2SessionResponse'
      summary: 微信小程序code2session
      tags:
      - auth
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
