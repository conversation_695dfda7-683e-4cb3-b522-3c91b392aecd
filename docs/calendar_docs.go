// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplatecalendar = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/app/version/newest": {
            "post": {
                "description": "最新版本",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用版本"
                ],
                "summary": "最新版本",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CheckAppUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "最新版本",
                        "schema": {
                            "$ref": "#/definitions/v1.CheckAppUpdateResponse"
                        }
                    }
                }
            }
        },
        "/calendar/day": {
            "post": {
                "description": "获取本日日历",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日历"
                ],
                "summary": "获取本日日历",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarDayRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarDayResponse"
                        }
                    }
                }
            }
        },
        "/calendar/day/dowhat": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "今天干什么",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日历"
                ],
                "summary": "今天干什么",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarDayDoWhatRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarDayDoWhatResponse"
                        }
                    }
                }
            }
        },
        "/calendar/day/vip": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取本日日历",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日历"
                ],
                "summary": "获取本日日历",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarDayRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarDayResponse"
                        }
                    }
                }
            }
        },
        "/calendar/jiaoyun": {
            "post": {
                "description": "交运",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日历"
                ],
                "summary": "交运",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.GetJiaoyunRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.GetJiaoyunResponse"
                        }
                    }
                }
            }
        },
        "/calendar/month": {
            "post": {
                "description": "获取本月日历",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日历"
                ],
                "summary": "获取本月日历",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarMonthRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarMonthResponse"
                        }
                    }
                }
            }
        },
        "/calendar/month/vip": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取本月日历",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日历"
                ],
                "summary": "获取本月日历",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarMonthRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarMonthResponse"
                        }
                    }
                }
            }
        },
        "/datesub": {
            "post": {
                "description": "日期换算",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日期换算"
                ],
                "summary": "日期换算",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.DateSubRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.DateSubResponse"
                        }
                    }
                }
            }
        },
        "/datesub/future": {
            "post": {
                "description": "未来日期",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日期换算"
                ],
                "summary": "未来日期",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.DateSubFutureRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.DateSubFutureResponse"
                        }
                    }
                }
            }
        },
        "/datesub/past": {
            "post": {
                "description": "过去日期",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日期换算"
                ],
                "summary": "过去日期",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.DateSubPastRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.DateSubPastResponse"
                        }
                    }
                }
            }
        },
        "/datetime/fromSizhu": {
            "post": {
                "description": "从四柱获取时间",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "时间"
                ],
                "summary": "从四柱获取时间",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.GetDatetimeBySizhuRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.GetDatetimeBySizhuResponse"
                        }
                    }
                }
            }
        },
        "/enums/jieqi": {
            "post": {
                "description": "获取节气列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "枚举"
                ],
                "summary": "获取节气列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsJieqiRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsJieqiResponse"
                        }
                    }
                }
            }
        },
        "/enums/jixiong": {
            "post": {
                "description": "获取吉/凶神列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "枚举"
                ],
                "summary": "获取吉/凶神列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsJiXiongRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsJiXiongResponse"
                        }
                    }
                }
            }
        },
        "/enums/location": {
            "post": {
                "description": "获取地区列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "枚举"
                ],
                "summary": "获取地区列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsLocationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsLocationResponse"
                        }
                    }
                }
            }
        },
        "/enums/lunar": {
            "post": {
                "description": "获取农历列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "枚举"
                ],
                "summary": "获取农历列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsLunarRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsLunarResponse"
                        }
                    }
                }
            }
        },
        "/enums/nayin": {
            "post": {
                "description": "获取纳音列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "枚举"
                ],
                "summary": "获取纳音列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsNayinRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsNayinResponse"
                        }
                    }
                }
            }
        },
        "/enums/pengzubaiji": {
            "post": {
                "description": "获取彭祖百忌列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "枚举"
                ],
                "summary": "获取彭祖百忌列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsPengzubaijiRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsPengzubaijiResponse"
                        }
                    }
                }
            }
        },
        "/enums/shierjianri": {
            "post": {
                "description": "获取十二建日列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "枚举"
                ],
                "summary": "获取十二建日列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsShierjianriRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsShierjianriResponse"
                        }
                    }
                }
            }
        },
        "/enums/time": {
            "post": {
                "description": "获取时辰列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "枚举"
                ],
                "summary": "获取时辰列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsTimeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsTimeResponse"
                        }
                    }
                }
            }
        },
        "/enums/wuxing": {
            "post": {
                "description": "获取五行列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "枚举"
                ],
                "summary": "获取五行列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsWuxingRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsWuxingResponse"
                        }
                    }
                }
            }
        },
        "/enums/yiji": {
            "post": {
                "description": "获取宜忌列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "枚举"
                ],
                "summary": "获取宜忌列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsYijiRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsYijiResponse"
                        }
                    }
                }
            }
        },
        "/enums/zhishen": {
            "post": {
                "description": "获取值神列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "枚举"
                ],
                "summary": "获取值神列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsZhishenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsZhishenResponse"
                        }
                    }
                }
            }
        },
        "/historyDayEvents": {
            "post": {
                "description": "获取历史上的今天",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "历史上的今天"
                ],
                "summary": "获取历史上的今天",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.GetHistoryDayEventsRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.GetHistoryDayEventsResponse"
                        }
                    }
                }
            }
        },
        "/jiemeng": {
            "post": {
                "description": "获取解梦",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "解梦"
                ],
                "summary": "获取解梦",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.JiemengRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.JiemengResponse"
                        }
                    }
                }
            }
        },
        "/userCountdownDay/create": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建倒数日",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "倒数日"
                ],
                "summary": "创建倒数日",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CreateUserCountdownDayRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.CreateUserCountdownDayResponse"
                        }
                    }
                }
            }
        },
        "/userCountdownDay/delete": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除倒数日",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "倒数日"
                ],
                "summary": "删除倒数日",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.DeleteUserCountdownDayRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/userCountdownDay/list": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取倒数日列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "倒数日"
                ],
                "summary": "获取倒数日列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.ListUserCountdownDayRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.ListUserCountdownDayResponse"
                        }
                    }
                }
            }
        },
        "/userCountdownDay/update": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新倒数日",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "倒数日"
                ],
                "summary": "更新倒数日",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.UpdateUserCountdownDayRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/userMingli/create": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建命例",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "命例"
                ],
                "summary": "创建命例",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CreateUserMingliRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.CreateUserMingliResponse"
                        }
                    }
                }
            }
        },
        "/userMingli/delete": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除命例",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "命例"
                ],
                "summary": "删除命例",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.DeleteUserMingliRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.DeleteUserMingliResponse"
                        }
                    }
                }
            }
        },
        "/userMingli/jieqiScore": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取节气得分",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "命例"
                ],
                "summary": "获取节气得分",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.MingliJieqiScoreRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.MingliJieqiScoreResponse"
                        }
                    }
                }
            }
        },
        "/userMingli/list": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取命例列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "命例"
                ],
                "summary": "获取命例列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.ListUserMingliRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.ListUserMingliResponse"
                        }
                    }
                }
            }
        },
        "/userMingli/paipan": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "八字排盘",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "命例"
                ],
                "summary": "八字排盘",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.PaipanRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.PaipanResponse"
                        }
                    }
                }
            }
        },
        "/userMingli/setDefault": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "设置默认命例",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "命例"
                ],
                "summary": "设置默认命例",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.SetDefaultUserMingliRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.SetDefaultUserMingliResponse"
                        }
                    }
                }
            }
        },
        "/userMingli/setWuxing": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "设置命例五行",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "命例"
                ],
                "summary": "设置命例五行",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.SetMingliWuxingRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.SetMingliWuxingResponse"
                        }
                    }
                }
            }
        },
        "/userMingli/update": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新命例",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "命例"
                ],
                "summary": "更新命例",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.UpdateUserMingliRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.UpdateUserMingliResponse"
                        }
                    }
                }
            }
        },
        "/userMingli/yunScore": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取大运流年得分",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "命例"
                ],
                "summary": "获取大运流年得分",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.MingliDayunliulianScoreRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.MingliDayunliulianScoreResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "model.UserMingliDayun": {
            "type": "object",
            "properties": {
                "endYear": {
                    "type": "integer"
                },
                "startYear": {
                    "type": "integer"
                },
                "values": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "model.UserMingliXiaoyun": {
            "type": "object",
            "properties": {
                "endYear": {
                    "type": "integer"
                },
                "startYear": {
                    "type": "integer"
                },
                "subYear": {
                    "type": "integer"
                },
                "values": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "v1.CalendarDayDoWhatRequest": {
            "type": "object",
            "properties": {
                "appID": {
                    "description": "应用：2-排盘、3-万年历、4-运势、5-论财",
                    "type": "integer"
                },
                "mingliID": {
                    "description": "命例ID",
                    "type": "integer",
                    "example": 1
                },
                "time": {
                    "description": "时间",
                    "type": "string",
                    "example": "2021-01-01 12:00:00"
                }
            }
        },
        "v1.CalendarDayDoWhatResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/v1.CalendarDayDoWhatResponseData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.CalendarDayDoWhatResponseData": {
            "type": "object",
            "properties": {
                "dressing": {
                    "description": "1.5 每日穿衣五行",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "fateScore": {
                    "description": "1.1 今日运势分数",
                    "type": "number",
                    "example": 75
                },
                "guiren": {
                    "description": "1.8 贵人",
                    "type": "string",
                    "example": "天乙贵人"
                },
                "hasJiaoyun": {
                    "description": "是否已交运",
                    "type": "boolean",
                    "example": true
                },
                "healthScore": {
                    "description": "1.2.4 健康分数",
                    "type": "number",
                    "example": 75
                },
                "healthWarning": {
                    "description": "1.2.4 健康预警",
                    "type": "boolean",
                    "example": true
                },
                "jiWhat": {
                    "description": "每日忌what",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "阴宅破土",
                        "安葬",
                        "启攒",
                        "探亲访友"
                    ]
                },
                "jiaoyunTime": {
                    "description": "1.9 交运时间",
                    "type": "string",
                    "example": "2021-01-01 12:00:00"
                },
                "loveLocation": {
                    "description": "1.7 桃花位",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "东南"
                    ]
                },
                "loveScore": {
                    "description": "1.2.3 桃花分数",
                    "type": "number",
                    "example": 75
                },
                "luckyNum": {
                    "description": "1.6 幸运数字",
                    "type": "integer",
                    "example": 8
                },
                "lunarDay": {
                    "description": "1.0 农历日期",
                    "type": "string"
                },
                "pianPropertyClock": {
                    "description": "偏财时辰",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    },
                    "example": [
                        1,
                        2,
                        3
                    ]
                },
                "pianPropertyLocation": {
                    "description": "偏财位",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "东南"
                    ]
                },
                "propertyLocation": {
                    "description": "1.7 财位",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "东南"
                    ]
                },
                "propertyScore": {
                    "description": "1.2.2 今日财运分数",
                    "type": "number",
                    "example": 75
                },
                "tenGodPower": {
                    "description": "1.2.1 流日十神能量",
                    "type": "object",
                    "additionalProperties": {
                        "type": "number"
                    }
                },
                "tenTianganPower": {
                    "description": "1.2.1 流日十天干能量",
                    "type": "object",
                    "additionalProperties": {
                        "type": "number"
                    }
                },
                "yiWhat": {
                    "description": "1.4 每日宜what",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "祭祀",
                        "打扫",
                        "破屋坏垣"
                    ]
                },
                "zhishen": {
                    "description": "1.3 每日建议（根据值神匹配）",
                    "type": "string"
                }
            }
        },
        "v1.CalendarDayRequest": {
            "type": "object",
            "properties": {
                "appID": {
                    "description": "应用：2-排盘、3-万年历、4-运势、5-论财",
                    "type": "integer",
                    "example": 3
                },
                "date": {
                    "description": "公历日期",
                    "type": "string",
                    "example": "2020-01-01"
                }
            }
        },
        "v1.CalendarDayResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/v1.CalendarDayResponseData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.CalendarDayResponseData": {
            "type": "object",
            "properties": {
                "bazi1": {
                    "description": "八字1（年份干支）",
                    "type": "string",
                    "example": "庚申"
                },
                "bazi2": {
                    "description": "八字2（月份干支）",
                    "type": "string",
                    "example": "壬午"
                },
                "bazi2Next": {
                    "description": "八字2（下个月份干支）",
                    "type": "string",
                    "example": "壬午"
                },
                "bazi3": {
                    "description": "八字3（日期干支）",
                    "type": "string",
                    "example": "辛巳"
                },
                "caiLocation": {
                    "description": "财位",
                    "type": "string",
                    "example": "东北"
                },
                "constellation": {
                    "description": "星座",
                    "type": "string",
                    "example": "双鱼座"
                },
                "date": {
                    "description": "公历日期",
                    "type": "string",
                    "example": "2099-03-12"
                },
                "festival": {
                    "description": "节日",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "北方小年",
                        "南方小年"
                    ]
                },
                "fuLocation": {
                    "description": "福位",
                    "type": "string",
                    "example": "西南"
                },
                "heidao": {
                    "description": "黑道",
                    "type": "string",
                    "example": "白虎"
                },
                "hou": {
                    "description": "七十二侯",
                    "type": "string",
                    "example": "半夏生"
                },
                "huangdao": {
                    "description": "黄道",
                    "type": "string",
                    "example": "青龙"
                },
                "ji": {
                    "description": "忌",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "阴宅破土",
                        "安葬",
                        "启攒",
                        "探亲访友"
                    ]
                },
                "jieqi": {
                    "description": "节气（今日或之前的最后一个节气）",
                    "type": "string",
                    "example": "大雪"
                },
                "jieqiDate": {
                    "description": "节气日期",
                    "type": "string",
                    "example": "2006-01-02"
                },
                "jieqiTime": {
                    "description": "节气时间",
                    "type": "string",
                    "example": "2006-01-02 15:00:59"
                },
                "jishen": {
                    "description": "吉神",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "天德合",
                        "月德合"
                    ]
                },
                "luLocation": {
                    "description": "禄位",
                    "type": "string",
                    "example": "东南"
                },
                "lunarDate": {
                    "description": "农历日期",
                    "type": "string",
                    "example": "二月廿一"
                },
                "pengzubaiji": {
                    "description": "彭祖百忌",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "乙不栽植 千株不长",
                        "未不服药 毒气入肠"
                    ]
                },
                "pengzubaijiOverview": {
                    "description": "彭祖百忌概述",
                    "type": "string",
                    "example": "猴日冲虎煞南"
                },
                "shierjianri": {
                    "description": "十二建日",
                    "type": "string",
                    "example": "定日"
                },
                "taishen": {
                    "description": "胎神",
                    "type": "string",
                    "example": "房床厕外"
                },
                "taishenLocation": {
                    "description": "胎神位置",
                    "type": "string",
                    "example": "西北"
                },
                "times": {
                    "description": "时辰（共13个，包含早子时与晚子时）",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.CalendarShichen"
                    }
                },
                "weekday": {
                    "description": "星期",
                    "type": "string",
                    "example": "星期四"
                },
                "wuxing": {
                    "description": "五行",
                    "type": "string",
                    "example": "山下火"
                },
                "xiLocation": {
                    "description": "喜位",
                    "type": "string",
                    "example": "西北"
                },
                "xingxiu": {
                    "description": "星宿",
                    "type": "string",
                    "example": "张月鹿"
                },
                "xiongshen": {
                    "description": "凶神",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "月破",
                        "大耗",
                        "四击",
                        "九空"
                    ]
                },
                "yellowYears": {
                    "description": "黄帝纪年：公元年+2697",
                    "type": "integer",
                    "example": 4721
                },
                "yellowYearsZh": {
                    "description": "黄帝纪年",
                    "type": "string",
                    "example": "四千七百二十一"
                },
                "yi": {
                    "description": "宜",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "祭祀",
                        "打扫",
                        "破屋坏垣"
                    ]
                },
                "zeri": {
                    "description": "择日",
                    "type": "string",
                    "example": "大吉"
                },
                "zodiac": {
                    "description": "生肖",
                    "type": "string",
                    "example": "鼠"
                }
            }
        },
        "v1.CalendarEachDayOfMonth": {
            "type": "object",
            "properties": {
                "bazi": {
                    "description": "八字",
                    "type": "string",
                    "example": "庚申"
                },
                "currentMonth": {
                    "description": "是否为当前月份",
                    "type": "boolean",
                    "example": true
                },
                "date": {
                    "description": "公历日期",
                    "type": "string",
                    "example": "2020-01-01"
                },
                "holidayOff": {
                    "description": "节假日调休：1休，2班",
                    "type": "integer",
                    "example": 1
                },
                "ji": {
                    "description": "忌",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "阴宅破土",
                        "安葬",
                        "启攒",
                        "探亲访友"
                    ]
                },
                "jieqi": {
                    "description": "节气",
                    "type": "string",
                    "example": "大雪"
                },
                "jieqiTime": {
                    "description": "节气时间",
                    "type": "string"
                },
                "jieri": {
                    "description": "节日",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "北方小年",
                        "南方小年"
                    ]
                },
                "liuriShensha": {
                    "description": "流日神煞",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "lunarDate": {
                    "description": "农历日期",
                    "type": "string",
                    "example": "二月廿一"
                },
                "vipShishen": {
                    "description": "VIP的干十神与支十神",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "weekday": {
                    "description": "星期",
                    "type": "string",
                    "example": "星期四"
                },
                "yi": {
                    "description": "宜",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "祭祀",
                        "打扫",
                        "破屋坏垣"
                    ]
                }
            }
        },
        "v1.CalendarMonthRequest": {
            "type": "object",
            "properties": {
                "appID": {
                    "description": "应用：2-排盘、3-万年历、4-运势、5-论财",
                    "type": "integer"
                },
                "month": {
                    "description": "公历月份",
                    "type": "string",
                    "example": "2020-01"
                }
            }
        },
        "v1.CalendarMonthResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "type": "array",
                        "items": {
                            "$ref": "#/definitions/v1.CalendarEachDayOfMonth"
                        }
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.CalendarShichen": {
            "type": "object",
            "properties": {
                "bazi": {
                    "description": "八字",
                    "type": "string",
                    "example": "丙子"
                },
                "caiLocation": {
                    "description": "财位",
                    "type": "string",
                    "example": "财神东北"
                },
                "chong": {
                    "description": "冲",
                    "type": "string",
                    "example": "虎"
                },
                "fuLocation": {
                    "description": "福位",
                    "type": "string",
                    "example": "福神西南"
                },
                "ji": {
                    "description": "忌",
                    "type": "string",
                    "example": "忌"
                },
                "jixiong": {
                    "description": "吉",
                    "type": "string",
                    "example": "吉"
                },
                "luLocation": {
                    "description": "禄位",
                    "type": "string",
                    "example": "阳贵东南"
                },
                "sha": {
                    "description": "煞",
                    "type": "string",
                    "example": "南"
                },
                "time": {
                    "description": "时辰",
                    "type": "string",
                    "example": "23:00-00:59"
                },
                "xiLocation": {
                    "description": "喜位",
                    "type": "string",
                    "example": "喜神西北"
                },
                "yi": {
                    "description": "宜",
                    "type": "string",
                    "example": "宜"
                }
            }
        },
        "v1.CheckAppUpdateRequest": {
            "type": "object",
            "required": [
                "osType",
                "versionName"
            ],
            "properties": {
                "osType": {
                    "description": "1:android, 2:ios",
                    "type": "integer"
                },
                "versionName": {
                    "description": "版本名称",
                    "type": "string"
                }
            }
        },
        "v1.CheckAppUpdateResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/v1.CheckAppUpdateResponseData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.CheckAppUpdateResponseData": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "isForceUpdate": {
                    "description": "是否强制更新",
                    "type": "boolean"
                },
                "isHotUpdate": {
                    "description": "是否热更新",
                    "type": "boolean"
                },
                "updateNote": {
                    "description": "更新说明",
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                },
                "url": {
                    "description": "下载地址",
                    "type": "string"
                },
                "versionCode": {
                    "description": "版本号",
                    "type": "integer"
                },
                "versionName": {
                    "description": "版本名称",
                    "type": "string"
                }
            }
        },
        "v1.CreateUserCountdownDayRequest": {
            "type": "object",
            "properties": {
                "isTop": {
                    "description": "是否置顶",
                    "type": "boolean",
                    "example": true
                },
                "name": {
                    "description": "名称",
                    "type": "string",
                    "example": "张三"
                },
                "remindTime": {
                    "description": "提醒时间",
                    "type": "string",
                    "example": "2021-01-01 12:00:00"
                },
                "remindType": {
                    "description": "提醒类型：0-不提醒，1-整点提醒，2-提前5分钟，3-提前10分钟，4-提前15分钟，5-提前30分钟，6-提前1小时，7-提前1天，8-提前3天，9-提前7天",
                    "type": "integer",
                    "example": 1
                },
                "repeatTime": {
                    "description": "重复时间：0-不重复，1-每年，2-每月，3-每日，4-每时，5-法定节假日重复（智能跳过工作日），6-法定工作日重复（智能跳过节假日）",
                    "type": "integer",
                    "example": 1
                },
                "type": {
                    "description": "类型：1-纪念日，2-生日，3-日程",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "v1.CreateUserCountdownDayResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/v1.CreateUserCountdownDayResponseData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.CreateUserCountdownDayResponseData": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                }
            }
        },
        "v1.CreateUserMingliRequest": {
            "type": "object",
            "required": [
                "birthtime",
                "gender",
                "name"
            ],
            "properties": {
                "address": {
                    "description": "地址",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "北京市",
                        "市辖区",
                        "东城区"
                    ]
                },
                "appID": {
                    "description": "应用：2-排盘、3-万年历、4-运势、5-论财",
                    "type": "integer",
                    "example": 2
                },
                "birthtime": {
                    "description": "出生时间（公历）",
                    "type": "string",
                    "example": "2021-01-01 12:00:00"
                },
                "gender": {
                    "description": "性别：1-男，2-女",
                    "type": "integer",
                    "example": 1
                },
                "groupID": {
                    "description": "分组ID",
                    "type": "integer",
                    "example": 1
                },
                "isDefault": {
                    "description": "是否默认",
                    "type": "boolean",
                    "example": true
                },
                "name": {
                    "description": "姓名",
                    "type": "string",
                    "example": "张三"
                }
            }
        },
        "v1.CreateUserMingliResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.DateSubFutureRequest": {
            "type": "object",
            "required": [
                "date",
                "days"
            ],
            "properties": {
                "date": {
                    "description": "日期",
                    "type": "string",
                    "example": "2021-01-01"
                },
                "days": {
                    "description": "天数",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "v1.DateSubFutureResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.DateSubPastRequest": {
            "type": "object",
            "required": [
                "date",
                "days"
            ],
            "properties": {
                "date": {
                    "description": "日期",
                    "type": "string",
                    "example": "2021-01-01"
                },
                "days": {
                    "description": "天数",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "v1.DateSubPastResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.DateSubRequest": {
            "type": "object",
            "required": [
                "date1",
                "date2"
            ],
            "properties": {
                "date1": {
                    "description": "日期1",
                    "type": "string",
                    "example": "2021-01-01"
                },
                "date2": {
                    "description": "日期2",
                    "type": "string",
                    "example": "2021-01-02"
                }
            }
        },
        "v1.DateSubResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.DeleteUserCountdownDayRequest": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "type": "integer"
                }
            }
        },
        "v1.DeleteUserMingliRequest": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "v1.DeleteUserMingliResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsJiXiongRequest": {
            "type": "object"
        },
        "v1.EnumsJiXiongResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.EnumsJiXiongResponseItem"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsJiXiongResponseItem": {
            "type": "object",
            "properties": {
                "detail": {
                    "description": "吉/凶神详情",
                    "type": "string",
                    "example": "...你不要给我哇哇叫"
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "description": "吉/凶神名称",
                    "type": "string",
                    "example": "同德合"
                }
            }
        },
        "v1.EnumsJieqiRequest": {
            "type": "object"
        },
        "v1.EnumsJieqiResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.EnumsJieqiResponseDateItem"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsJieqiResponseDateItem": {
            "type": "object",
            "properties": {
                "health": {
                    "type": "string",
                    "example": "春有百花秋有月，夏有凉风冬有雪。若无闲事挂心头，便是人间好时节。"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "type": "string",
                    "example": "立春"
                },
                "season": {
                    "type": "string",
                    "example": "春"
                },
                "url": {
                    "type": "string",
                    "example": "https://mp.weixin.qq.com/s/Am_Myb-K04M5REtnkmEuuQ"
                }
            }
        },
        "v1.EnumsLocationRequest": {
            "type": "object",
            "properties": {
                "overseas": {
                    "description": "是否海外",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "v1.EnumsLocationResponse": {
            "type": "object",
            "properties": {
                "EnumsLocationResponseData": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.LocationTree"
                    }
                },
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsLunarRequest": {
            "type": "object",
            "required": [
                "year"
            ],
            "properties": {
                "year": {
                    "description": "年份",
                    "type": "string",
                    "example": "2020"
                }
            }
        },
        "v1.EnumsLunarResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.EnumsLunarResponseItem"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsLunarResponseItem": {
            "type": "object",
            "properties": {
                "days": {
                    "description": "日期",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.LunarDay"
                    }
                },
                "month": {
                    "description": "月份",
                    "type": "string",
                    "example": "正月"
                }
            }
        },
        "v1.EnumsNayinRequest": {
            "type": "object"
        },
        "v1.EnumsNayinResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.EnumsNayinResponseItem"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsNayinResponseItem": {
            "type": "object",
            "properties": {
                "hanyi": {
                    "description": "含义",
                    "type": "string",
                    "example": "..."
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "description": "纳音名称",
                    "type": "string",
                    "example": "盖中王"
                },
                "wuxing": {
                    "description": "五行",
                    "type": "string",
                    "example": "木"
                },
                "zhu1": {
                    "description": "纳音名称",
                    "type": "string",
                    "example": "甲"
                },
                "zhu2": {
                    "description": "纳音名称",
                    "type": "string",
                    "example": "子"
                }
            }
        },
        "v1.EnumsPengzubaijiRequest": {
            "type": "object"
        },
        "v1.EnumsPengzubaijiResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.EnumsPengzubaijiResponseItem"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsPengzubaijiResponseItem": {
            "type": "object",
            "properties": {
                "detail": {
                    "description": "彭祖百忌详情",
                    "type": "string",
                    "example": "..."
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "description": "彭祖百忌名称",
                    "type": "string",
                    "example": "小呆比 一比雕凿"
                }
            }
        },
        "v1.EnumsShierjianriRequest": {
            "type": "object"
        },
        "v1.EnumsShierjianriResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.EnumsShierjianriResponseItem"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsShierjianriResponseItem": {
            "type": "object",
            "properties": {
                "alias": {
                    "description": "十二建日别名",
                    "type": "string",
                    "example": "建寅"
                },
                "detail": {
                    "description": "十二建日详情",
                    "type": "string",
                    "example": "..."
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "description": "十二建日名称",
                    "type": "string",
                    "example": "建寅"
                }
            }
        },
        "v1.EnumsTimeRequest": {
            "type": "object"
        },
        "v1.EnumsTimeResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.EnumsTimeResponseItem"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsTimeResponseItem": {
            "type": "object",
            "properties": {
                "alias": {
                    "description": "时辰别名",
                    "type": "string",
                    "example": "夜半"
                },
                "dangling": {
                    "description": "经络",
                    "type": "string",
                    "example": "胆经"
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                },
                "ji": {
                    "description": "忌",
                    "type": "string",
                    "example": "忌"
                },
                "jingmai": {
                    "description": "经脉",
                    "type": "string",
                    "example": "足少阳"
                },
                "name": {
                    "description": "时辰名称",
                    "type": "string",
                    "example": "子时"
                },
                "time": {
                    "description": "时辰时间",
                    "type": "string",
                    "example": "23:00-0:59"
                },
                "yi": {
                    "description": "宜",
                    "type": "string",
                    "example": "宜"
                }
            }
        },
        "v1.EnumsWuxingRequest": {
            "type": "object"
        },
        "v1.EnumsWuxingResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.EnumsWuxingResponseItem"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsWuxingResponseItem": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "description": "五行名称",
                    "type": "string",
                    "example": "金"
                },
                "position": {
                    "description": "方位",
                    "type": "string"
                },
                "profession": {
                    "description": "职业",
                    "type": "string"
                },
                "season": {
                    "description": "季节",
                    "type": "string"
                },
                "wuxing": {
                    "description": "五行",
                    "type": "string"
                }
            }
        },
        "v1.EnumsYijiRequest": {
            "type": "object"
        },
        "v1.EnumsYijiResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.EnumsYijiResponseItem"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsYijiResponseItem": {
            "type": "object",
            "properties": {
                "detail": {
                    "description": "宜忌详情",
                    "type": "string",
                    "example": "祭祀神佛，祈福祈福"
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "description": "宜忌名称",
                    "type": "string",
                    "example": "祭祀"
                }
            }
        },
        "v1.EnumsZhishenRequest": {
            "type": "object"
        },
        "v1.EnumsZhishenResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.EnumsZhishenResponseItem"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsZhishenResponseItem": {
            "type": "object",
            "properties": {
                "daily": {
                    "description": "日值神",
                    "type": "string",
                    "example": "..."
                },
                "detail": {
                    "description": "值神详情",
                    "type": "string",
                    "example": "..."
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "description": "值神名称",
                    "type": "string",
                    "example": "青龙"
                },
                "symbol": {
                    "description": "值神符号",
                    "type": "string",
                    "example": "青龙"
                },
                "type": {
                    "description": "值神类型：黄道/黑道",
                    "type": "string",
                    "example": "黄道"
                }
            }
        },
        "v1.GetDatetimeBySizhuRequest": {
            "type": "object",
            "properties": {
                "endYear": {
                    "description": "默认2099",
                    "type": "integer",
                    "example": 2099
                },
                "sizhu": {
                    "description": "四柱",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "癸亥",
                        "戊午",
                        "壬寅",
                        "己酉"
                    ]
                },
                "startYear": {
                    "description": "默认1801",
                    "type": "integer",
                    "example": 1801
                }
            }
        },
        "v1.GetDatetimeBySizhuResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.GetHistoryDayEventsRequest": {
            "type": "object",
            "required": [
                "date"
            ],
            "properties": {
                "date": {
                    "description": "日期",
                    "type": "string",
                    "example": "2006-01-02"
                }
            }
        },
        "v1.GetHistoryDayEventsResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "description": "数据",
                    "allOf": [
                        {
                            "$ref": "#/definitions/v1.GetHistoryDayEventsResponseData"
                        }
                    ]
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.GetHistoryDayEventsResponseData": {
            "type": "object",
            "properties": {
                "actions": {
                    "description": "活动",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.GetHistoryDayEventsResponseItem"
                    }
                },
                "events": {
                    "description": "事件",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.GetHistoryDayEventsResponseItem"
                    }
                }
            }
        },
        "v1.GetHistoryDayEventsResponseItem": {
            "type": "object",
            "properties": {
                "ad": {
                    "description": "公元后",
                    "type": "boolean",
                    "example": true
                },
                "content": {
                    "description": "内容",
                    "type": "string",
                    "example": "****************"
                },
                "date": {
                    "description": "日期",
                    "type": "string",
                    "example": "2006-01-02"
                },
                "keyword": {
                    "description": "关键词",
                    "type": "string",
                    "example": "大学生爱国运D"
                },
                "title": {
                    "description": "标题",
                    "type": "string",
                    "example": "大学生爱国运D"
                },
                "type": {
                    "description": "类型",
                    "type": "string",
                    "example": "大事记"
                }
            }
        },
        "v1.GetJiaoyunRequest": {
            "type": "object",
            "required": [
                "birthtime",
                "gender"
            ],
            "properties": {
                "birthplace": {
                    "description": "出生地",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "birthtime": {
                    "description": "生日",
                    "type": "string",
                    "example": "2006-01-02 15:04:05"
                },
                "gender": {
                    "type": "string",
                    "enum": [
                        "男",
                        "女"
                    ],
                    "example": "男"
                }
            }
        },
        "v1.GetJiaoyunResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/v1.GetJiaoyunResponseData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.GetJiaoyunResponseData": {
            "type": "object",
            "properties": {
                "jiaoyunTime": {
                    "description": "交运时间",
                    "type": "string",
                    "example": "2021-01-01 12:00:00"
                }
            }
        },
        "v1.JiemengRequest": {
            "type": "object",
            "required": [
                "pageNum",
                "pageSize"
            ],
            "properties": {
                "pageNum": {
                    "type": "integer",
                    "minimum": 1,
                    "example": 1
                },
                "pageSize": {
                    "type": "integer",
                    "minimum": 1,
                    "example": 10
                },
                "param": {
                    "$ref": "#/definitions/v1.JiemengRequestParam"
                }
            }
        },
        "v1.JiemengRequestParam": {
            "type": "object",
            "properties": {
                "content": {
                    "description": "梦境内容（非必须）",
                    "type": "string",
                    "example": "梦见大海（非必须）"
                }
            }
        },
        "v1.JiemengResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/v1.JiemengResponseData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.JiemengResponseData": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.JiemengResponseDataItem"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "v1.JiemengResponseDataItem": {
            "type": "object",
            "properties": {
                "content": {
                    "description": "内容",
                    "type": "string",
                    "example": "梦见大海（内容）"
                },
                "title": {
                    "description": "标题",
                    "type": "string",
                    "example": "梦见大海（标题）"
                }
            }
        },
        "v1.ListUserCountdownDayRequest": {
            "type": "object"
        },
        "v1.ListUserCountdownDayResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.UserCountdownDay"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.ListUserMingliRequest": {
            "type": "object",
            "properties": {
                "appID": {
                    "type": "integer"
                },
                "groupID": {
                    "type": "integer"
                }
            }
        },
        "v1.ListUserMingliResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.UserMingli"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.LocationTree": {
            "type": "object",
            "properties": {
                "children": {
                    "description": "子节点，省包含市，市包含区",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.LocationTree"
                    }
                },
                "code": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "v1.LunarDay": {
            "type": "object",
            "properties": {
                "date": {
                    "description": "日期",
                    "type": "string",
                    "example": "2020-01-01"
                },
                "lunarDate": {
                    "description": "农历日期",
                    "type": "string",
                    "example": "正月初一"
                },
                "name": {
                    "description": "名称",
                    "type": "string",
                    "example": "初一"
                }
            }
        },
        "v1.MingliDayunliulianScoreRequest": {
            "type": "object",
            "properties": {
                "mingliID": {
                    "description": "命例ID",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "v1.MingliDayunliulianScoreResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/v1.MingliDayunliulianScoreResponseData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.MingliDayunliulianScoreResponseData": {
            "type": "object",
            "properties": {
                "Dyun": {
                    "description": "大运分（移除末尾0值，每两个一组，共二十四组）",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "Lnian": {
                    "description": "流年分（每五个一组，共二十四组，每两组与大运分的一组对应）",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "Xxian": {
                    "description": "小运分（每一个小运年对应一个分数）",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "Zscore": {
                    "description": "综合分（移除末尾0值）",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "v1.MingliJieqiScoreRequest": {
            "type": "object",
            "properties": {
                "mingliID": {
                    "description": "命例ID",
                    "type": "integer",
                    "example": 1
                },
                "time": {
                    "description": "时间",
                    "type": "string",
                    "example": "2021-01-01 12:00:00"
                }
            }
        },
        "v1.MingliJieqiScoreResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/v1.MingliJieqiScoreResponseData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.MingliJieqiScoreResponseData": {
            "type": "object",
            "properties": {
                "DayGanzhiList": {
                    "description": "日干支列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "dayFen": {
                    "description": "日分",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "dayNum": {
                    "description": "日数（两个节气交替日+中间的天数）",
                    "type": "integer"
                },
                "jqBegin": {
                    "description": "节气开始时间（节气交替时的半天记作一天）",
                    "type": "string"
                },
                "jqEnd": {
                    "description": "节气结束时间（节气交替时的半天记作一天）",
                    "type": "string"
                },
                "monthGanzhiList": {
                    "description": "月干支列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "nowMonthFen": {
                    "description": "当前月分",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "paiYueJq": {
                    "description": "排月节气",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "v1.PaipanRequest": {
            "type": "object",
            "properties": {
                "mingliID": {
                    "description": "命例ID",
                    "type": "integer",
                    "example": 1
                },
                "time": {
                    "description": "时间",
                    "type": "string",
                    "example": "2021-01-01 12:00:00"
                }
            }
        },
        "v1.PaipanResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/v1.PaipanResponseData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.PaipanResponseData": {
            "type": "object",
            "properties": {
                "benqi": {
                    "description": "本气",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "benqiShishen": {
                    "description": "本气十神",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "dizhi": {
                    "description": "地支",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "kongwang": {
                    "description": "空亡",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "nayin": {
                    "description": "纳音",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "shenShaJiShen": {
                    "description": "神煞",
                    "type": "array",
                    "items": {
                        "type": "array",
                        "items": {
                            "type": "string"
                        }
                    }
                },
                "tiangan": {
                    "description": "天干",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "xingyun": {
                    "description": "星运",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "yuqi": {
                    "description": "余气",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "yuqiShishen": {
                    "description": "余气十神",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "zhongqi": {
                    "description": "中气",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "zhongqiShishen": {
                    "description": "中气十神",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "zhuxing": {
                    "description": "主星",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "zizuo": {
                    "description": "自坐",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "v1.SetDefaultUserMingliRequest": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "v1.SetDefaultUserMingliResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.SetMingliWuxingRequest": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                },
                "wuxing": {
                    "description": "五行：用神,喜神,忌神,仇神,闲神",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "v1.SetMingliWuxingResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.UpdateUserCountdownDayRequest": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                },
                "isTop": {
                    "description": "是否置顶",
                    "type": "boolean",
                    "example": true
                },
                "name": {
                    "description": "名称",
                    "type": "string",
                    "example": "张三"
                },
                "remindTime": {
                    "description": "提醒时间",
                    "type": "string",
                    "example": "2021-01-01 12:00:00"
                },
                "remindType": {
                    "description": "提醒类型：0-不提醒，1-整点提醒，2-提前5分钟，3-提前10分钟，4-提前15分钟，5-提前30分钟，6-提前1小时，7-提前1天，8-提前3天，9-提前7天",
                    "type": "integer",
                    "example": 1
                },
                "repeatTime": {
                    "description": "重复时间：0-不重复，1-每年，2-每月，3-每日，4-每时，5-法定节假日重复（智能跳过工作日），6-法定工作日重复（智能跳过节假日）",
                    "type": "integer",
                    "example": 1
                },
                "type": {
                    "description": "类型：1-纪念日，2-生日，3-日程",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "v1.UpdateUserMingliRequest": {
            "type": "object",
            "required": [
                "birthtime",
                "gender",
                "id",
                "name"
            ],
            "properties": {
                "address": {
                    "description": "地址",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "[\"北京市\"",
                        "\"市辖区\"",
                        "\"东城区\"]"
                    ]
                },
                "birthtime": {
                    "description": "出生时间（公历）",
                    "type": "string",
                    "example": "2021-01-01 12:00:00"
                },
                "gender": {
                    "description": "性别：1-男，2-女",
                    "type": "integer",
                    "example": 1
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                },
                "isDefault": {
                    "description": "是否默认",
                    "type": "boolean",
                    "example": true
                },
                "name": {
                    "description": "姓名",
                    "type": "string",
                    "example": "张三"
                }
            }
        },
        "v1.UpdateUserMingliResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.UserCountdownDay": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                },
                "isTop": {
                    "description": "是否置顶",
                    "type": "boolean",
                    "example": true
                },
                "name": {
                    "description": "名称",
                    "type": "string",
                    "example": "张三"
                },
                "remindTime": {
                    "description": "提醒时间",
                    "type": "string",
                    "example": "2021-01-01 12:00:00"
                },
                "remindType": {
                    "description": "提醒类型：0-不提醒，1-整点提醒，2-提前5分钟，3-提前10分钟，4-提前15分钟，5-提前30分钟，6-提前1小时，7-提前1天，8-提前3天，9-提前7天",
                    "type": "integer",
                    "example": 1
                },
                "repeatTime": {
                    "description": "重复时间：0-不重复，1-每年，2-每月，3-每日，4-每时，5-法定节假日重复（智能跳过工作日），6-法定工作日重复（智能跳过节假日）",
                    "type": "integer",
                    "example": 1
                },
                "type": {
                    "description": "类型：1-纪念日，2-生日，3-日程",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "v1.UserMingli": {
            "type": "object",
            "properties": {
                "address": {
                    "description": "地址",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "北京市",
                        "市辖区",
                        "东城区"
                    ]
                },
                "appID": {
                    "description": "应用：2-排盘、3-万年历、4-运势、5-论财",
                    "type": "integer",
                    "example": 2
                },
                "appName": {
                    "description": "应用名称",
                    "type": "string"
                },
                "bazi": {
                    "description": "八字：年份干支,月份干支,日期干支,时辰干支",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "birthplace": {
                    "description": "出生地",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "[\"北京市\"",
                        "\"市辖区\"",
                        "\"东城区\"]"
                    ]
                },
                "birthtime": {
                    "description": "出生时间（公历）",
                    "type": "string",
                    "example": "2021-01-01 12:00:00"
                },
                "birthtimeLunar": {
                    "description": "出生时间（农历）",
                    "type": "string",
                    "example": "2021年闰四月十一子时"
                },
                "birthtimeSun": {
                    "description": "真太阳时",
                    "type": "string",
                    "example": "2021-01-01 12:00:00"
                },
                "dayun": {
                    "description": "大运",
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.UserMingliDayun"
                        }
                    ]
                },
                "gender": {
                    "description": "性别：1-男，2-女",
                    "type": "integer",
                    "example": 1
                },
                "groupID": {
                    "description": "分组ID",
                    "type": "integer",
                    "example": 1
                },
                "groupName": {
                    "description": "分组名称",
                    "type": "string"
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "example": 1
                },
                "isDefault": {
                    "description": "是否默认",
                    "type": "boolean",
                    "example": true
                },
                "lunarBirthtime": {
                    "description": "出生时间（农历）",
                    "type": "string",
                    "example": "2021年闰四月十一子时"
                },
                "name": {
                    "description": "姓名",
                    "type": "string",
                    "example": "张三"
                },
                "userID": {
                    "description": "用户ID",
                    "type": "string",
                    "example": "1"
                },
                "wuxing": {
                    "description": "五行：用神,喜神,忌神,仇神,闲神",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "xiaoyun": {
                    "description": "小运",
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.UserMingliXiaoyun"
                        }
                    ]
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfocalendar holds exported Swagger Info so clients can modify it
var SwaggerInfocalendar = &swag.Spec{
	Version:          "1.0.0",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "万年历",
	Description:      "This is a http server template.",
	InfoInstanceName: "calendar",
	SwaggerTemplate:  docTemplatecalendar,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfocalendar.InstanceName(), SwaggerInfocalendar)
}
