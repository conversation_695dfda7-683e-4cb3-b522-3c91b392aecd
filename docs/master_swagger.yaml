definitions:
  corona.PaipanShishenPowerItem:
    properties:
      attr:
        description: 五行
        type: string
      cangNum:
        description: 藏干数量
        type: integer
      liliang:
        description: 力量
        type: integer
      num:
        description: 五行数量
        type: integer
      power:
        description: 能量占比数组
        items:
          type: number
        type: array
      powerArr:
        description: 能量数组
        items:
          type: integer
        type: array
      shiShen:
        description: 十神数组
        items:
          type: string
        type: array
      shiShenName:
        description: 十神合称
        type: string
      tianganArr:
        description: 天干数组
        items:
          type: string
        type: array
      totalBfb:
        description: 总能量占比
        type: number
      totalPower:
        description: 总能量
        type: integer
    type: object
  model.BirthDayFortune:
    properties:
      birthDay:
        type: string
      fortuneText:
        type: string
    type: object
  model.BirthDayHour:
    properties:
      basis:
        type: string
      conclusion:
        type: string
      rizhu:
        type: string
      shizhu:
        type: string
    type: object
  model.BirthHourFortune:
    properties:
      birthHour:
        type: string
      criticalYears:
        type: string
      fortuneText:
        type: string
      suitableCareer:
        type: string
      timeRange:
        type: string
    type: object
  model.BirthHourRizhu:
    properties:
      basis:
        type: string
      birthHour:
        type: string
      conclusion:
        type: string
      rizhu:
        type: string
    type: object
  model.BirthMonthFortune:
    properties:
      birthMonth:
        type: string
      conceptionMonth:
        type: string
      endDate:
        type: string
      fortuneText:
        type: string
      solarTerm:
        type: string
      startDate:
        type: string
    type: object
  model.BirthMonthRizhu:
    properties:
      birthMonth:
        type: string
      explanation:
        type: string
      god:
        type: string
      rizhu:
        type: string
    type: object
  model.BirthYearFortune:
    properties:
      basis:
        type: string
      birthYear:
        type: string
      femaleFortune:
        type: string
      maleFortune:
        type: string
      wuxing:
        type: string
      zodiac:
        type: string
      zodiacDetail:
        type: string
    type: object
  model.Guaxiang:
    properties:
      duanri:
        description: 断日
        type: string
      gejue:
        description: 歌诀
        type: string
      guaji:
        description: 卦吉
        type: string
      id:
        description: 主键ID
        type: integer
      idiom:
        description: 成语
        type: string
      jieshi:
        description: 解释
        type: string
      name:
        description: 卦名
        type: string
      topdown:
        description: 上下卦
        type: string
      value:
        description: 卦值
        type: integer
      yao1:
        description: 爻1
        type: integer
      yao2:
        description: 爻2
        type: integer
      yao3:
        description: 爻3
        type: integer
      yao4:
        description: 爻4
        type: integer
      yao5:
        description: 爻5
        type: integer
      yao6:
        description: 爻6
        type: integer
      yaoValue:
        description: 爻值
        type: integer
    type: object
  model.Minggong:
    properties:
      constellation:
        type: string
      createdAt:
        description: 创建时间
        type: string
      development:
        type: string
      name:
        type: string
      star:
        type: string
      summary:
        type: string
      temperament:
        type: string
      updatedAt:
        description: 更新时间
        type: string
    type: object
  v1.CalendarDayRequest:
    properties:
      appID:
        description: 应用：2-排盘、3-万年历、4-运势、5-论财
        example: 3
        type: integer
      date:
        description: 公历日期
        example: "2020-01-01"
        type: string
    type: object
  v1.CalendarDayResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CalendarDayResponseData'
      message:
        type: string
    type: object
  v1.CalendarDayResponseData:
    properties:
      bazi1:
        description: 八字1（年份干支）
        example: 庚申
        type: string
      bazi2:
        description: 八字2（月份干支）
        example: 壬午
        type: string
      bazi2Next:
        description: 八字2（下个月份干支）
        example: 壬午
        type: string
      bazi3:
        description: 八字3（日期干支）
        example: 辛巳
        type: string
      caiLocation:
        description: 财位
        example: 东北
        type: string
      constellation:
        description: 星座
        example: 双鱼座
        type: string
      date:
        description: 公历日期
        example: "2099-03-12"
        type: string
      festival:
        description: 节日
        example:
        - 北方小年
        - 南方小年
        items:
          type: string
        type: array
      fuLocation:
        description: 福位
        example: 西南
        type: string
      heidao:
        description: 黑道
        example: 白虎
        type: string
      hou:
        description: 七十二侯
        example: 半夏生
        type: string
      huangdao:
        description: 黄道
        example: 青龙
        type: string
      ji:
        description: 忌
        example:
        - 阴宅破土
        - 安葬
        - 启攒
        - 探亲访友
        items:
          type: string
        type: array
      jieqi:
        description: 节气（今日或之前的最后一个节气）
        example: 大雪
        type: string
      jieqiDate:
        description: 节气日期
        example: "2006-01-02"
        type: string
      jieqiTime:
        description: 节气时间
        example: "2006-01-02 15:00:59"
        type: string
      jishen:
        description: 吉神
        example:
        - 天德合
        - 月德合
        items:
          type: string
        type: array
      luLocation:
        description: 禄位
        example: 东南
        type: string
      lunarDate:
        description: 农历日期
        example: 二月廿一
        type: string
      pengzubaiji:
        description: 彭祖百忌
        example:
        - 乙不栽植 千株不长
        - 未不服药 毒气入肠
        items:
          type: string
        type: array
      pengzubaijiOverview:
        description: 彭祖百忌概述
        example: 猴日冲虎煞南
        type: string
      shierjianri:
        description: 十二建日
        example: 定日
        type: string
      taishen:
        description: 胎神
        example: 房床厕外
        type: string
      taishenLocation:
        description: 胎神位置
        example: 西北
        type: string
      times:
        description: 时辰（共13个，包含早子时与晚子时）
        items:
          $ref: '#/definitions/v1.CalendarShichen'
        type: array
      weekday:
        description: 星期
        example: 星期四
        type: string
      wuxing:
        description: 五行
        example: 山下火
        type: string
      xiLocation:
        description: 喜位
        example: 西北
        type: string
      xingxiu:
        description: 星宿
        example: 张月鹿
        type: string
      xiongshen:
        description: 凶神
        example:
        - 月破
        - 大耗
        - 四击
        - 九空
        items:
          type: string
        type: array
      yellowYears:
        description: 黄帝纪年：公元年+2697
        example: 4721
        type: integer
      yellowYearsZh:
        description: 黄帝纪年
        example: 四千七百二十一
        type: string
      yi:
        description: 宜
        example:
        - 祭祀
        - 打扫
        - 破屋坏垣
        items:
          type: string
        type: array
      zeri:
        description: 择日
        example: 大吉
        type: string
      zodiac:
        description: 生肖
        example: 鼠
        type: string
    type: object
  v1.CalendarEachDayOfMonth:
    properties:
      bazi:
        description: 八字
        example: 庚申
        type: string
      currentMonth:
        description: 是否为当前月份
        example: true
        type: boolean
      date:
        description: 公历日期
        example: "2020-01-01"
        type: string
      holidayOff:
        description: 节假日调休：1休，2班
        example: 1
        type: integer
      ji:
        description: 忌
        example:
        - 阴宅破土
        - 安葬
        - 启攒
        - 探亲访友
        items:
          type: string
        type: array
      jieqi:
        description: 节气
        example: 大雪
        type: string
      jieqiTime:
        description: 节气时间
        type: string
      jieri:
        description: 节日
        example:
        - 北方小年
        - 南方小年
        items:
          type: string
        type: array
      liuriShensha:
        description: 流日神煞
        items:
          type: string
        type: array
      lunarDate:
        description: 农历日期
        example: 二月廿一
        type: string
      vipShishen:
        description: VIP的干十神与支十神
        items:
          type: string
        type: array
      weekday:
        description: 星期
        example: 星期四
        type: string
      yi:
        description: 宜
        example:
        - 祭祀
        - 打扫
        - 破屋坏垣
        items:
          type: string
        type: array
    type: object
  v1.CalendarMonthRequest:
    properties:
      appID:
        description: 应用：2-排盘、3-万年历、4-运势、5-论财
        type: integer
      month:
        description: 公历月份
        example: 2020-01
        type: string
    type: object
  v1.CalendarMonthResponse:
    properties:
      code:
        type: integer
      data:
        items:
          items:
            $ref: '#/definitions/v1.CalendarEachDayOfMonth'
          type: array
        type: array
      message:
        type: string
    type: object
  v1.CalendarShichen:
    properties:
      bazi:
        description: 八字
        example: 丙子
        type: string
      caiLocation:
        description: 财位
        example: 财神东北
        type: string
      chong:
        description: 冲
        example: 虎
        type: string
      fuLocation:
        description: 福位
        example: 福神西南
        type: string
      ji:
        description: 忌
        example: 忌
        type: string
      jixiong:
        description: 吉
        example: 吉
        type: string
      luLocation:
        description: 禄位
        example: 阳贵东南
        type: string
      sha:
        description: 煞
        example: 南
        type: string
      time:
        description: 时辰
        example: 23:00-00:59
        type: string
      xiLocation:
        description: 喜位
        example: 喜神西北
        type: string
      yi:
        description: 宜
        example: 宜
        type: string
    type: object
  v1.CheckAppUpdateRequest:
    properties:
      osType:
        description: 1:android, 2:ios
        type: integer
      versionName:
        description: 版本名称
        type: string
    required:
    - osType
    - versionName
    type: object
  v1.CheckAppUpdateResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CheckAppUpdateResponseData'
      message:
        type: string
    type: object
  v1.CheckAppUpdateResponseData:
    properties:
      createdAt:
        description: 创建时间
        type: string
      isForceUpdate:
        description: 是否强制更新
        type: boolean
      isHotUpdate:
        description: 是否热更新
        type: boolean
      updateNote:
        description: 更新说明
        type: string
      updatedAt:
        description: 更新时间
        type: string
      url:
        description: 下载地址
        type: string
      versionCode:
        description: 版本号
        type: integer
      versionName:
        description: 版本名称
        type: string
    type: object
  v1.DayunAnalysisRequest:
    properties:
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 出生时间
        example: "2020-01-01 12:00:00"
        type: string
      dayun:
        description: 所选大运
        type: string
      gender:
        description: 性别：1-男，2-女
        example: 1
        type: integer
    required:
    - birthtime
    - dayun
    - gender
    type: object
  v1.DayunAnalysisResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.DayunAnalysisResponseData'
      message:
        type: string
    type: object
  v1.DayunAnalysisResponseData:
    properties:
      current:
        allOf:
        - $ref: '#/definitions/v1.MasterDayunAnalysisCurrent'
        description: 当前大运
      hehua:
        allOf:
        - $ref: '#/definitions/v1.MasterDayunAnalysisHehua'
        description: 大运合化
      overview:
        allOf:
        - $ref: '#/definitions/v1.MasterDayunAnalysisOverview'
        description: 大运概述
      shensha:
        description: 大运神煞
        items:
          type: string
        type: array
      vernacular:
        allOf:
        - $ref: '#/definitions/v1.MasterDayunVernacular'
        description: 大运白话
      xiyong:
        description: 大运喜用
        type: string
    type: object
  v1.DayunliunianRequest:
    properties:
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 出生时间
        example: "2020-01-01 12:00:00"
        type: string
      gender:
        description: 性别：1-男，2-女
        example: 1
        type: integer
    required:
    - birthtime
    - gender
    type: object
  v1.DayunliunianResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.DayunliunianResponseData'
      message:
        type: string
    type: object
  v1.DayunliunianResponseData:
    properties:
      birthtime:
        description: 出生时间（太阳时）
        type: string
      birthtimeLunar:
        description: 出生时间（农历）
        type: string
      dayunGanzhiList:
        description: 大运干支列表：12个
        items:
          type: string
        type: array
      dayunJiezhi:
        description: 大运结束年份（农历）
        type: integer
      dayunLiunianGanzhiList:
        description: 大运流年干支列表：120个
        items:
          type: string
        type: array
      dayunQishi:
        description: 大运起始年份（农历）
        type: integer
      dayunScoreList:
        description: 大运评分：24个，每两个组成一个大运
        items:
          type: integer
        type: array
      jiaoyunTime:
        description: 交运时间（公历）
        type: string
      liunianScoreList:
        description: 流年评分：n+120个，小限流年n+大运流年120
        items:
          type: integer
        type: array
      xiaoxianScoreList:
        description: 小运评分：n个
        items:
          type: integer
        type: array
      xiaoyunGanzhiList:
        description: 小运干支列表：n个
        items:
          type: string
        type: array
      xiaoyunJiezhi:
        description: 小运结束年份（农历）
        type: integer
      xiaoyunLiunianGanzhiList:
        description: 小运流年干支列表：n个
        items:
          type: string
        type: array
      xiaoyunQishi:
        description: 小运起始年份（农历）
        type: integer
      xiaoyunYears:
        description: 小运年数
        type: integer
      zongheScoreList:
        description: 综合评分：n+120个，小限流年n+大运流年120
        items:
          type: integer
        type: array
    type: object
  v1.DeleteHepanRequest:
    properties:
      ids:
        description: ID：空值则全部清空。
        items:
          type: integer
        type: array
    type: object
  v1.DeleteHepanResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.EnumsDizhiResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsDizhiResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsDizhiResponseItem:
    properties:
      dizhi:
        description: 地支
        type: string
      id:
        example: 1
        type: integer
      jieqi:
        description: 节气
        type: string
      name:
        example: 子
        type: string
      shichen:
        description: 时辰
        type: string
      shuxiang:
        description: 属相
        type: string
      wuxing:
        description: 五行
        type: string
      yinyang:
        description: 阴阳
        type: string
      yuefen:
        description: 月份
        type: string
      zhongqi:
        description: 中气
        type: string
    type: object
  v1.EnumsLocationRequest:
    properties:
      overseas:
        description: 是否海外
        example: false
        type: boolean
    type: object
  v1.EnumsLocationResponse:
    properties:
      EnumsLocationResponseData:
        items:
          $ref: '#/definitions/v1.LocationTree'
        type: array
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.EnumsLunarRequest:
    properties:
      year:
        description: 年份
        example: "2020"
        type: string
    required:
    - year
    type: object
  v1.EnumsLunarResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsLunarResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsLunarResponseItem:
    properties:
      days:
        description: 日期
        items:
          $ref: '#/definitions/v1.LunarDay'
        type: array
      month:
        description: 月份
        example: 正月
        type: string
    type: object
  v1.EnumsShishenPropertyItem:
    properties:
      ability:
        description: 能力
        type: string
      bias:
        description: 偏向
        type: string
      category:
        description: 十神类型
        type: string
      insufficient:
        description: 不足
        type: string
      overview:
        description: 概述
        type: string
      preferredRegion:
        description: 适合地区
        type: string
      profession:
        description: 职业
        type: string
      superiority:
        description: 擅长
        type: string
      type:
        description: 类型
        type: string
      wealthBy:
        description: 财富来源
        type: string
    type: object
  v1.EnumsShishenPropertyResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsShishenPropertyItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsTianganResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsTianganResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsTianganResponseItem:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: 甲
        type: string
      tiangan:
        description: 天干
        type: string
      wuxing:
        description: 五行
        type: string
      yinyang:
        description: 阴阳
        type: string
    type: object
  v1.EnumsWuxingResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsWuxingResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsWuxingResponseItem:
    properties:
      id:
        description: ID
        example: 1
        type: integer
      name:
        description: 五行名称
        example: 金
        type: string
      position:
        description: 方位
        type: string
      profession:
        description: 职业
        type: string
      season:
        description: 季节
        type: string
      wuxing:
        description: 五行
        type: string
    type: object
  v1.GaoKaoPreferenceRequest:
    properties:
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 生日
        example: "2006-01-02 15:04:05"
        type: string
      endTime:
        description: 选择时间结束
        example: "2025-07-07 18:00:00"
        type: string
      gender:
        description: 性别
        enum:
        - 男
        - 女
        example: 男
        type: string
      name:
        description: 姓名
        example: 张三
        type: string
      startTime:
        description: 选择时间开始
        example: "2025-07-07 08:00:00"
        type: string
    required:
    - birthtime
    - gender
    type: object
  v1.GaoKaoPreferenceResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.GaoKaoPreferenceResponseData'
      message:
        type: string
    type: object
  v1.GaoKaoPreferenceResponseData:
    properties:
      systemSay:
        description: 论玄说
        type: string
      systemTip:
        description: 默认提示
        type: string
      timeRanges:
        description: 时间段（开始时间与结束时间）
        items:
          items:
            type: string
          type: array
        type: array
      times:
        description: 小时序号
        items:
          type: integer
        type: array
    type: object
  v1.GaoKaoRequest:
    properties:
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 生日
        example: "2006-01-02 15:04:05"
        type: string
      gender:
        description: 性别
        enum:
        - 男
        - 女
        example: 男
        type: string
      name:
        description: 姓名
        example: 张三
        type: string
      startYear:
        description: 开始年份
        example: 2025
        type: integer
    required:
    - birthtime
    - gender
    - startYear
    type: object
  v1.GaoKaoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.GaoKaoResponseData'
      message:
        type: string
    type: object
  v1.GaoKaoResponseData:
    properties:
      basicInfo:
        allOf:
        - $ref: '#/definitions/v1.GaoKaoResponseDataBasicInfo'
        description: 基本信息
      favorableChoice:
        allOf:
        - $ref: '#/definitions/v1.GaoKaoResponseDataFavorableChoice'
        description: 有利选择
      futureDayun:
        allOf:
        - $ref: '#/definitions/v1.GaokaoResponseDataFutureDayun'
        description: 未来大运
      shishenTianfu:
        allOf:
        - $ref: '#/definitions/v1.GaoKaoResponseDataShishenTianfu'
        description: 十神天赋-看方向
      startYear:
        description: 开始年份
        example: 2025
        type: integer
      wuxingXiyong:
        allOf:
        - $ref: '#/definitions/v1.GaoKaoResponseDataWuxingXiyong'
        description: 五行喜用-选行业
    type: object
  v1.GaoKaoResponseDataBasicInfo:
    properties:
      bazi:
        description: 八字（四柱）
        items:
          type: string
        type: array
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 出生时间（用户输入）
        type: string
      birthtimeLunar:
        description: 出生时间农历（真太阳时）
        type: string
      birthtimeSolar:
        description: 出生时间公历（真太阳时）
        type: string
      gender:
        description: 性别
        type: string
      name:
        description: 姓名
        type: string
      power:
        description: 五行/十神/十天干能量
        items:
          $ref: '#/definitions/corona.PaipanShishenPowerItem'
        type: array
      xiyong:
        description: 五行喜用：用喜仇忌闲
        items:
          type: string
        type: array
    type: object
  v1.GaoKaoResponseDataFavorableChoice:
    properties:
      choiceArea:
        description: 选择区域
        type: string
      choiceDirection:
        description: 选择方向
        type: string
      systemSay:
        description: 论玄说
        type: string
      systemTip:
        description: 默认提示
        type: string
    type: object
  v1.GaoKaoResponseDataFutureDayunStage:
    properties:
      endYear:
        description: 结束年份
        type: integer
      shishen:
        description: 十神（一个或两个）
        items:
          $ref: '#/definitions/v1.GaoKaoResponseDataFutureDayunStageShishen'
        type: array
      startYear:
        description: 开始年份
        type: integer
      stateNum:
        description: 阶段编号（1开始）
        type: integer
    type: object
  v1.GaoKaoResponseDataFutureDayunStageShishen:
    properties:
      dayunNum:
        description: 大运编号（1开始）
        type: integer
      endYear:
        description: 结束年份
        type: integer
      half:
        description: 1-大运前五年，2-大运后五年
        type: integer
      items:
        description: 特征与建议
        items:
          $ref: '#/definitions/v1.GaoKaoResponseDataFutureDayunStageShishenItem'
        type: array
      name:
        description: 十神名称
        type: string
      startYear:
        description: 开始年份
        type: integer
      systemSay:
        description: 论玄说
        type: string
    type: object
  v1.GaoKaoResponseDataFutureDayunStageShishenItem:
    properties:
      content:
        description: 内容
        type: string
      type:
        description: 类型：1-特征、2-建议
        type: integer
      typeName:
        description: 类型名称
        type: string
    type: object
  v1.GaoKaoResponseDataShishenTianfu:
    properties:
      strongestAbility:
        description: 最强十神能力
        items:
          type: string
        type: array
      strongestShishen:
        description: 最强十神
        items:
          type: string
        type: array
      suggestedCareer:
        description: 建议职业
        type: string
      systemSay:
        description: 论玄说
        type: string
      systemTip:
        description: 默认提示
        type: string
      weakestShishen:
        description: 最弱十神
        items:
          type: string
        type: array
      weakestSuperiority:
        description: 最弱十神弱势
        items:
          type: string
        type: array
    type: object
  v1.GaoKaoResponseDataWuxingXiyong:
    properties:
      moreSuggestion:
        description: 更多建议
        items:
          type: string
        type: array
      occupation:
        description: 行业：1-用、2-喜
        items:
          type: string
        type: array
      systemSay:
        description: 论玄说
        type: string
      systemTip:
        description: 默认提示
        type: string
    type: object
  v1.GaokaoResponseDataFutureDayun:
    properties:
      stages:
        description: 阶段（共四个）
        items:
          $ref: '#/definitions/v1.GaoKaoResponseDataFutureDayunStage'
        type: array
      systemTip:
        description: 默认提示
        type: string
    type: object
  v1.GeJuBianGeRequest:
    properties:
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 生日
        example: "2006-01-02 15:04:05"
        type: string
      gender:
        enum:
        - 男
        - 女
        example: 男
        type: string
      name:
        description: 姓名
        example: 张三
        type: string
    required:
    - birthtime
    - gender
    type: object
  v1.GeJuBianGeResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.GeJuBianGeResponseDataItem'
        type: array
      message:
        type: string
    type: object
  v1.GeJuBianGeResponseDataItem:
    properties:
      name:
        description: 格局名称
        type: string
      xiyong:
        description: 喜用五行
        items:
          type: string
        type: array
    type: object
  v1.GetDatetimeBySizhuRequest:
    properties:
      endYear:
        description: 默认2099
        example: 2099
        type: integer
      sizhu:
        description: 四柱
        example:
        - 癸亥
        - 戊午
        - 壬寅
        - 己酉
        items:
          type: string
        type: array
      startYear:
        description: 默认1801
        example: 1801
        type: integer
    type: object
  v1.GetDatetimeBySizhuResponse:
    properties:
      code:
        type: integer
      data:
        items:
          type: string
        type: array
      message:
        type: string
    type: object
  v1.HepanRequest:
    properties:
      mingliIdA:
        description: 命例A
        example: 1
        type: integer
      mingliIdB:
        description: 命例B
        example: 1
        type: integer
    required:
    - mingliIdA
    - mingliIdB
    type: object
  v1.HepanResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.HepanResponseData'
      message:
        type: string
    type: object
  v1.HepanResponseData:
    properties:
      points:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataPoints'
        description: 指标
      sizhu:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataSizhu'
        description: 四柱
      wuxing:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataWuxing'
        description: 五行
    type: object
  v1.HepanResponseDataPoint:
    properties:
      a:
        description: 合盘1
        items:
          type: string
        type: array
      b:
        description: 合盘2
        items:
          type: string
        type: array
      c:
        description: 解析
        type: string
    type: object
  v1.HepanResponseDataPoints:
    properties:
      fuqigong:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataPoint'
        description: 夫妻宫
      geju:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataPoint'
        description: 格局（格局+变革）
      mingua:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataPoint'
        description: 命卦
      riyuan:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataPoint'
        description: 日元
      shengxiao:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataPoint'
        description: 生肖
      wangshuai:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataPoint'
        description: 旺衰
      wuxingWanque:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataPoint'
        description: 五行完缺
      wuxingXiyong:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataPoint'
        description: 五行喜用
      wuxingZuiruo:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataPoint'
        description: 五行最弱
      wuxingZuiwang:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataPoint'
        description: 五行最旺
      xingxiu:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataPoint'
        description: 星宿
    type: object
  v1.HepanResponseDataShensha:
    properties:
      nianZhuA:
        description: 年柱神煞1
        items:
          type: string
        type: array
      nianZhuB:
        description: 年柱神煞2
        items:
          type: string
        type: array
      relations:
        description: 相互关系
        items:
          type: string
        type: array
      riZhuA:
        description: 日柱神煞1
        items:
          type: string
        type: array
      riZhuB:
        description: 日柱神煞2
        items:
          type: string
        type: array
    type: object
  v1.HepanResponseDataSizhu:
    properties:
      ganzhi:
        description: 干支：0-年干、1-年支、2-月干、3-月支、4-日干、5-日支、6-时干、7-时支
        items:
          $ref: '#/definitions/v1.HepanResponseDataSizhuGanzhiItem'
        type: array
      nayin:
        description: 纳音：0-年、1-日
        items:
          $ref: '#/definitions/v1.HepanResponseDataSizhuNayinItem'
        type: array
      shensha:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataShensha'
        description: 神煞
    type: object
  v1.HepanResponseDataSizhuGanzhiItem:
    properties:
      a:
        description: 干
        type: string
      b:
        description: 支
        type: string
      c:
        description: 解析
        items:
          type: string
        type: array
    type: object
  v1.HepanResponseDataSizhuNayinItem:
    properties:
      a:
        description: 纳音1
        type: string
      b:
        description: 纳音2
        type: string
      c:
        description: 解析
        type: string
    type: object
  v1.HepanResponseDataWuxing:
    properties:
      a:
        items:
          $ref: '#/definitions/corona.PaipanShishenPowerItem'
        type: array
      b:
        items:
          $ref: '#/definitions/corona.PaipanShishenPowerItem'
        type: array
    type: object
  v1.LiunianAnalysisRequest:
    properties:
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 出生时间
        example: "2020-01-01 12:00:00"
        type: string
      dayun:
        description: 所选大运（小运时为空，大运流年时必须）
        type: string
      gender:
        description: 性别：1-男，2-女
        example: 1
        type: integer
      liunian:
        description: 所选流年（小运流年/大运流年）
        type: string
    required:
    - birthtime
    - gender
    - liunian
    type: object
  v1.LiunianAnalysisResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.LiunianAnalysisResponseData'
      message:
        type: string
    type: object
  v1.LiunianAnalysisResponseData:
    properties:
      guayun:
        allOf:
        - $ref: '#/definitions/model.Guaxiang'
        description: 流年卦运
      hehua:
        allOf:
        - $ref: '#/definitions/v1.MasterLiunianAnalysisHehua'
        description: 流年合化
      jiaoyunTime:
        description: 交运时间
        type: string
      liuyue:
        description: 流月运势（第13个月只返回节气（用于前端显示最佳/谨慎月份时间范围））
        items:
          $ref: '#/definitions/v1.YunshiYueScore'
        type: array
      lunduan:
        description: 流年论断
        items:
          type: string
        type: array
      overview:
        allOf:
        - $ref: '#/definitions/v1.MasterLiunianAnalysisOverview'
        description: 流年概述
      shensha:
        description: 流年神煞
        items:
          type: string
        type: array
      suiyun:
        description: 流年岁运
        items:
          type: string
        type: array
      xiyong:
        description: 流年喜用
        type: string
    type: object
  v1.LocationRequest:
    properties:
      overseas:
        description: 是否海外
        example: false
        type: boolean
    type: object
  v1.LocationResponse:
    properties:
      EnumsLocationResponseData:
        items:
          $ref: '#/definitions/v1.LocationTree'
        type: array
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.LocationTree:
    properties:
      children:
        description: 子节点，省包含市，市包含区
        items:
          $ref: '#/definitions/v1.LocationTree'
        type: array
      code:
        type: integer
      name:
        type: string
    type: object
  v1.LunarDay:
    properties:
      date:
        description: 日期
        example: "2020-01-01"
        type: string
      lunarDate:
        description: 农历日期
        example: 正月初一
        type: string
      name:
        description: 名称
        example: 初一
        type: string
    type: object
  v1.LuncaiCareerPalace:
    properties:
      down:
        description: 下（六合地支）
        type: string
      left:
        description: 左（三合地支1）
        type: string
      middle:
        description: 中（月支）
        type: string
      right:
        description: 右（三合地支2）
        type: string
      shishen:
        description: 月支十神
        type: string
      yueling:
        allOf:
        - $ref: '#/definitions/v1.LuncaiCareerPalaceYueling'
        description: 月令
    type: object
  v1.LuncaiCareerPalaceYueling:
    properties:
      dizhi:
        description: 地支（三合或六合地支）
        items:
          type: string
        type: array
      xiyong:
        description: 喜用（地支与地支五行）
        example: 卯木
        type: string
    type: object
  v1.MasterBirthAnalysis:
    properties:
      day:
        allOf:
        - $ref: '#/definitions/model.BirthDayFortune'
        description: 日
      hour:
        allOf:
        - $ref: '#/definitions/model.BirthHourFortune'
        description: 时
      month:
        allOf:
        - $ref: '#/definitions/model.BirthMonthFortune'
        description: 月
      year:
        allOf:
        - $ref: '#/definitions/model.BirthYearFortune'
        description: 年
    type: object
  v1.MasterDayunAnalysis:
    properties:
      current:
        allOf:
        - $ref: '#/definitions/v1.MasterDayunAnalysisCurrent'
        description: 当前大运
      hehua:
        allOf:
        - $ref: '#/definitions/v1.MasterDayunAnalysisHehua'
        description: 大运合化
      overview:
        allOf:
        - $ref: '#/definitions/v1.MasterDayunAnalysisOverview'
        description: 大运概述
      shensha:
        description: 大运神煞
        items:
          type: string
        type: array
      vernacular:
        allOf:
        - $ref: '#/definitions/v1.MasterDayunVernacular'
        description: 大运白话
      xiyong:
        description: 大运喜用
        type: string
    type: object
  v1.MasterDayunAnalysisCurrent:
    properties:
      bestScore:
        description: 最佳评分（综合评分）
        type: integer
      bestYears:
        description: 最佳年份
        items:
          type: integer
        type: array
      endYear:
        description: 结束年份
        type: integer
      number:
        description: 大运序号（1～12）
        type: integer
      scores:
        description: 评分列表（综合评分）
        items:
          type: integer
        type: array
      startYear:
        description: 开始年份
        type: integer
      worstScore:
        description: 最差评分（综合评分）
        type: integer
      worstYears:
        description: 最差年份
        items:
          type: integer
        type: array
      years:
        description: 年份列表
        items:
          type: integer
        type: array
    type: object
  v1.MasterDayunAnalysisHehua:
    properties:
      dizhiBansanhe:
        description: 地支半三合
        type: string
      dizhiLiuhe:
        description: 地支六合
        type: string
      dizhiSanhe:
        description: 地支三合
        type: string
      dizhiSanhui:
        description: 地支三会
        type: string
      dizhiSanxing:
        description: 地支三刑
        type: string
      dizhiWuliZhixing:
        description: 地支无礼之刑
        type: string
      dizhiXiangchong:
        description: 地支相冲
        type: string
      dizhiXianghai:
        description: 地支相害
        type: string
      dizhiZixing:
        description: 地支自刑
        type: string
      tianganWuhe:
        description: 天干合化
        type: string
    type: object
  v1.MasterDayunAnalysisOverview:
    properties:
      overview:
        description: 大运概述
        type: string
      xingyun:
        description: 星运说明
        type: string
      zhushi:
        description: 大运主事
        type: string
    type: object
  v1.MasterDayunVernacular:
    properties:
      dizhi:
        description: 地支
        type: string
      tiangan:
        description: 天干
        type: string
    type: object
  v1.MasterFavorableChoice:
    properties:
      careerPalace:
        allOf:
        - $ref: '#/definitions/v1.LuncaiCareerPalace'
        description: 事业宫
      shengxiao:
        description: 生肖
        type: string
      tianyiGuiren:
        description: 天乙贵人
        type: string
      wuxing:
        description: 五行：方位、季节、颜色、数字、居住地、饮食、药学
        items:
          type: string
        type: array
    type: object
  v1.MasterLiunianAnalysis:
    properties:
      guayun:
        allOf:
        - $ref: '#/definitions/model.Guaxiang'
        description: 流年卦运
      hehua:
        allOf:
        - $ref: '#/definitions/v1.MasterLiunianAnalysisHehua'
        description: 流年合化
      jiaoyunTime:
        description: 交运时间
        type: string
      liuyue:
        description: 流月运势（第13个月只返回节气（用于前端显示最佳/谨慎月份时间范围））
        items:
          $ref: '#/definitions/v1.YunshiYueScore'
        type: array
      lunduan:
        description: 流年论断
        items:
          type: string
        type: array
      overview:
        allOf:
        - $ref: '#/definitions/v1.MasterLiunianAnalysisOverview'
        description: 流年概述
      shensha:
        description: 流年神煞
        items:
          type: string
        type: array
      suiyun:
        description: 流年岁运
        items:
          type: string
        type: array
      xiyong:
        description: 流年喜用
        type: string
    type: object
  v1.MasterLiunianAnalysisHehua:
    properties:
      dizhiBansanhe:
        description: 地支半三合
        type: string
      dizhiLiuhe:
        description: 地支六合
        type: string
      dizhiSanhe:
        description: 地支三合
        type: string
      dizhiSanhui:
        description: 地支三会
        type: string
      dizhiSanxing:
        description: 地支三刑
        type: string
      dizhiWuliZhixing:
        description: 地支无礼之刑
        type: string
      dizhiXiangchong:
        description: 地支相冲
        type: string
      dizhiXianghai:
        description: 地支相害
        type: string
      dizhiZixing:
        description: 地支自刑
        type: string
      tianganWuhe:
        description: 天干合化
        type: string
    type: object
  v1.MasterLiunianAnalysisOverview:
    properties:
      overviewS:
        description: 流年概述（实岁）
        type: string
      overviewX:
        description: 流年概述（虚岁）
        type: string
      zhushi:
        description: 流年主事
        type: string
    type: object
  v1.MasterMinzhu:
    properties:
      bazi:
        description: 八字
        items:
          type: string
        type: array
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 出生日期
        type: string
      birthtimeLunar:
        description: 农历生日
        type: string
      gender:
        description: 性别
        type: string
      name:
        description: 姓名
        type: string
      wuxing:
        description: 五行：用神,喜神,忌神,仇神,闲神
        items:
          type: string
        type: array
      zodiac:
        description: 生肖
        type: string
    type: object
  v1.MasterRequest:
    properties:
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 生日
        example: "2006-01-02 15:04:05"
        type: string
      gender:
        enum:
        - 男
        - 女
        example: 男
        type: string
      ignoreRecord:
        description: 是否忽略记录
        type: boolean
      name:
        description: 姓名
        example: 张三
        type: string
    required:
    - birthtime
    - gender
    type: object
  v1.MasterResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.MasterResponseData'
      message:
        type: string
    type: object
  v1.MasterResponseData:
    properties:
      birthAnalysis:
        allOf:
        - $ref: '#/definitions/v1.MasterBirthAnalysis'
        description: 生辰分析
      dayunAnalysis:
        allOf:
        - $ref: '#/definitions/v1.MasterDayunAnalysis'
        description: 大运分析
      favorableChoice:
        allOf:
        - $ref: '#/definitions/v1.MasterFavorableChoice'
        description: 有利选择
      gongmingGuanyun:
        description: 功名官运
        items:
          type: string
        type: array
      id:
        type: integer
      liunianAnalysis:
        allOf:
        - $ref: '#/definitions/v1.MasterLiunianAnalysis'
        description: 流年分析
      minggong:
        allOf:
        - $ref: '#/definitions/model.Minggong'
        description: 命宫
      mingzhu:
        allOf:
        - $ref: '#/definitions/v1.MasterMinzhu'
        description: 命主信息
      rizhuShengshi:
        allOf:
        - $ref: '#/definitions/v1.MasterRizhuShengshi'
        description: 日主生时
      shentiJiankang:
        description: 身体健康
        items:
          type: string
        type: array
      shishenLunduan:
        allOf:
        - $ref: '#/definitions/v1.MatchMingliRulesResponseData'
        description: 十神论断
      tiaoHouYongShen:
        allOf:
        - $ref: '#/definitions/model.BirthMonthRizhu'
        description: 调侯用神
      tiekouZhiduan:
        allOf:
        - $ref: '#/definitions/v1.MasterTiekouZhiduan'
        description: 铁口直断
      xinge:
        allOf:
        - $ref: '#/definitions/v1.MasterXinge'
        description: 性格
      yizhuLunmin:
        allOf:
        - $ref: '#/definitions/v1.MasterYizhuLunmin'
        description: 一柱论命
      zuyeYichan:
        description: 祖业遗产
        items:
          type: string
        type: array
    type: object
  v1.MasterRizhuShengshi:
    properties:
      birthDayHour:
        allOf:
        - $ref: '#/definitions/model.BirthDayHour'
        description: 日柱时柱
      rizhuShengshi:
        allOf:
        - $ref: '#/definitions/model.BirthHourRizhu'
        description: 日主生时
    type: object
  v1.MasterTiekouZhiduan:
    properties:
      ganzhiDuan:
        description: 干支断
        items:
          type: string
        type: array
      shangcanDuan:
        description: 伤残断
        type: string
      shishenDuan1:
        description: 十神断1
        items:
          type: string
        type: array
      shishenDuan2:
        description: 十神断2
        items:
          type: string
        type: array
    type: object
  v1.MasterXinge:
    properties:
      rigan:
        allOf:
        - $ref: '#/definitions/v1.MasterXingeRigan'
        description: 日干
      riganWaixiang:
        description: 日干外相
        type: string
      riganWuxingWangxiang:
        allOf:
        - $ref: '#/definitions/v1.MasterXingeRiganWuxingWangxiang'
        description: 日干五行旺相
      xinge:
        description: 性格规则
        items:
          type: string
        type: array
      yinyangPingheng:
        description: 阴阳平衡
        type: string
      yueshiYongji:
        description: 月时用忌
        items:
          type: string
        type: array
      yuezhiShishen:
        allOf:
        - $ref: '#/definitions/v1.MasterXingeYuezhiShishen'
        description: 月支十神
      yuezhuXingyun:
        allOf:
        - $ref: '#/definitions/v1.MasterXingeYuezhuXingyun'
        description: 月柱星运
    type: object
  v1.MasterXingeRigan:
    properties:
      mongpie:
        description: "盲派（芒果派？\U0001F96D）"
        type: string
      rigan:
        description: 日干
        type: string
      rizhu:
        description: 日主
        type: string
      xinxing:
        description: 心性
        type: string
    type: object
  v1.MasterXingeRiganWuxingWangxiang:
    properties:
      riganWuxing:
        description: 日干五行
        type: string
      shuoming:
        description: 说明
        type: string
      wuxingWangshuai:
        description: 五行旺衰
        type: string
    type: object
  v1.MasterXingeYuezhiShishen:
    properties:
      quedot:
        description: 缺点
        type: string
      shishen:
        description: 十神
        type: string
      xinxing:
        description: 心性
        type: string
      youdot:
        description: 优点
        type: string
    type: object
  v1.MasterXingeYuezhuXingyun:
    properties:
      shuoming:
        description: 说明
        type: string
      xingyun:
        description: 星运
        type: string
    type: object
  v1.MasterXiyongWangshuai4Wuxing:
    properties:
      chouji:
        description: 仇忌概率
        type: string
      xian:
        description: 闲神概率
        type: string
      xiyong:
        description: 喜用概率
        type: string
    type: object
  v1.MasterXiyongWangshuaiRequest:
    properties:
      birthtime:
        description: 生日
        example: "2006-01-02"
        type: string
      gender:
        enum:
        - 男
        - 女
        example: 男
        type: string
      times:
        description: 可能的时辰（格式为数组，00:30:00）
        example:
        - "00:30:00"
        items:
          type: string
        type: array
    required:
    - birthtime
    - gender
    - times
    type: object
  v1.MasterXiyongWangshuaiResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.MasterXiyongWangshuaiResponseData'
      message:
        type: string
    type: object
  v1.MasterXiyongWangshuaiResponseData:
    properties:
      wangshuai:
        additionalProperties:
          type: string
        type: object
      xiyong:
        additionalProperties:
          $ref: '#/definitions/v1.MasterXiyongWangshuai4Wuxing'
        type: object
    type: object
  v1.MasterYizhuLunmin:
    properties:
      lunmin:
        description: 轮命
        items:
          type: string
        type: array
      rizhu:
        description: 日主
        type: string
    type: object
  v1.MatchMingliRuleResponseDataCondition:
    properties:
      criterion:
        description: 条件依据
        example: 依据
        type: string
      id:
        description: 条件ID
        example: 1
        type: integer
    type: object
  v1.MatchMingliRulesResponseData:
    properties:
      birth:
        description: 出生信息（索引：0-年、1-月、2-日、3-时）
        items:
          type: string
        type: array
      rules:
        description: 匹配规则
        items:
          $ref: '#/definitions/v1.MatchMingliRulesResponseDataRule'
        type: array
    type: object
  v1.MatchMingliRulesResponseDataRule:
    properties:
      conditions:
        description: 匹配条件
        items:
          $ref: '#/definitions/v1.MatchMingliRuleResponseDataCondition'
        type: array
      id:
        description: 规则ID
        example: 1
        type: integer
      result:
        description: 判断结果
        example: 判断结果
        type: string
    type: object
  v1.PageListHepanRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListHepanRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListHepanRequestParam:
    type: object
  v1.PageListHepanResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListHepanResponseData'
      message:
        type: string
    type: object
  v1.PageListHepanResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListHepanResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListHepanResponseDataItem:
    properties:
      createdAt:
        description: 创建时间
        type: string
      id:
        description: 合盘ID
        type: integer
      mingliA:
        allOf:
        - $ref: '#/definitions/v1.PageListHepanResponseDataItemMingli'
        description: 命例A
      mingliB:
        allOf:
        - $ref: '#/definitions/v1.PageListHepanResponseDataItemMingli'
        description: 命例B
    type: object
  v1.PageListHepanResponseDataItemMingli:
    properties:
      bazi:
        description: 八字：天干+地支
        items:
          type: string
        type: array
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 出生时间（公历）
        type: string
      birthtimeLunar:
        description: 农历生日
        type: string
      birthtimeSun:
        description: 真太阳时
        type: string
      gender:
        description: 性别：1-男，2-女
        type: integer
      id:
        description: 命例ID
        type: integer
      name:
        description: 姓名
        type: string
    type: object
  v1.PaipanRecordOwnRequest:
    properties:
      ids:
        description: ID
        items:
          type: integer
        type: array
    required:
    - ids
    type: object
  v1.PaipanRecordOwnResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.ViewHepanRequest:
    properties:
      id:
        description: 合盘ID
        example: 1
        type: integer
    required:
    - id
    type: object
  v1.ViewHepanResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.ViewHepanResponseData'
      message:
        type: string
    type: object
  v1.ViewHepanResponseData:
    properties:
      points:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataPoints'
        description: 指标
      sizhu:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataSizhu'
        description: 四柱
      wuxing:
        allOf:
        - $ref: '#/definitions/v1.HepanResponseDataWuxing'
        description: 五行
    type: object
  v1.YunshiYueScore:
    properties:
      finalScore:
        type: number
      ganzhi:
        type: string
      jieqi:
        type: string
      jieqiTime:
        type: string
    type: object
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a http server template.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: 排盘专家版
  version: 1.0.0
paths:
  /app/version/newest:
    post:
      consumes:
      - application/json
      description: 最新版本
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CheckAppUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 最新版本
          schema:
            $ref: '#/definitions/v1.CheckAppUpdateResponse'
      summary: 最新版本
      tags:
      - 应用版本
  /date/day:
    post:
      consumes:
      - application/json
      description: 获取本日日历
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CalendarDayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CalendarDayResponse'
      summary: 获取本日日历
      tags:
      - 日历
  /date/month:
    post:
      consumes:
      - application/json
      description: 获取本月日历
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CalendarMonthRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CalendarMonthResponse'
      summary: 获取本月日历
      tags:
      - 日历
  /datetime/fromSizhu:
    post:
      consumes:
      - application/json
      description: 从四柱获取时间
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.GetDatetimeBySizhuRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.GetDatetimeBySizhuResponse'
      summary: 从四柱获取时间
      tags:
      - 时间
  /dayun:
    post:
      consumes:
      - application/json
      description: 大运分析
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DayunAnalysisRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.DayunAnalysisResponse'
      summary: 大运分析
      tags:
      - 排盘专家版
  /dayunliunian:
    post:
      consumes:
      - application/json
      description: 大运流年
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DayunliunianRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.DayunliunianResponse'
      summary: 大运流年
      tags:
      - 排盘专家版
  /enums/dizhi:
    post:
      consumes:
      - application/json
      description: 获取地支
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsDizhiResponse'
      summary: 获取地支
      tags:
      - 枚举
  /enums/location:
    post:
      consumes:
      - application/json
      description: 获取地区列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsLocationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsLocationResponse'
      summary: 获取地区列表
      tags:
      - 枚举
  /enums/lunar:
    post:
      consumes:
      - application/json
      description: 获取农历列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsLunarRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsLunarResponse'
      summary: 获取农历列表
      tags:
      - 枚举
  /enums/shishen/property:
    post:
      consumes:
      - application/json
      description: 获取十神特征
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsShishenPropertyResponse'
      summary: 获取十神特征
      tags:
      - 枚举
  /enums/tiangan:
    post:
      consumes:
      - application/json
      description: 获取天干
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsTianganResponse'
      summary: 获取天干
      tags:
      - 枚举
  /enums/wuxing:
    post:
      consumes:
      - application/json
      description: 获取五行
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsWuxingResponse'
      summary: 获取五行
      tags:
      - 枚举
  /gaokao:
    post:
      consumes:
      - application/json
      description: 高考
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.GaoKaoRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.GaoKaoResponse'
      summary: 高考
      tags:
      - 高考专题
  /gaokao/preference:
    post:
      consumes:
      - application/json
      description: 高考志愿时间
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.GaoKaoPreferenceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.GaoKaoPreferenceResponse'
      summary: 高考志愿时间
      tags:
      - 高考专题
  /geju/biange:
    post:
      consumes:
      - application/json
      description: 格局别格
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.GeJuBianGeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.GeJuBianGeResponse'
      summary: 格局别格
      tags:
      - 排盘专家版
  /hepan:
    post:
      consumes:
      - application/json
      description: 合盘
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.HepanRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.HepanResponse'
      security:
      - BearerAuth: []
      summary: 合盘
      tags:
      - 合盘
  /hepan/delete:
    post:
      consumes:
      - application/json
      description: 删除合盘记录
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DeleteHepanRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.DeleteHepanResponse'
      security:
      - BearerAuth: []
      summary: 删除合盘记录
      tags:
      - 合盘
  /hepan/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询合盘记录
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListHepanRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.PageListHepanResponse'
      security:
      - BearerAuth: []
      summary: 分页查询合盘记录
      tags:
      - 合盘
  /hepan/view:
    post:
      consumes:
      - application/json
      description: 查看合盘记录
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.ViewHepanRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.ViewHepanResponse'
      security:
      - BearerAuth: []
      summary: 查看合盘记录
      tags:
      - 合盘
  /ip:
    post:
      consumes:
      - application/json
      description: 获取IP
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
      summary: 获取IP
      tags:
      - 排盘专家版
  /liunian:
    post:
      consumes:
      - application/json
      description: 流年分析
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.LiunianAnalysisRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.LiunianAnalysisResponse'
      summary: 流年分析
      tags:
      - 排盘专家版
  /location:
    post:
      consumes:
      - application/json
      description: 获取地区树
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.LocationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.LocationResponse'
      summary: 获取地区树
      tags:
      - 地区
  /master/:
    post:
      consumes:
      - application/json
      description: 排盘专家版
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.MasterRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.MasterResponse'
      summary: 排盘专家版
      tags:
      - 排盘专家版
  /paipanRecord/own:
    post:
      consumes:
      - application/json
      description: 占有排盘记录
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PaipanRecordOwnRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.PaipanRecordOwnResponse'
      security:
      - BearerAuth: []
      summary: 占有排盘记录
      tags:
      - 排盘
  /xiyongwangshaui:
    post:
      consumes:
      - application/json
      description: 喜用旺衰
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.MasterXiyongWangshuaiRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.MasterXiyongWangshuaiResponse'
      summary: 喜用旺衰
      tags:
      - 排盘专家版
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
