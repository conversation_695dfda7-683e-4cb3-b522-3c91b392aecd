{"swagger": "2.0", "info": {"description": "This is a http server template.", "title": "排盘专家版", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0.0"}, "paths": {"/app/version/newest": {"post": {"description": "最新版本", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用版本"], "summary": "最新版本", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CheckAppUpdateRequest"}}], "responses": {"200": {"description": "最新版本", "schema": {"$ref": "#/definitions/v1.CheckAppUpdateResponse"}}}}}, "/date/day": {"post": {"description": "获取本日日历", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["日历"], "summary": "获取本日日历", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CalendarDayRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.CalendarDayResponse"}}}}}, "/date/month": {"post": {"description": "获取本月日历", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["日历"], "summary": "获取本月日历", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CalendarMonthRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.CalendarMonthResponse"}}}}}, "/datetime/fromSizhu": {"post": {"description": "从四柱获取时间", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["时间"], "summary": "从四柱获取时间", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.GetDatetimeBySizhuRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.GetDatetimeBySizhuResponse"}}}}}, "/dayun": {"post": {"description": "大运分析", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["排盘专家版"], "summary": "大运分析", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.DayunAnalysisRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.DayunAnalysisResponse"}}}}}, "/dayunliunian": {"post": {"description": "大运流年", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["排盘专家版"], "summary": "大运流年", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.Day<PERSON>liunianRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.DayunliunianResponse"}}}}}, "/enums/dizhi": {"post": {"description": "获取地支", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "获取地支", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsDizhiResponse"}}}}}, "/enums/location": {"post": {"description": "获取地区列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "获取地区列表", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsLocationRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsLocationResponse"}}}}}, "/enums/lunar": {"post": {"description": "获取农历列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "获取农历列表", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsLunarRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsLunarResponse"}}}}}, "/enums/shishen/property": {"post": {"description": "获取十神特征", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "获取十神特征", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsShishenPropertyResponse"}}}}}, "/enums/tiangan": {"post": {"description": "获取天干", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "获取天干", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsTianganResponse"}}}}}, "/enums/wuxing": {"post": {"description": "获取五行", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "获取五行", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsWuxingResponse"}}}}}, "/gaokao": {"post": {"description": "高考", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["高考专题"], "summary": "高考", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.GaoKaoRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.GaoKaoResponse"}}}}}, "/gaokao/preference": {"post": {"description": "高考志愿时间", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["高考专题"], "summary": "高考志愿时间", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.GaoKaoPreferenceRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.GaoKaoPreferenceResponse"}}}}}, "/geju/biange": {"post": {"description": "格局别格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["排盘专家版"], "summary": "格局别格", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.GeJuBianGeRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.GeJuBianGeResponse"}}}}}, "/hepan": {"post": {"security": [{"BearerAuth": []}], "description": "合盘", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["合盘"], "summary": "合盘", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.HepanRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.HepanResponse"}}}}}, "/hepan/delete": {"post": {"security": [{"BearerAuth": []}], "description": "删除合盘记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["合盘"], "summary": "删除合盘记录", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.DeleteHepanRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.DeleteHepanResponse"}}}}}, "/hepan/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "分页查询合盘记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["合盘"], "summary": "分页查询合盘记录", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListHepanRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.PageListHepanResponse"}}}}}, "/hepan/view": {"post": {"security": [{"BearerAuth": []}], "description": "查看合盘记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["合盘"], "summary": "查看合盘记录", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.ViewHepanRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.ViewHepanResponse"}}}}}, "/invite/accept": {"post": {"security": [{"BearerAuth": []}], "description": "接受邀请", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["邀请推广"], "summary": "接受邀请", "parameters": [{"type": "string", "description": "application_horoscope", "name": "Application", "in": "header", "required": true}, {"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.AcceptInvitationRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.AcceptInvitationResponse"}}}}}, "/invite/binding-ip": {"post": {"security": [{"BearerAuth": []}], "description": "渠道码绑定IP", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["邀请推广"], "summary": "渠道码绑定IP", "parameters": [{"type": "string", "description": "AE86", "name": "Channel", "in": "header", "required": true}, {"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.InviteBindingIPRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.InviteBindingIPResponse"}}}}}, "/invite/code": {"post": {"security": [{"BearerAuth": []}], "description": "个人邀请码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["邀请推广"], "summary": "个人邀请码", "parameters": [{"type": "string", "description": "application_horoscope", "name": "Application", "in": "header", "required": true}, {"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.GetInviteCodeRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.GetInvitationCodeResponse"}}}}}, "/invite/referrals": {"post": {"security": [{"BearerAuth": []}], "description": "邀请记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["邀请推广"], "summary": "邀请记录", "parameters": [{"type": "string", "description": "application_horoscope", "name": "Application", "in": "header", "required": true}, {"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.GetInviteReferralsRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.GetInviteReferralsResponse"}}}}}, "/invite/referrer": {"post": {"security": [{"BearerAuth": []}], "description": "邀请人", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["邀请推广"], "summary": "邀请人", "parameters": [{"type": "string", "description": "application_horoscope", "name": "Application", "in": "header", "required": true}, {"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.GetInviteReferrerRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.GetInviteReferrerResponse"}}}}}, "/invite/reward/popups/unread": {"post": {"security": [{"BearerAuth": []}], "description": "未读弹窗", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["邀请推广"], "summary": "未读弹窗", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.GetInviteRewardUnreadPopupsRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.GetInviteRewardUnreadPopupsResponse"}}}}}, "/ip": {"post": {"description": "获取IP", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["排盘专家版"], "summary": "获取IP", "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/liunian": {"post": {"description": "流年分析", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["排盘专家版"], "summary": "流年分析", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.LiunianAnalysisRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.LiunianAnalysisResponse"}}}}}, "/location": {"post": {"description": "获取地区树", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["地区"], "summary": "获取地区树", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.LocationRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.LocationResponse"}}}}}, "/master/": {"post": {"description": "排盘专家版", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["排盘专家版"], "summary": "排盘专家版", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.MasterRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.MasterResponse"}}}}}, "/paipanRecord/own": {"post": {"security": [{"BearerAuth": []}], "description": "占有排盘记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["排盘"], "summary": "占有排盘记录", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PaipanRecordOwnRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.PaipanRecordOwnResponse"}}}}}, "/xiyongwangshaui": {"post": {"description": "喜用旺衰", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["排盘专家版"], "summary": "喜用旺衰", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.MasterXiyongWangshuaiRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.MasterXiyongWangshuaiResponse"}}}}}}, "definitions": {"corona.PaipanShishenPowerItem": {"type": "object", "properties": {"attr": {"description": "五行", "type": "string"}, "cangNum": {"description": "藏干数量", "type": "integer"}, "liliang": {"description": "力量", "type": "integer"}, "num": {"description": "五行数量", "type": "integer"}, "power": {"description": "能量占比数组", "type": "array", "items": {"type": "number"}}, "powerArr": {"description": "能量数组", "type": "array", "items": {"type": "integer"}}, "shiShen": {"description": "十神数组", "type": "array", "items": {"type": "string"}}, "shiShenName": {"description": "十神合称", "type": "string"}, "tianganArr": {"description": "天干数组", "type": "array", "items": {"type": "string"}}, "totalBfb": {"description": "总能量占比", "type": "number"}, "totalPower": {"description": "总能量", "type": "integer"}}}, "model.BirthDayFortune": {"type": "object", "properties": {"birthDay": {"type": "string"}, "fortuneText": {"type": "string"}}}, "model.BirthDayHour": {"type": "object", "properties": {"basis": {"type": "string"}, "conclusion": {"type": "string"}, "rizhu": {"type": "string"}, "shizhu": {"type": "string"}}}, "model.BirthHourFortune": {"type": "object", "properties": {"birthHour": {"type": "string"}, "criticalYears": {"type": "string"}, "fortuneText": {"type": "string"}, "suitableCareer": {"type": "string"}, "timeRange": {"type": "string"}}}, "model.BirthHourRizhu": {"type": "object", "properties": {"basis": {"type": "string"}, "birthHour": {"type": "string"}, "conclusion": {"type": "string"}, "rizhu": {"type": "string"}}}, "model.BirthMonthFortune": {"type": "object", "properties": {"birthMonth": {"type": "string"}, "conceptionMonth": {"type": "string"}, "endDate": {"type": "string"}, "fortuneText": {"type": "string"}, "solarTerm": {"type": "string"}, "startDate": {"type": "string"}}}, "model.BirthMonthRizhu": {"type": "object", "properties": {"birthMonth": {"type": "string"}, "explanation": {"type": "string"}, "god": {"type": "string"}, "rizhu": {"type": "string"}}}, "model.BirthYearFortune": {"type": "object", "properties": {"basis": {"type": "string"}, "birthYear": {"type": "string"}, "femaleFortune": {"type": "string"}, "maleFortune": {"type": "string"}, "wuxing": {"type": "string"}, "zodiac": {"type": "string"}, "zodiacDetail": {"type": "string"}}}, "model.Guaxiang": {"type": "object", "properties": {"duanri": {"description": "断日", "type": "string"}, "gejue": {"description": "歌诀", "type": "string"}, "guaji": {"description": "卦吉", "type": "string"}, "id": {"description": "主键ID", "type": "integer"}, "idiom": {"description": "成语", "type": "string"}, "jieshi": {"description": "解释", "type": "string"}, "name": {"description": "卦名", "type": "string"}, "topdown": {"description": "上下卦", "type": "string"}, "value": {"description": "卦值", "type": "integer"}, "yao1": {"description": "爻1", "type": "integer"}, "yao2": {"description": "爻2", "type": "integer"}, "yao3": {"description": "爻3", "type": "integer"}, "yao4": {"description": "爻4", "type": "integer"}, "yao5": {"description": "爻5", "type": "integer"}, "yao6": {"description": "爻6", "type": "integer"}, "yaoValue": {"description": "爻值", "type": "integer"}}}, "model.Minggong": {"type": "object", "properties": {"constellation": {"type": "string"}, "createdAt": {"description": "创建时间", "type": "string"}, "development": {"type": "string"}, "name": {"type": "string"}, "star": {"type": "string"}, "summary": {"type": "string"}, "temperament": {"type": "string"}, "updatedAt": {"description": "更新时间", "type": "string"}}}, "v1.AcceptInvitationRequest": {"type": "object", "required": ["inviteCode"], "properties": {"inviteCode": {"description": "邀请码", "type": "string"}}}, "v1.AcceptInvitationResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.AcceptInvitationResponseData"}, "message": {"type": "string"}}}, "v1.AcceptInvitationResponseData": {"type": "object", "properties": {"giftVipDuration": {"description": "赠送会员时长（单位s）", "type": "integer"}, "isNewVip": {"description": "true-被邀请前不为会员，false-被邀请前已是会员", "type": "boolean"}, "vipExpiredAt": {"description": "会员到期时间（yyyy-MM-dd HH:mm:ss）", "type": "string"}}}, "v1.CalendarDayRequest": {"type": "object", "properties": {"appID": {"description": "应用：2-排盘、3-万年历、4-运势、5-论财", "type": "integer", "example": 3}, "date": {"description": "公历日期", "type": "string", "example": "2020-01-01"}}}, "v1.CalendarDayResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.CalendarDayResponseData"}, "message": {"type": "string"}}}, "v1.CalendarDayResponseData": {"type": "object", "properties": {"bazi1": {"description": "八字1（年份干支）", "type": "string", "example": "庚申"}, "bazi2": {"description": "八字2（月份干支）", "type": "string", "example": "壬午"}, "bazi2Next": {"description": "八字2（下个月份干支）", "type": "string", "example": "壬午"}, "bazi3": {"description": "八字3（日期干支）", "type": "string", "example": "辛巳"}, "caiLocation": {"description": "财位", "type": "string", "example": "东北"}, "constellation": {"description": "星座", "type": "string", "example": "双鱼座"}, "date": {"description": "公历日期", "type": "string", "example": "2099-03-12"}, "festival": {"description": "节日", "type": "array", "items": {"type": "string"}, "example": ["北方小年", "南方小年"]}, "fuLocation": {"description": "福位", "type": "string", "example": "西南"}, "heidao": {"description": "黑道", "type": "string", "example": "白虎"}, "hou": {"description": "七十二侯", "type": "string", "example": "半夏生"}, "huangdao": {"description": "黄道", "type": "string", "example": "青龙"}, "ji": {"description": "忌", "type": "array", "items": {"type": "string"}, "example": ["阴宅破土", "安葬", "启攒", "探亲访友"]}, "jieqi": {"description": "节气（今日或之前的最后一个节气）", "type": "string", "example": "大雪"}, "jieqiDate": {"description": "节气日期", "type": "string", "example": "2006-01-02"}, "jieqiTime": {"description": "节气时间", "type": "string", "example": "2006-01-02 15:00:59"}, "jishen": {"description": "吉神", "type": "array", "items": {"type": "string"}, "example": ["天德合", "月德合"]}, "luLocation": {"description": "禄位", "type": "string", "example": "东南"}, "lunarDate": {"description": "农历日期", "type": "string", "example": "二月廿一"}, "pengzubaiji": {"description": "彭祖百忌", "type": "array", "items": {"type": "string"}, "example": ["乙不栽植 千株不长", "未不服药 毒气入肠"]}, "pengzubaijiOverview": {"description": "彭祖百忌概述", "type": "string", "example": "猴日冲虎煞南"}, "shierjianri": {"description": "十二建日", "type": "string", "example": "定日"}, "taishen": {"description": "胎神", "type": "string", "example": "房床厕外"}, "taishenLocation": {"description": "胎神位置", "type": "string", "example": "西北"}, "times": {"description": "时辰（共13个，包含早子时与晚子时）", "type": "array", "items": {"$ref": "#/definitions/v1.<PERSON><PERSON><PERSON><PERSON>"}}, "weekday": {"description": "星期", "type": "string", "example": "星期四"}, "wuxing": {"description": "五行", "type": "string", "example": "山下火"}, "xiLocation": {"description": "喜位", "type": "string", "example": "西北"}, "xingxiu": {"description": "星宿", "type": "string", "example": "张月鹿"}, "xiongshen": {"description": "凶神", "type": "array", "items": {"type": "string"}, "example": ["月破", "大耗", "四击", "九空"]}, "yellowYears": {"description": "黄帝纪年：公元年+2697", "type": "integer", "example": 4721}, "yellowYearsZh": {"description": "黄帝纪年", "type": "string", "example": "四千七百二十一"}, "yi": {"description": "宜", "type": "array", "items": {"type": "string"}, "example": ["祭祀", "打扫", "破屋坏垣"]}, "zeri": {"description": "择日", "type": "string", "example": "大吉"}, "zodiac": {"description": "生肖", "type": "string", "example": "鼠"}}}, "v1.CalendarEachDayOfMonth": {"type": "object", "properties": {"bazi": {"description": "八字", "type": "string", "example": "庚申"}, "currentMonth": {"description": "是否为当前月份", "type": "boolean", "example": true}, "date": {"description": "公历日期", "type": "string", "example": "2020-01-01"}, "holidayOff": {"description": "节假日调休：1休，2班", "type": "integer", "example": 1}, "ji": {"description": "忌", "type": "array", "items": {"type": "string"}, "example": ["阴宅破土", "安葬", "启攒", "探亲访友"]}, "jieqi": {"description": "节气", "type": "string", "example": "大雪"}, "jieqiTime": {"description": "节气时间", "type": "string"}, "jieri": {"description": "节日", "type": "array", "items": {"type": "string"}, "example": ["北方小年", "南方小年"]}, "liuriShensha": {"description": "流日神煞", "type": "array", "items": {"type": "string"}}, "lunarDate": {"description": "农历日期", "type": "string", "example": "二月廿一"}, "vipShishen": {"description": "VIP的干十神与支十神", "type": "array", "items": {"type": "string"}}, "weekday": {"description": "星期", "type": "string", "example": "星期四"}, "yi": {"description": "宜", "type": "array", "items": {"type": "string"}, "example": ["祭祀", "打扫", "破屋坏垣"]}}}, "v1.CalendarMonthRequest": {"type": "object", "properties": {"appID": {"description": "应用：2-排盘、3-万年历、4-运势、5-论财", "type": "integer"}, "month": {"description": "公历月份", "type": "string", "example": "2020-01"}}}, "v1.CalendarMonthResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"type": "array", "items": {"$ref": "#/definitions/v1.CalendarEachDayOfMonth"}}}, "message": {"type": "string"}}}, "v1.CalendarShichen": {"type": "object", "properties": {"bazi": {"description": "八字", "type": "string", "example": "丙子"}, "caiLocation": {"description": "财位", "type": "string", "example": "财神东北"}, "chong": {"description": "冲", "type": "string", "example": "虎"}, "fuLocation": {"description": "福位", "type": "string", "example": "福神西南"}, "ji": {"description": "忌", "type": "string", "example": "忌"}, "jixiong": {"description": "吉", "type": "string", "example": "吉"}, "luLocation": {"description": "禄位", "type": "string", "example": "阳贵东南"}, "sha": {"description": "煞", "type": "string", "example": "南"}, "time": {"description": "时辰", "type": "string", "example": "23:00-00:59"}, "xiLocation": {"description": "喜位", "type": "string", "example": "喜神西北"}, "yi": {"description": "宜", "type": "string", "example": "宜"}}}, "v1.CheckAppUpdateRequest": {"type": "object", "required": ["osType", "versionName"], "properties": {"osType": {"description": "1:android, 2:ios", "type": "integer"}, "versionName": {"description": "版本名称", "type": "string"}}}, "v1.CheckAppUpdateResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.CheckAppUpdateResponseData"}, "message": {"type": "string"}}}, "v1.CheckAppUpdateResponseData": {"type": "object", "properties": {"createdAt": {"description": "创建时间", "type": "string"}, "isForceUpdate": {"description": "是否强制更新", "type": "boolean"}, "isHotUpdate": {"description": "是否热更新", "type": "boolean"}, "updateNote": {"description": "更新说明", "type": "string"}, "updatedAt": {"description": "更新时间", "type": "string"}, "url": {"description": "下载地址", "type": "string"}, "versionCode": {"description": "版本号", "type": "integer"}, "versionName": {"description": "版本名称", "type": "string"}}}, "v1.DayunAnalysisRequest": {"type": "object", "required": ["birthtime", "dayun", "gender"], "properties": {"birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "出生时间", "type": "string", "example": "2020-01-01 12:00:00"}, "dayun": {"description": "所选大运", "type": "string"}, "gender": {"description": "性别：1-男，2-女", "type": "integer", "example": 1}}}, "v1.DayunAnalysisResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.DayunAnalysisResponseData"}, "message": {"type": "string"}}}, "v1.DayunAnalysisResponseData": {"type": "object", "properties": {"current": {"description": "当前大运", "allOf": [{"$ref": "#/definitions/v1.MasterDayunAnalysisCurrent"}]}, "hehua": {"description": "大运合化", "allOf": [{"$ref": "#/definitions/v1.MasterDayunAnalysisHehua"}]}, "overview": {"description": "大运概述", "allOf": [{"$ref": "#/definitions/v1.MasterDayunAnalysisOverview"}]}, "shensha": {"description": "大运神煞", "type": "array", "items": {"type": "string"}}, "vernacular": {"description": "大运白话", "allOf": [{"$ref": "#/definitions/v1.MasterDayunVernacular"}]}, "xiyong": {"description": "大运喜用", "type": "string"}}}, "v1.DayunliunianRequest": {"type": "object", "required": ["birthtime", "gender"], "properties": {"birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "出生时间", "type": "string", "example": "2020-01-01 12:00:00"}, "gender": {"description": "性别：1-男，2-女", "type": "integer", "example": 1}}}, "v1.DayunliunianResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.<PERSON><PERSON>liunianResponseData"}, "message": {"type": "string"}}}, "v1.DayunliunianResponseData": {"type": "object", "properties": {"birthtime": {"description": "出生时间（太阳时）", "type": "string"}, "birthtimeLunar": {"description": "出生时间（农历）", "type": "string"}, "dayunGanzhiList": {"description": "大运干支列表：12个", "type": "array", "items": {"type": "string"}}, "dayunJiezhi": {"description": "大运结束年份（农历）", "type": "integer"}, "dayunLiunianGanzhiList": {"description": "大运流年干支列表：120个", "type": "array", "items": {"type": "string"}}, "dayunQishi": {"description": "大运起始年份（农历）", "type": "integer"}, "dayunScoreList": {"description": "大运评分：24个，每两个组成一个大运", "type": "array", "items": {"type": "integer"}}, "jiaoyunTime": {"description": "交运时间（公历）", "type": "string"}, "liunianScoreList": {"description": "流年评分：n+120个，小限流年n+大运流年120", "type": "array", "items": {"type": "integer"}}, "xiaoxianScoreList": {"description": "小运评分：n个", "type": "array", "items": {"type": "integer"}}, "xiaoyunGanzhiList": {"description": "小运干支列表：n个", "type": "array", "items": {"type": "string"}}, "xiaoyunJiezhi": {"description": "小运结束年份（农历）", "type": "integer"}, "xiaoyunLiunianGanzhiList": {"description": "小运流年干支列表：n个", "type": "array", "items": {"type": "string"}}, "xiaoyunQishi": {"description": "小运起始年份（农历）", "type": "integer"}, "xiaoyunYears": {"description": "小运年数", "type": "integer"}, "zongheScoreList": {"description": "综合评分：n+120个，小限流年n+大运流年120", "type": "array", "items": {"type": "integer"}}}}, "v1.DeleteHepanRequest": {"type": "object", "properties": {"ids": {"description": "ID：空值则全部清空。", "type": "array", "items": {"type": "integer"}}}}, "v1.DeleteHepanResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.EnumsDizhiResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsDizhiResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsDizhiResponseItem": {"type": "object", "properties": {"dizhi": {"description": "地支", "type": "string"}, "id": {"type": "integer", "example": 1}, "jieqi": {"description": "节气", "type": "string"}, "name": {"type": "string", "example": "子"}, "shichen": {"description": "时辰", "type": "string"}, "shuxiang": {"description": "属相", "type": "string"}, "wuxing": {"description": "五行", "type": "string"}, "yinyang": {"description": "阴阳", "type": "string"}, "yuefen": {"description": "月份", "type": "string"}, "zhongqi": {"description": "中气", "type": "string"}}}, "v1.EnumsLocationRequest": {"type": "object", "properties": {"overseas": {"description": "是否海外", "type": "boolean", "example": false}}}, "v1.EnumsLocationResponse": {"type": "object", "properties": {"EnumsLocationResponseData": {"type": "array", "items": {"$ref": "#/definitions/v1.LocationTree"}}, "code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.EnumsLunarRequest": {"type": "object", "required": ["year"], "properties": {"year": {"description": "年份", "type": "string", "example": "2020"}}}, "v1.EnumsLunarResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsLunarResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsLunarResponseItem": {"type": "object", "properties": {"days": {"description": "日期", "type": "array", "items": {"$ref": "#/definitions/v1.LunarDay"}}, "month": {"description": "月份", "type": "string", "example": "正月"}}}, "v1.EnumsShishenPropertyItem": {"type": "object", "properties": {"ability": {"description": "能力", "type": "string"}, "bias": {"description": "偏向", "type": "string"}, "category": {"description": "十神类型", "type": "string"}, "insufficient": {"description": "不足", "type": "string"}, "overview": {"description": "概述", "type": "string"}, "preferredRegion": {"description": "适合地区", "type": "string"}, "profession": {"description": "职业", "type": "string"}, "superiority": {"description": "擅长", "type": "string"}, "type": {"description": "类型", "type": "string"}, "wealthBy": {"description": "财富来源", "type": "string"}}}, "v1.EnumsShishenPropertyResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsShishenPropertyItem"}}, "message": {"type": "string"}}}, "v1.EnumsTianganResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsTianganResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsTianganResponseItem": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "甲"}, "tiangan": {"description": "天干", "type": "string"}, "wuxing": {"description": "五行", "type": "string"}, "yinyang": {"description": "阴阳", "type": "string"}}}, "v1.EnumsWuxingResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsWuxingResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsWuxingResponseItem": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer", "example": 1}, "name": {"description": "五行名称", "type": "string", "example": "金"}, "position": {"description": "方位", "type": "string"}, "profession": {"description": "职业", "type": "string"}, "season": {"description": "季节", "type": "string"}, "wuxing": {"description": "五行", "type": "string"}}}, "v1.GaoKaoPreferenceRequest": {"type": "object", "required": ["birthtime", "gender"], "properties": {"birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "生日", "type": "string", "example": "2006-01-02 15:04:05"}, "endTime": {"description": "选择时间结束", "type": "string", "example": "2025-07-07 18:00:00"}, "gender": {"description": "性别", "type": "string", "enum": ["男", "女"], "example": "男"}, "name": {"description": "姓名", "type": "string", "example": "张三"}, "startTime": {"description": "选择时间开始", "type": "string", "example": "2025-07-07 08:00:00"}}}, "v1.GaoKaoPreferenceResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.GaoKaoPreferenceResponseData"}, "message": {"type": "string"}}}, "v1.GaoKaoPreferenceResponseData": {"type": "object", "properties": {"systemSay": {"description": "论玄说", "type": "string"}, "systemTip": {"description": "默认提示", "type": "string"}, "timeRanges": {"description": "时间段（开始时间与结束时间）", "type": "array", "items": {"type": "array", "items": {"type": "string"}}}, "times": {"description": "小时序号", "type": "array", "items": {"type": "integer"}}}}, "v1.GaoKaoRequest": {"type": "object", "required": ["birthtime", "gender", "startYear"], "properties": {"birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "生日", "type": "string", "example": "2006-01-02 15:04:05"}, "gender": {"description": "性别", "type": "string", "enum": ["男", "女"], "example": "男"}, "name": {"description": "姓名", "type": "string", "example": "张三"}, "startYear": {"description": "开始年份", "type": "integer", "example": 2025}}}, "v1.GaoKaoResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.GaoKaoResponseData"}, "message": {"type": "string"}}}, "v1.GaoKaoResponseData": {"type": "object", "properties": {"basicInfo": {"description": "基本信息", "allOf": [{"$ref": "#/definitions/v1.GaoKaoResponseDataBasicInfo"}]}, "favorableChoice": {"description": "有利选择", "allOf": [{"$ref": "#/definitions/v1.GaoKaoResponseDataFavorableChoice"}]}, "futureDayun": {"description": "未来大运", "allOf": [{"$ref": "#/definitions/v1.GaokaoResponseDataFutureDayun"}]}, "shishenTianfu": {"description": "十神天赋-看方向", "allOf": [{"$ref": "#/definitions/v1.GaoKaoResponseDataShishenTianfu"}]}, "startYear": {"description": "开始年份", "type": "integer", "example": 2025}, "wuxingXiyong": {"description": "五行喜用-选行业", "allOf": [{"$ref": "#/definitions/v1.GaoKaoResponseDataWuxingXiyong"}]}}}, "v1.GaoKaoResponseDataBasicInfo": {"type": "object", "properties": {"bazi": {"description": "八字（四柱）", "type": "array", "items": {"type": "string"}}, "birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "出生时间（用户输入）", "type": "string"}, "birthtimeLunar": {"description": "出生时间农历（真太阳时）", "type": "string"}, "birthtimeSolar": {"description": "出生时间公历（真太阳时）", "type": "string"}, "gender": {"description": "性别", "type": "string"}, "name": {"description": "姓名", "type": "string"}, "power": {"description": "五行/十神/十天干能量", "type": "array", "items": {"$ref": "#/definitions/corona.PaipanShishenPowerItem"}}, "xiyong": {"description": "五行喜用：用喜仇忌闲", "type": "array", "items": {"type": "string"}}}}, "v1.GaoKaoResponseDataFavorableChoice": {"type": "object", "properties": {"choiceArea": {"description": "选择区域", "type": "string"}, "choiceDirection": {"description": "选择方向", "type": "string"}, "systemSay": {"description": "论玄说", "type": "string"}, "systemTip": {"description": "默认提示", "type": "string"}}}, "v1.GaoKaoResponseDataFutureDayunStage": {"type": "object", "properties": {"endYear": {"description": "结束年份", "type": "integer"}, "shishen": {"description": "十神（一个或两个）", "type": "array", "items": {"$ref": "#/definitions/v1.GaoKaoResponseDataFutureDayunStageShishen"}}, "startYear": {"description": "开始年份", "type": "integer"}, "stateNum": {"description": "阶段编号（1开始）", "type": "integer"}}}, "v1.GaoKaoResponseDataFutureDayunStageShishen": {"type": "object", "properties": {"dayunNum": {"description": "大运编号（1开始）", "type": "integer"}, "endYear": {"description": "结束年份", "type": "integer"}, "items": {"description": "特征与建议", "type": "array", "items": {"$ref": "#/definitions/v1.GaoKaoResponseDataFutureDayunStageShishenItem"}}, "name": {"description": "十神名称", "type": "string"}, "startYear": {"description": "开始年份", "type": "integer"}, "systemSay": {"description": "论玄说", "type": "string"}}}, "v1.GaoKaoResponseDataFutureDayunStageShishenItem": {"type": "object", "properties": {"content": {"description": "内容", "type": "string"}, "type": {"description": "类型：1-特征、2-建议", "type": "integer"}, "typeName": {"description": "类型名称", "type": "string"}}}, "v1.GaoKaoResponseDataShishenTianfu": {"type": "object", "properties": {"strongestAbility": {"description": "最强十神能力", "type": "array", "items": {"type": "string"}}, "strongestShishen": {"description": "最强十神", "type": "array", "items": {"type": "string"}}, "suggestedCareer": {"description": "建议职业", "type": "string"}, "systemSay": {"description": "论玄说", "type": "string"}, "systemTip": {"description": "默认提示", "type": "string"}, "weakestShishen": {"description": "最弱十神", "type": "array", "items": {"type": "string"}}, "weakestSuperiority": {"description": "最弱十神弱势", "type": "array", "items": {"type": "string"}}}}, "v1.GaoKaoResponseDataWuxingXiyong": {"type": "object", "properties": {"moreSuggestion": {"description": "更多建议", "type": "array", "items": {"type": "string"}}, "occupation": {"description": "行业：1-用、2-喜", "type": "array", "items": {"type": "string"}}, "systemSay": {"description": "论玄说", "type": "string"}, "systemTip": {"description": "默认提示", "type": "string"}}}, "v1.GaokaoResponseDataFutureDayun": {"type": "object", "properties": {"stages": {"description": "阶段（共四个）", "type": "array", "items": {"$ref": "#/definitions/v1.GaoKaoResponseDataFutureDayunStage"}}, "systemTip": {"description": "默认提示", "type": "string"}}}, "v1.GeJuBianGeRequest": {"type": "object", "required": ["birthtime", "gender"], "properties": {"birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "生日", "type": "string", "example": "2006-01-02 15:04:05"}, "gender": {"type": "string", "enum": ["男", "女"], "example": "男"}, "name": {"description": "姓名", "type": "string", "example": "张三"}}}, "v1.GeJuBianGeResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.GeJuBianGeResponseDataItem"}}, "message": {"type": "string"}}}, "v1.GeJuBianGeResponseDataItem": {"type": "object", "properties": {"name": {"description": "格局名称", "type": "string"}, "xiyong": {"description": "喜用五行", "type": "array", "items": {"type": "string"}}}}, "v1.GetDatetimeBySizhuRequest": {"type": "object", "properties": {"endYear": {"description": "默认2099", "type": "integer", "example": 2099}, "sizhu": {"description": "四柱", "type": "array", "items": {"type": "string"}, "example": ["癸亥", "戊午", "壬寅", "己酉"]}, "startYear": {"description": "默认1801", "type": "integer", "example": 1801}}}, "v1.GetDatetimeBySizhuResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"type": "string"}}, "message": {"type": "string"}}}, "v1.GetInvitationCodeResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.GetInviteCodeResponseData"}, "message": {"type": "string"}}}, "v1.GetInviteCodeRequest": {"type": "object"}, "v1.GetInviteCodeResponseData": {"type": "object", "properties": {"inviteCode": {"type": "string"}}}, "v1.GetInviteReferralsRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.GetInviteReferralsRequestParam"}}}, "v1.GetInviteReferralsRequestParam": {"type": "object"}, "v1.GetInviteReferralsResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.GetInviteReferralsResponseData"}, "message": {"type": "string"}}}, "v1.GetInviteReferralsResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.GetInviteReferralsResponseDataItem"}}, "total": {"type": "integer"}}}, "v1.GetInviteReferralsResponseDataItem": {"type": "object", "properties": {"giftVipDuration": {"description": "当前账号赠送会员时长（单位s）", "type": "integer"}, "inviteAt": {"description": "邀请时间（yyyy-MM-dd HH:mm:ss）", "type": "string"}, "invitee": {"description": "被邀请人手机号加密", "type": "string"}}}, "v1.GetInviteReferrerRequest": {"type": "object"}, "v1.GetInviteReferrerResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.GetInviteReferrerResponseData"}, "message": {"type": "string"}}}, "v1.GetInviteReferrerResponseData": {"type": "object", "properties": {"giftVipDuration": {"description": "当前账号赠送会员时长（单位s）", "type": "integer"}, "inviteAt": {"description": "被邀请时间（yyyy-MM-dd HH:mm:ss）", "type": "string"}, "inviter": {"description": "邀请人手机号加密", "type": "string"}}}, "v1.GetInviteRewardUnreadPopupsRequest": {"type": "object"}, "v1.GetInviteRewardUnreadPopupsResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.GetInviteRewardUnreadPopupsResponseDataItem"}}, "message": {"type": "string"}}}, "v1.GetInviteRewardUnreadPopupsResponseDataItem": {"type": "object", "properties": {"giftVipDuration": {"description": "赠送会员时长（单位s）", "type": "integer"}, "inviteCode": {"description": "邀请码", "type": "string"}, "inviteTime": {"description": "邀请时间", "type": "string"}, "invitee": {"description": "被邀请人手机号加密，type=inviter_reward 时返回", "type": "string"}, "inviter": {"description": "邀请人手机号加密，type=invitee_reward 时返回", "type": "string"}, "type": {"description": "弹窗类型：invitee_reward - 被邀请者奖励 / inviter_reward - 邀请人奖励", "type": "string", "example": "invitee_reward || inviter_reward"}}}, "v1.HepanRequest": {"type": "object", "required": ["mingliIdA", "mingliIdB"], "properties": {"mingliIdA": {"description": "命例A", "type": "integer", "example": 1}, "mingliIdB": {"description": "命例B", "type": "integer", "example": 1}}}, "v1.HepanResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.HepanResponseData"}, "message": {"type": "string"}}}, "v1.HepanResponseData": {"type": "object", "properties": {"points": {"description": "指标", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataPoints"}]}, "sizhu": {"description": "四柱", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataSizhu"}]}, "wuxing": {"description": "五行", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataWuxing"}]}}}, "v1.HepanResponseDataPoint": {"type": "object", "properties": {"a": {"description": "合盘1", "type": "array", "items": {"type": "string"}}, "b": {"description": "合盘2", "type": "array", "items": {"type": "string"}}, "c": {"description": "解析", "type": "string"}}}, "v1.HepanResponseDataPoints": {"type": "object", "properties": {"fuqigong": {"description": "夫妻宫", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataPoint"}]}, "geju": {"description": "格局（格局+变革）", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataPoint"}]}, "mingua": {"description": "命卦", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataPoint"}]}, "riyuan": {"description": "日元", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataPoint"}]}, "shengxiao": {"description": "生肖", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataPoint"}]}, "wangshuai": {"description": "旺衰", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataPoint"}]}, "wuxingWanque": {"description": "五行完缺", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataPoint"}]}, "wuxingXiyong": {"description": "五行喜用", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataPoint"}]}, "wuxingZuiruo": {"description": "五行最弱", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataPoint"}]}, "wuxingZuiwang": {"description": "五行最旺", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataPoint"}]}, "xingxiu": {"description": "星宿", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataPoint"}]}}}, "v1.HepanResponseDataShensha": {"type": "object", "properties": {"nianZhuA": {"description": "年柱神煞1", "type": "array", "items": {"type": "string"}}, "nianZhuB": {"description": "年柱神煞2", "type": "array", "items": {"type": "string"}}, "relations": {"description": "相互关系", "type": "array", "items": {"type": "string"}}, "riZhuA": {"description": "日柱神煞1", "type": "array", "items": {"type": "string"}}, "riZhuB": {"description": "日柱神煞2", "type": "array", "items": {"type": "string"}}}}, "v1.HepanResponseDataSizhu": {"type": "object", "properties": {"ganzhi": {"description": "干支：0-年干、1-年支、2-月干、3-月支、4-日干、5-日支、6-时干、7-时支", "type": "array", "items": {"$ref": "#/definitions/v1.HepanResponseDataSizhuGanzhiItem"}}, "nayin": {"description": "纳音：0-年、1-日", "type": "array", "items": {"$ref": "#/definitions/v1.HepanResponseDataSizhuNayinItem"}}, "shensha": {"description": "神煞", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataShensha"}]}}}, "v1.HepanResponseDataSizhuGanzhiItem": {"type": "object", "properties": {"a": {"description": "干", "type": "string"}, "b": {"description": "支", "type": "string"}, "c": {"description": "解析", "type": "array", "items": {"type": "string"}}}}, "v1.HepanResponseDataSizhuNayinItem": {"type": "object", "properties": {"a": {"description": "纳音1", "type": "string"}, "b": {"description": "纳音2", "type": "string"}, "c": {"description": "解析", "type": "string"}}}, "v1.HepanResponseDataWuxing": {"type": "object", "properties": {"a": {"type": "array", "items": {"$ref": "#/definitions/corona.PaipanShishenPowerItem"}}, "b": {"type": "array", "items": {"$ref": "#/definitions/corona.PaipanShishenPowerItem"}}}}, "v1.InviteBindingIPRequest": {"type": "object"}, "v1.InviteBindingIPResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.LiunianAnalysisRequest": {"type": "object", "required": ["birthtime", "gender", "liunian"], "properties": {"birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "出生时间", "type": "string", "example": "2020-01-01 12:00:00"}, "dayun": {"description": "所选大运（小运时为空，大运流年时必须）", "type": "string"}, "gender": {"description": "性别：1-男，2-女", "type": "integer", "example": 1}, "liunian": {"description": "所选流年（小运流年/大运流年）", "type": "string"}}}, "v1.LiunianAnalysisResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.LiunianAnalysisResponseData"}, "message": {"type": "string"}}}, "v1.LiunianAnalysisResponseData": {"type": "object", "properties": {"guayun": {"description": "流年卦运", "allOf": [{"$ref": "#/definitions/model.Guaxiang"}]}, "hehua": {"description": "流年合化", "allOf": [{"$ref": "#/definitions/v1.MasterLiunianAnalysisHehua"}]}, "jiaoyunTime": {"description": "交运时间", "type": "string"}, "liuyue": {"description": "流月运势（第13个月只返回节气（用于前端显示最佳/谨慎月份时间范围））", "type": "array", "items": {"$ref": "#/definitions/v1.YunshiYueScore"}}, "lunduan": {"description": "流年论断", "type": "array", "items": {"type": "string"}}, "overview": {"description": "流年概述", "allOf": [{"$ref": "#/definitions/v1.MasterLiunianAnalysisOverview"}]}, "shensha": {"description": "流年神煞", "type": "array", "items": {"type": "string"}}, "suiyun": {"description": "流年岁运", "type": "array", "items": {"type": "string"}}, "xiyong": {"description": "流年喜用", "type": "string"}}}, "v1.LocationRequest": {"type": "object", "properties": {"overseas": {"description": "是否海外", "type": "boolean", "example": false}}}, "v1.LocationResponse": {"type": "object", "properties": {"EnumsLocationResponseData": {"type": "array", "items": {"$ref": "#/definitions/v1.LocationTree"}}, "code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.LocationTree": {"type": "object", "properties": {"children": {"description": "子节点，省包含市，市包含区", "type": "array", "items": {"$ref": "#/definitions/v1.LocationTree"}}, "code": {"type": "integer"}, "name": {"type": "string"}}}, "v1.LunarDay": {"type": "object", "properties": {"date": {"description": "日期", "type": "string", "example": "2020-01-01"}, "lunarDate": {"description": "农历日期", "type": "string", "example": "正月初一"}, "name": {"description": "名称", "type": "string", "example": "初一"}}}, "v1.LuncaiCareerPalace": {"type": "object", "properties": {"down": {"description": "下（六合地支）", "type": "string"}, "left": {"description": "左（三合地支1）", "type": "string"}, "middle": {"description": "中（月支）", "type": "string"}, "right": {"description": "右（三合地支2）", "type": "string"}, "shishen": {"description": "月支十神", "type": "string"}, "yueling": {"description": "月令", "allOf": [{"$ref": "#/definitions/v1.LuncaiCareerPalaceYueling"}]}}}, "v1.LuncaiCareerPalaceYueling": {"type": "object", "properties": {"dizhi": {"description": "地支（三合或六合地支）", "type": "array", "items": {"type": "string"}}, "xiyong": {"description": "喜用（地支与地支五行）", "type": "string", "example": "卯木"}}}, "v1.MasterBirthAnalysis": {"type": "object", "properties": {"day": {"description": "日", "allOf": [{"$ref": "#/definitions/model.BirthDayFortune"}]}, "hour": {"description": "时", "allOf": [{"$ref": "#/definitions/model.BirthHourFortune"}]}, "month": {"description": "月", "allOf": [{"$ref": "#/definitions/model.BirthMonthFortune"}]}, "year": {"description": "年", "allOf": [{"$ref": "#/definitions/model.BirthYearFortune"}]}}}, "v1.MasterDayunAnalysis": {"type": "object", "properties": {"current": {"description": "当前大运", "allOf": [{"$ref": "#/definitions/v1.MasterDayunAnalysisCurrent"}]}, "hehua": {"description": "大运合化", "allOf": [{"$ref": "#/definitions/v1.MasterDayunAnalysisHehua"}]}, "overview": {"description": "大运概述", "allOf": [{"$ref": "#/definitions/v1.MasterDayunAnalysisOverview"}]}, "shensha": {"description": "大运神煞", "type": "array", "items": {"type": "string"}}, "vernacular": {"description": "大运白话", "allOf": [{"$ref": "#/definitions/v1.MasterDayunVernacular"}]}, "xiyong": {"description": "大运喜用", "type": "string"}}}, "v1.MasterDayunAnalysisCurrent": {"type": "object", "properties": {"bestScore": {"description": "最佳评分（综合评分）", "type": "integer"}, "bestYears": {"description": "最佳年份", "type": "array", "items": {"type": "integer"}}, "endYear": {"description": "结束年份", "type": "integer"}, "number": {"description": "大运序号（1～12）", "type": "integer"}, "scores": {"description": "评分列表（综合评分）", "type": "array", "items": {"type": "integer"}}, "startYear": {"description": "开始年份", "type": "integer"}, "worstScore": {"description": "最差评分（综合评分）", "type": "integer"}, "worstYears": {"description": "最差年份", "type": "array", "items": {"type": "integer"}}, "years": {"description": "年份列表", "type": "array", "items": {"type": "integer"}}}}, "v1.MasterDayunAnalysisHehua": {"type": "object", "properties": {"dizhiBansanhe": {"description": "地支半三合", "type": "string"}, "dizhiLiuhe": {"description": "地支六合", "type": "string"}, "dizhiSanhe": {"description": "地支三合", "type": "string"}, "dizhiSanhui": {"description": "地支三会", "type": "string"}, "dizhiSanxing": {"description": "地支三刑", "type": "string"}, "dizhiWuliZhixing": {"description": "地支无礼之刑", "type": "string"}, "dizhiXiangchong": {"description": "地支相冲", "type": "string"}, "dizhiXianghai": {"description": "地支相害", "type": "string"}, "dizhiZixing": {"description": "地支自刑", "type": "string"}, "tianganWuhe": {"description": "天干合化", "type": "string"}}}, "v1.MasterDayunAnalysisOverview": {"type": "object", "properties": {"overview": {"description": "大运概述", "type": "string"}, "xingyun": {"description": "星运说明", "type": "string"}, "zhushi": {"description": "大运主事", "type": "string"}}}, "v1.MasterDayunVernacular": {"type": "object", "properties": {"dizhi": {"description": "地支", "type": "string"}, "tiangan": {"description": "天干", "type": "string"}}}, "v1.MasterFavorableChoice": {"type": "object", "properties": {"careerPalace": {"description": "事业宫", "allOf": [{"$ref": "#/definitions/v1.LuncaiCareerPalace"}]}, "shengxiao": {"description": "生肖", "type": "string"}, "tianyiGuiren": {"description": "天乙贵人", "type": "string"}, "wuxing": {"description": "五行：方位、季节、颜色、数字、居住地、饮食、药学", "type": "array", "items": {"type": "string"}}}}, "v1.MasterLiunianAnalysis": {"type": "object", "properties": {"guayun": {"description": "流年卦运", "allOf": [{"$ref": "#/definitions/model.Guaxiang"}]}, "hehua": {"description": "流年合化", "allOf": [{"$ref": "#/definitions/v1.MasterLiunianAnalysisHehua"}]}, "jiaoyunTime": {"description": "交运时间", "type": "string"}, "liuyue": {"description": "流月运势（第13个月只返回节气（用于前端显示最佳/谨慎月份时间范围））", "type": "array", "items": {"$ref": "#/definitions/v1.YunshiYueScore"}}, "lunduan": {"description": "流年论断", "type": "array", "items": {"type": "string"}}, "overview": {"description": "流年概述", "allOf": [{"$ref": "#/definitions/v1.MasterLiunianAnalysisOverview"}]}, "shensha": {"description": "流年神煞", "type": "array", "items": {"type": "string"}}, "suiyun": {"description": "流年岁运", "type": "array", "items": {"type": "string"}}, "xiyong": {"description": "流年喜用", "type": "string"}}}, "v1.MasterLiunianAnalysisHehua": {"type": "object", "properties": {"dizhiBansanhe": {"description": "地支半三合", "type": "string"}, "dizhiLiuhe": {"description": "地支六合", "type": "string"}, "dizhiSanhe": {"description": "地支三合", "type": "string"}, "dizhiSanhui": {"description": "地支三会", "type": "string"}, "dizhiSanxing": {"description": "地支三刑", "type": "string"}, "dizhiWuliZhixing": {"description": "地支无礼之刑", "type": "string"}, "dizhiXiangchong": {"description": "地支相冲", "type": "string"}, "dizhiXianghai": {"description": "地支相害", "type": "string"}, "dizhiZixing": {"description": "地支自刑", "type": "string"}, "tianganWuhe": {"description": "天干合化", "type": "string"}}}, "v1.MasterLiunianAnalysisOverview": {"type": "object", "properties": {"overviewS": {"description": "流年概述（实岁）", "type": "string"}, "overviewX": {"description": "流年概述（虚岁）", "type": "string"}, "zhushi": {"description": "流年主事", "type": "string"}}}, "v1.MasterMinzhu": {"type": "object", "properties": {"bazi": {"description": "八字", "type": "array", "items": {"type": "string"}}, "birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "出生日期", "type": "string"}, "birthtimeLunar": {"description": "农历生日", "type": "string"}, "gender": {"description": "性别", "type": "string"}, "name": {"description": "姓名", "type": "string"}, "wuxing": {"description": "五行：用神,喜神,忌神,仇神,闲神", "type": "array", "items": {"type": "string"}}, "zodiac": {"description": "生肖", "type": "string"}}}, "v1.MasterRequest": {"type": "object", "required": ["birthtime", "gender"], "properties": {"birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "生日", "type": "string", "example": "2006-01-02 15:04:05"}, "gender": {"type": "string", "enum": ["男", "女"], "example": "男"}, "ignoreRecord": {"description": "是否忽略记录", "type": "boolean"}, "name": {"description": "姓名", "type": "string", "example": "张三"}}}, "v1.MasterResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.MasterResponseData"}, "message": {"type": "string"}}}, "v1.MasterResponseData": {"type": "object", "properties": {"birthAnalysis": {"description": "生辰分析", "allOf": [{"$ref": "#/definitions/v1.MasterBirthAnalysis"}]}, "dayunAnalysis": {"description": "大运分析", "allOf": [{"$ref": "#/definitions/v1.MasterDayunAnalysis"}]}, "favorableChoice": {"description": "有利选择", "allOf": [{"$ref": "#/definitions/v1.MasterFavorableChoice"}]}, "gongmingGuanyun": {"description": "功名官运", "type": "array", "items": {"type": "string"}}, "id": {"type": "integer"}, "liunianAnalysis": {"description": "流年分析", "allOf": [{"$ref": "#/definitions/v1.MasterLiunianAnalysis"}]}, "minggong": {"description": "命宫", "allOf": [{"$ref": "#/definitions/model.Minggong"}]}, "mingzhu": {"description": "命主信息", "allOf": [{"$ref": "#/definitions/v1.Master<PERSON><PERSON><PERSON><PERSON>"}]}, "rizhuShengshi": {"description": "日主生时", "allOf": [{"$ref": "#/definitions/v1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "shentiJiankang": {"description": "身体健康", "type": "array", "items": {"type": "string"}}, "shishenLunduan": {"description": "十神论断", "allOf": [{"$ref": "#/definitions/v1.MatchMingliRulesResponseData"}]}, "tiaoHouYongShen": {"description": "调侯用神", "allOf": [{"$ref": "#/definitions/model.BirthMonthRizhu"}]}, "tiekouZhiduan": {"description": "铁口直断", "allOf": [{"$ref": "#/definitions/v1.<PERSON><PERSON><PERSON><PERSON>"}]}, "xinge": {"description": "性格", "allOf": [{"$ref": "#/definitions/v1.MasterXinge"}]}, "yizhuLunmin": {"description": "一柱论命", "allOf": [{"$ref": "#/definitions/v1.Master<PERSON><PERSON>hu<PERSON><PERSON>min"}]}, "zuyeYichan": {"description": "祖业遗产", "type": "array", "items": {"type": "string"}}}}, "v1.MasterRizhuShengshi": {"type": "object", "properties": {"birthDayHour": {"description": "日柱时柱", "allOf": [{"$ref": "#/definitions/model.BirthDayHour"}]}, "rizhuShengshi": {"description": "日主生时", "allOf": [{"$ref": "#/definitions/model.BirthHourRizhu"}]}}}, "v1.MasterTiekouZhiduan": {"type": "object", "properties": {"ganzhiDuan": {"description": "干支断", "type": "array", "items": {"type": "string"}}, "shangcanDuan": {"description": "伤残断", "type": "string"}, "shishenDuan1": {"description": "十神断1", "type": "array", "items": {"type": "string"}}, "shishenDuan2": {"description": "十神断2", "type": "array", "items": {"type": "string"}}}}, "v1.MasterXinge": {"type": "object", "properties": {"rigan": {"description": "日干", "allOf": [{"$ref": "#/definitions/v1.MasterXingeRigan"}]}, "riganWaixiang": {"description": "日干外相", "type": "string"}, "riganWuxingWangxiang": {"description": "日干五行旺相", "allOf": [{"$ref": "#/definitions/v1.MasterXingeRiganWuxingWangxiang"}]}, "xinge": {"description": "性格规则", "type": "array", "items": {"type": "string"}}, "yinyangPingheng": {"description": "阴阳平衡", "type": "string"}, "yueshiYongji": {"description": "月时用忌", "type": "array", "items": {"type": "string"}}, "yuezhiShishen": {"description": "月支十神", "allOf": [{"$ref": "#/definitions/v1.MasterXingeYuezhiShishen"}]}, "yuezhuXingyun": {"description": "月柱星运", "allOf": [{"$ref": "#/definitions/v1.MasterXingeYuezhuXingyun"}]}}}, "v1.MasterXingeRigan": {"type": "object", "properties": {"mongpie": {"description": "盲派（芒果派？🥭）", "type": "string"}, "rigan": {"description": "日干", "type": "string"}, "rizhu": {"description": "日主", "type": "string"}, "xinxing": {"description": "心性", "type": "string"}}}, "v1.MasterXingeRiganWuxingWangxiang": {"type": "object", "properties": {"riganWuxing": {"description": "日干五行", "type": "string"}, "shuoming": {"description": "说明", "type": "string"}, "wuxingWangshuai": {"description": "五行旺衰", "type": "string"}}}, "v1.MasterXingeYuezhiShishen": {"type": "object", "properties": {"quedot": {"description": "缺点", "type": "string"}, "shishen": {"description": "十神", "type": "string"}, "xinxing": {"description": "心性", "type": "string"}, "youdot": {"description": "优点", "type": "string"}}}, "v1.MasterXingeYuezhuXingyun": {"type": "object", "properties": {"shuoming": {"description": "说明", "type": "string"}, "xingyun": {"description": "星运", "type": "string"}}}, "v1.MasterXiyongWangshuai4Wuxing": {"type": "object", "properties": {"chouji": {"description": "仇忌概率", "type": "string"}, "xian": {"description": "闲神概率", "type": "string"}, "xiyong": {"description": "喜用概率", "type": "string"}}}, "v1.MasterXiyongWangshuaiRequest": {"type": "object", "required": ["birthtime", "gender", "times"], "properties": {"birthtime": {"description": "生日", "type": "string", "example": "2006-01-02"}, "gender": {"type": "string", "enum": ["男", "女"], "example": "男"}, "times": {"description": "可能的时辰（格式为数组，00:30:00）", "type": "array", "items": {"type": "string"}, "example": ["00:30:00"]}}}, "v1.MasterXiyongWangshuaiResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.MasterXiyongWangshuaiResponseData"}, "message": {"type": "string"}}}, "v1.MasterXiyongWangshuaiResponseData": {"type": "object", "properties": {"wangshuai": {"type": "object", "additionalProperties": {"type": "string"}}, "xiyong": {"type": "object", "additionalProperties": {"$ref": "#/definitions/v1.MasterXiyongWangshuai4Wuxing"}}}}, "v1.MasterYizhuLunmin": {"type": "object", "properties": {"lunmin": {"description": "轮命", "type": "array", "items": {"type": "string"}}, "rizhu": {"description": "日主", "type": "string"}}}, "v1.MatchMingliRuleResponseDataCondition": {"type": "object", "properties": {"criterion": {"description": "条件依据", "type": "string", "example": "依据"}, "id": {"description": "条件ID", "type": "integer", "example": 1}}}, "v1.MatchMingliRulesResponseData": {"type": "object", "properties": {"birth": {"description": "出生信息（索引：0-年、1-月、2-日、3-时）", "type": "array", "items": {"type": "string"}}, "rules": {"description": "匹配规则", "type": "array", "items": {"$ref": "#/definitions/v1.MatchMingliRulesResponseDataRule"}}}}, "v1.MatchMingliRulesResponseDataRule": {"type": "object", "properties": {"conditions": {"description": "匹配条件", "type": "array", "items": {"$ref": "#/definitions/v1.MatchMingliRuleResponseDataCondition"}}, "id": {"description": "规则ID", "type": "integer", "example": 1}, "result": {"description": "判断结果", "type": "string", "example": "判断结果"}}}, "v1.PageListHepanRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListHepanRequestParam"}}}, "v1.PageListHepanRequestParam": {"type": "object"}, "v1.PageListHepanResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListHepanResponseData"}, "message": {"type": "string"}}}, "v1.PageListHepanResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListHepanResponseDataItem"}}, "total": {"type": "integer"}}}, "v1.PageListHepanResponseDataItem": {"type": "object", "properties": {"createdAt": {"description": "创建时间", "type": "string"}, "id": {"description": "合盘ID", "type": "integer"}, "mingliA": {"description": "命例A", "allOf": [{"$ref": "#/definitions/v1.PageListHepanResponseDataItemMingli"}]}, "mingliB": {"description": "命例B", "allOf": [{"$ref": "#/definitions/v1.PageListHepanResponseDataItemMingli"}]}}}, "v1.PageListHepanResponseDataItemMingli": {"type": "object", "properties": {"bazi": {"description": "八字：天干+地支", "type": "array", "items": {"type": "string"}}, "birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "出生时间（公历）", "type": "string"}, "birthtimeLunar": {"description": "农历生日", "type": "string"}, "birthtimeSun": {"description": "真太阳时", "type": "string"}, "gender": {"description": "性别：1-男，2-女", "type": "integer"}, "id": {"description": "命例ID", "type": "integer"}, "name": {"description": "姓名", "type": "string"}}}, "v1.PaipanRecordOwnRequest": {"type": "object", "required": ["ids"], "properties": {"ids": {"description": "ID", "type": "array", "items": {"type": "integer"}}}}, "v1.PaipanRecordOwnResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.ViewHepanRequest": {"type": "object", "required": ["id"], "properties": {"id": {"description": "合盘ID", "type": "integer", "example": 1}}}, "v1.ViewHepanResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.ViewHepanResponseData"}, "message": {"type": "string"}}}, "v1.ViewHepanResponseData": {"type": "object", "properties": {"points": {"description": "指标", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataPoints"}]}, "sizhu": {"description": "四柱", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataSizhu"}]}, "wuxing": {"description": "五行", "allOf": [{"$ref": "#/definitions/v1.HepanResponseDataWuxing"}]}}}, "v1.YunshiYueScore": {"type": "object", "properties": {"finalScore": {"type": "number"}, "ganzhi": {"type": "string"}, "jieqi": {"type": "string"}, "jieqiTime": {"type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}