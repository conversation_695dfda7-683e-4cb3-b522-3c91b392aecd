package fortune

import (
	"github.com/gin-gonic/gin"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type LocationHandler struct {
	*handler.BaseHandler
	locationService service.LocationService
}

func NewLocationHandler(
	handler *handler.BaseHandler,
	locationService service.LocationService,
) *LocationHandler {
	return &LocationHandler{
		BaseHandler:     handler,
		locationService: locationService,
	}
}

func (slf *LocationHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/location", slf.Location)
}

// Location godoc
// @Summary 获取地区树
// @Schemes
// @Description 获取地区树
// @Tags 地区
// @Accept json
// @Produce json
// @Param request body v1.LocationRequest true "params"
// @Success 200 {object} v1.LocationResponse
// @Router /location [post]
func (slf *LocationHandler) Location(ctx *gin.Context) {
	var req v1.LocationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.locationService.Location(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
