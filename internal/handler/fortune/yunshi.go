package fortune

import (
	"github.com/gin-gonic/gin"
	_ "zodiacus/api/v1"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type Yun<PERSON><PERSON>andler struct {
	*handler.BaseHandler
	yunshiService service.YunshiService
}

func NewYunshiHandler(
	handler *handler.BaseHandler,
	yunshiService service.YunshiService,
) *YunshiHandler {
	return &YunshiHandler{
		BaseHandler:   handler,
		yunshiService: yunshiService,
	}
}

func (slf *<PERSON><PERSON>Handler) Handle(required, optional, nameless *handler.AuthRouter) {
	optional.POST("/yunshi", slf.Yunshi)
}

// Yunshi godoc
// @Summary 2025运势
// @Schemes
// @Description 2025运势
// @Tags 2025运势
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.YunshiRequest true "params"
// @Success 200 {object} v1.YunshiResponse
// @Router /yunshi [post]
func (slf *<PERSON><PERSON>and<PERSON>) Yunshi(ctx *gin.Context) {
	var req v1.YunshiRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	req.UserAgent = ctx.GetHeader("Auth-Agent")
	req.IP = ctx.ClientIP()
	res, err := slf.yunshiService.Yunshi(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
