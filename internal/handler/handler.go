package handler

import (
	"context"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strings"
	v1 "zodiacus/api/v1"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/third_party/casdoor"
)

type AuthRouter = gin.RouterGroup

type Handler interface {
	Handle(required, optional, nameless *AuthRouter)
	Log(ctx context.Context) *log.Logger
	Auth(ctx *gin.Context) (*casdoor.User, bool)
	Reply(ctx *gin.Context, reply any, httpStatusCode ...int)
}

type BaseHandler struct {
	logger *log.Logger
}

func NewHandler(
	logger *log.Logger,
) *BaseHandler {
	return &BaseHandler{
		logger: logger,
	}
}

func (slf *BaseHandler) Handler(required, optional, nameless *AuthRouter) {}

func (slf *BaseHandler) Log(ctx context.Context) *log.Logger {
	return slf.logger.WithContext(ctx)
}

func (slf *BaseHandler) Auth(ctx *gin.Context) *jwthub.Auth {
	v, exists := ctx.Get("auth")
	if !exists {
		return nil
	}
	return v.(*jwthub.Auth)
}

func (slf *BaseHandler) VIP(ctx *gin.Context) *casdoor.VIP {
	value, exists := ctx.Get("vip")
	if !exists {
		return nil
	}
	return value.(*casdoor.VIP)
}

func (slf *BaseHandler) Application(ctx *gin.Context) string {
	header := ctx.GetHeader("Application")
	if header == "" {
		return "application_horoscope"
	}
	return strings.TrimPrefix(header, "admin/")
}

func (slf *BaseHandler) Reply(ctx *gin.Context, reply any, httpStatusCode ...int) {
	switch v := reply.(type) {
	case error:
		slf.Log(ctx).Error("handler error", zap.Error(v))
		v1.HandleError(ctx, v, httpStatusCode...)
	default:
		v1.HandleSuccess(ctx, v, httpStatusCode...)
	}
}
