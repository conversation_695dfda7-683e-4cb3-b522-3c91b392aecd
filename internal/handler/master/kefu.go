package master

import (
	"github.com/gin-gonic/gin"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type KefuHandler struct {
	*handler.BaseHandler
	kefuService service.KefuService
}

func NewKefuHandler(handler *handler.BaseHandler, kefuService service.KefuService) *KefuHandler {
	return &KefuHandler{
		BaseHandler: handler,
		kefuService: kefuService,
	}
}

func (slf *KefuHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	required.POST("/kefu/qw/qrcode", slf.KefuQrcode)
	required.POST("/kefu/qw/qrcode/vip", slf.KefuVipQrcode)
}

func (slf *KefuHandler) KefuQrcode(ctx *gin.Context) {
	// 联系我：每个用户一个链接。
	slf.Reply(ctx, "")
}

func (slf *KefuHandler) KefuVipQrcode(ctx *gin.Context) {
	// 获客链接：共用链接，传递参数绑定。
	slf.Reply(ctx, "")
}
