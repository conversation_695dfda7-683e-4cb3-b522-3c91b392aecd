package master

import (
	"github.com/gin-gonic/gin"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type KefuHandler struct {
	*handler.BaseHandler
	kefuService service.AppUserKefuService
}

func NewKefuHandler(handler *handler.BaseHandler, kefuService service.AppUserKefuService) *KefuHandler {
	return &KefuHandler{
		BaseHandler: handler,
		kefuService: kefuService,
	}
}

func (slf *KefuHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	required.POST("/kefu/qw", slf.QwKefu)
	required.POST("/kefu/qw/vip", slf.QwKefuVip)
}

// QwKefu godoc
// @Summary 企微客服（二维码）
// @Schemes
// @Description 企微客服（二维码）
// @Tags 客服
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.QwKefuRequest true "params"
// @Success 200 {object} v1.QwKefuResponse
// @Router /kefu/qw [post]
func (slf *KefuHandler) QwKefu(ctx *gin.Context) {
	var req v1.QwKefuRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.Auth = slf.Auth(ctx)
	res, err := slf.kefuService.Qrcode(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// QwKefuVip godoc
// @Summary 会员专属企微客服（获客链接）
// @Schemes
// @Description 会员专属企微客服（获客链接）
// @Tags 客服
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.QwKefuVipRequest true "params"
// @Success 200 {object} v1.QwKefuVipResponse
// @Router /kefu/qw/vip [post]
func (slf *KefuHandler) QwKefuVip(ctx *gin.Context) {
	var req v1.QwKefuVipRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.Auth = slf.Auth(ctx)
	res, err := slf.kefuService.VipQrcode(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
