package master

import (
	"github.com/gin-gonic/gin"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type KefuHandler struct {
	*handler.BaseHandler
	kefuService service.KefuService
}

func NewKefuHandler(handler *handler.BaseHandler, kefuService service.KefuService) *KefuHandler {
	return &KefuHandler{
		BaseHandler: handler,
		kefuService: kefuService,
	}
}

func (slf *KefuHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/kefu/qrcode", slf.KefuQrcode)
}

func (slf *KefuHandler) KefuQrcode(ctx *gin.Context) {

}
