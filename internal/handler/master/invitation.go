package master

import (
	"github.com/gin-gonic/gin"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type AppUserInvitationHandler struct {
	*handler.BaseHandler
	invitationService service.AppUserInvitationService
}

func NewAppUserInvitationHandler(baseHandler *handler.BaseHandler, invitationService service.AppUserInvitationService) *AppUserInvitationHandler {
	return &AppUserInvitationHandler{
		BaseHandler:       baseHandler,
		invitationService: invitationService,
	}
}

func (slf *AppUserInvitationHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	optional.POST("/invite/binding-ip", slf.InviteBindingIP)
	required.POST("/invite/code", slf.InviteCode)
	required.POST("/invite/accept", slf.AcceptInviteCode)
	required.POST("/invite/referrals", slf.GetInviteReferrals)
	required.POST("/invite/referrer", slf.GetInviteReferrer)
	required.POST("/invite/reward/popups/unread", slf.GetRewardUnreadPopups)
}

// GetRewardUnreadPopups godoc
// @Summary 未读弹窗
// @Description 未读弹窗
// @Tags 邀请推广
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.GetInviteRewardUnreadPopupsRequest true "params"
// @Success 200 {object} v1.GetInviteRewardUnreadPopupsResponse
// @Router /invite/reward/popups/unread [post]
func (slf *AppUserInvitationHandler) GetRewardUnreadPopups(ctx *gin.Context) {
	var req v1.GetInviteRewardUnreadPopupsRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	req.AppClient.BindClient(ctx)
	data, err := slf.invitationService.GetRewardUnreadPopups(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// InviteBindingIP godoc
// @Summary 渠道码绑定IP
// @Description 渠道码绑定IP
// @Tags 邀请推广
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param Channel header string true "AE86"
// @Param request body v1.InviteBindingIPRequest true "params"
// @Success 200 {object} v1.InviteBindingIPResponse
// @Router /invite/binding-ip [post]
func (slf *AppUserInvitationHandler) InviteBindingIP(ctx *gin.Context) {
	var req v1.InviteBindingIPRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	req.BindClient(ctx)
	if err := slf.invitationService.InviteBindingIP(ctx, &req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// InviteCode godoc
// @Summary 个人邀请码
// @Description 个人邀请码
// @Tags 邀请推广
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param Application header string true "application_horoscope"
// @Param request body v1.GetInviteCodeRequest true "params"
// @Success 200 {object} v1.GetInvitationCodeResponse
// @Router /invite/code [post]
func (slf *AppUserInvitationHandler) InviteCode(ctx *gin.Context) {
	var req v1.GetInviteCodeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	data, err := slf.invitationService.GetInviteCode(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// AcceptInviteCode godoc
// @Summary 接受邀请
// @Description 接受邀请
// @Tags 邀请推广
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param Application header string true "application_horoscope"
// @Param request body v1.AcceptInvitationRequest true "params"
// @Success 200 {object} v1.AcceptInvitationResponse
// @Router /invite/accept [post]
func (slf *AppUserInvitationHandler) AcceptInviteCode(ctx *gin.Context) {
	var req v1.AcceptInvitationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	data, err := slf.invitationService.AcceptInvitation(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// GetInviteReferrals godoc
// @Summary 邀请记录
// @Description 邀请记录
// @Tags 邀请推广
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param Application header string true "application_horoscope"
// @Param request body v1.GetInviteReferralsRequest true "params"
// @Success 200 {object} v1.GetInviteReferralsResponse
// @Router /invite/referrals [post]
func (slf *AppUserInvitationHandler) GetInviteReferrals(ctx *gin.Context) {
	var req v1.GetInviteReferralsRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	req.Param.User = slf.Auth(ctx)
	data, err := slf.invitationService.GetInviteReferrals(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, data)
}

// GetInviteReferrer godoc
// @Summary 邀请人
// @Description 邀请人
// @Tags 邀请推广
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param Application header string true "application_horoscope"
// @Param request body v1.GetInviteReferrerRequest true "params"
// @Success 200 {object} v1.GetInviteReferrerResponse
// @Router /invite/referrer [post]
func (slf *AppUserInvitationHandler) GetInviteReferrer(ctx *gin.Context) {
	var req v1.GetInviteReferrerRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	data, err := slf.invitationService.GetInviteReferrer(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, data)
}
