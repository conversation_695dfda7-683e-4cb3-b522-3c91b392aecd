package authcenter

import (
	"github.com/gin-gonic/gin"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type AuthCenterHandler struct {
	*handler.BaseHandler
	auService service.AuthService
}

func NewAuthHandler(
	handler *handler.BaseHandler,
	auService service.AuthService,
) *AuthCenterHandler {
	return &AuthCenterHandler{
		BaseHandler: handler,
		auService:   auService,
	}
}

// Handle 说明：历史遗留原因，此处路由命名及其杂乱，mad！
func (slf *AuthCenterHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/user/getImageCode", slf.GenImageCode)               // 生成图片验证码
	nameless.POST("/user/smsAuth", slf.SMSAuth)                         // 发送短信验证码
	nameless.POST("/user/superLogin", slf.LoginByPhone)                 // 短信验证码登录
	nameless.POST("/user/phoneLogin", slf.LoginByPhoneQuickly4Uni)      // 手机号一键登录（uniapp）
	required.POST("/user/getUserInfoByToken", slf.GetUserInfo)          // 获取用户信息：基础信息、会员及付费信息
	nameless.POST("/user/loginByWechat", slf.LoginByWechatMiniProgram)  // 微信小程序登录
	required.POST("/user/wxLogin", slf.WechatMiniProgramJsCode2Session) // 微信js_code换取session（小程序）
	optional.POST("/user/wx/code2session", slf.WechatJsCode2Session)    // 微信js_code换取session（小程序/服务号）
	required.POST("/user/deactivate", slf.Deactivate)                   // 注销
	nameless.POST("/user/login", slf.AdminLogin)                        // 管理后台登录

	nameless.POST("/auth/login/umeng/mobile", slf.LoginByUMengMobileInfo)
}

// LoginByUMengMobileInfo godoc
// @Summary 友盟一键登录
// @Schemes
// @Description 友盟一键登录
// @Tags auth
// @Produce json
// @Param request body v1.LoginByUMengMobileInfoRequest true "params"
// @Success 200 {object} v1.LoginByUMengMobileInfoResponse
// @Router /auth/login/umeng/mobile [post]
func (slf *AuthCenterHandler) LoginByUMengMobileInfo(ctx *gin.Context) {
	var req v1.LoginByUMengMobileInfoRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.BindClient(ctx)
	res, err := slf.auService.LoginByUMengMobileInfo(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// AdminLogin godoc
// @Summary 管理后台登录
// @Schemes
// @Description 管理后台登录
// @Tags auth
// @Produce json
// @Param request body v1.AdminLoginRequest true "params"
// @Success 200 {object} v1.AdminLoginResponse
// @Router /user/login [post]
func (slf *AuthCenterHandler) AdminLogin(ctx *gin.Context) {
	var req v1.AdminLoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.auService.AdminLogin(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Deactivate godoc
// @Summary 注销
// @Schemes
// @Description 注销
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Param request body v1.DeactivateRequest true "params"
// @Success 200 {object} v1.DeactivateResponse
// @Router /user/deactivate [post]
func (slf *AuthCenterHandler) Deactivate(ctx *gin.Context) {
	var req v1.DeactivateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	if err := slf.auService.Deactivate(ctx, &req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// WechatJsCode2Session godoc
// @Summary 微信js_code换取session（小程序/服务号）
// @Schemes
// @Description 微信js_code换取session（小程序/服务号）
// @Tags auth
// @Produce json
// @Param request body v1.WechatJsCode2SessionRequest true "params"
// @Success 200 {object} v1.WechatCode2SessionResponse
// @Router /user/wx/code2session [post]
func (slf *AuthCenterHandler) WechatJsCode2Session(ctx *gin.Context) {
	var req v1.WechatJsCode2SessionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.Application = slf.Application(ctx)
	req.User = slf.Auth(ctx)
	res, err := slf.auService.WechatJsCode2Session(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// WechatMiniProgramJsCode2Session godoc
// @Summary 微信小程序code2session
// @Schemes
// @Description 微信小程序code2session
// @Tags auth
// @Produce json
// @Param request body v1.WechatMiniProgramJsCode2SessionRequest true "params"
// @Success 200 {object} v1.WechatMiniProgramJsCode2SessionResponse
// @Router /user/wxLogin [post]
func (slf *AuthCenterHandler) WechatMiniProgramJsCode2Session(ctx *gin.Context) {
	var req v1.WechatMiniProgramJsCode2SessionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.UserID = slf.Auth(ctx)
	if err := slf.auService.WechatMiniProgramJsCode2Session(ctx, &req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// LoginByWechatMiniProgram godoc
// @Summary 微信小程序登录
// @Schemes
// @Description 微信小程序登录
// @Tags auth
// @Produce json
// @Param request body v1.LoginByWechatMiniProgramRequest true "params"
// @Success 200 {object} v1.LoginByWechatMiniProgramResponse
// @Router /user/loginByWechat [post]
func (slf *AuthCenterHandler) LoginByWechatMiniProgram(ctx *gin.Context) {
	var req v1.LoginByWechatMiniProgramRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.BindClient(ctx)
	res, err := slf.auService.LoginByWechatMiniProgram(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// LoginByPhoneQuickly4Uni godoc
// @Summary 手机号快速登录/一键登录（uniapp）
// @Schemes
// @Description 手机号快速登录/一键登录（uniapp）
// @Tags auth
// @Produce json
// @Param request body v1.LoginByPhoneQuickly4UniRequest true "params"
// @Success 200 {object} v1.LoginByPhoneQuickly4UniResponse
// @Router /user/phoneLogin [post]
func (slf *AuthCenterHandler) LoginByPhoneQuickly4Uni(ctx *gin.Context) {
	var req v1.LoginByPhoneQuickly4UniRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.BindClient(ctx)
	res, err := slf.auService.LoginByPhoneQuickly4Uni(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// LoginByPhone godoc
// @Summary 短信验证码登录
// @Schemes
// @Description 短信验证码登录
// @Tags auth
// @Produce json
// @Param request body v1.LoginByPhoneRequest true "params"
// @Success 200 {object} v1.LoginByPhoneResponse
// @Router /user/superLogin [post]
func (slf *AuthCenterHandler) LoginByPhone(ctx *gin.Context) {
	var req v1.LoginByPhoneRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.BindClient(ctx)
	res, err := slf.auService.LoginByPhone(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// SMSAuth godoc
// @Summary 发送短信验证码
// @Schemes
// @Description 发送短信验证码
// @Tags auth
// @Produce json
// @Param request body v1.SMSAuthRequest true "params"
// @Success 200 {object} v1.SMSAuthResponse
// @Router /user/smsAuth [post]
func (slf *AuthCenterHandler) SMSAuth(ctx *gin.Context) {
	var req v1.SMSAuthRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.Application = slf.Application(ctx)
	data, err := slf.auService.SMSAuth(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// GenImageCode godoc
// @Summary 生成图片验证码
// @Schemes
// @Description 生成图片验证码
// @Tags auth
// @Produce json
// @Param request body v1.GenImageCodeRequest true "params"
// @Success 200 {object} v1.GenImageCodeResponse
// @Router /user/getImageCode [post]
func (slf *AuthCenterHandler) GenImageCode(ctx *gin.Context) {
	var req v1.GenImageCodeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.auService.GenImageCode(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// GetUserInfo godoc
// @Summary 通过Token获取用户信息
// @Schemes
// @Description 通过Token获取用户信息
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Param request body v1.GetUserInfoRequest true "params"
// @Success 200 {object} v1.GetUserInfoResponse
// @Router /user/getUserInfoByToken [post]
func (slf *AuthCenterHandler) GetUserInfo(ctx *gin.Context) {
	var req v1.GetUserInfoRequest
	/*
		// 前端未传空对象导致EOF
		if err := ctx.ShouldBindJSON(&req); err != nil {
			slf.Reply(ctx, err)
			return
		}
	*/
	req.User = slf.Auth(ctx)
	req.Application = slf.Application(ctx)
	res, err := slf.auService.GetUserInfo(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
