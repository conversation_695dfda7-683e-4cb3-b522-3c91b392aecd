package cms

import (
	"fmt"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"

	"github.com/gin-gonic/gin"
)

type UserOrderHandler struct {
	*handler.BaseHandler
	userOrderService service.UserOrderService
}

func NewUserOrderHandler(
	handler *handler.BaseHandler,
	orderService service.UserOrderService,
) *UserOrderHandler {
	return &UserOrderHandler{
		BaseHandler:      handler,
		userOrderService: orderService,
	}
}

func (slf *UserOrderHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	required.
		POST("/order/pageList", slf.PageListOrder)
	optional.POST("/order/detail", slf.OrderDetail)
}

// PageListOrder godoc
// @Summary 分页查询用户订单
// @Schemes
// @Description 分页查询用户订单
// @Tags 用户订单
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.PageListUserOrderRequest true "params"
// @Success 200 {object} v1.PageListUserOrderResponse
// @Router /order/pageList [post]
func (slf *UserOrderHandler) PageListOrder(ctx *gin.Context) {
	var req v1.PageListUserOrderRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	data, err := slf.userOrderService.PageListOrder(ctx, &req)
	if err != nil {
		err = fmt.Errorf("userOrderService.PageListOrder: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// OrderDetail 获取订单详情
// @Summary 获取订单详情
// @Description 获取订单详情
// @Tags 订单
// @Accept json
// @Produce json
// @Param request body v1.OrderDetailRequest true "获取订单详情请求"
// @Success 200 {object} v1.OrderDetailResponse "获取订单详情成功"
// @Router /order/detail [post]
func (slf *UserOrderHandler) OrderDetail(ctx *gin.Context) {
	req := &v1.OrderDetailRequest{}
	if err := ctx.ShouldBindJSON(req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.userOrderService.OrderDetail(ctx, req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
