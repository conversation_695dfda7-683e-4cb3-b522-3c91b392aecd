package cms

import (
	"github.com/ArtisanCloud/PowerLibs/v3/http/helper"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

// WecomHandler 企业微信
type WecomHandler struct {
	*handler.BaseHandler
	wecomService service.WecomService
}

func NewWecomHandler(handler *handler.BaseHandler, wecomService service.WecomService) *WecomHandler {
	return &WecomHandler{
		BaseHandler:  handler,
		wecomService: wecomService,
	}
}

func (slf *WecomHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.
		GET("/wecom/doraemon", slf.DoraemonVerify).
		POST("/wecom/doraemon", slf.DoraemonNotify)

	required.
		POST("/qw/departments", slf.Departments).
		POST("/qw/contactWay/create", slf.CreateContactWay).
		POST("/qw/contactWay/update", slf.UpdateContactWay).
		POST("/qw/contactWay/delete", slf.DeleteContactWay).
		POST("/qw/contactWay/pageList", slf.PageListContactWay)

	required.
		POST("/qw/bill/pageList", slf.PageListBill)

	required.
		POST("/qw/contact/follow/pageList", slf.PageListContactFollow).
		POST("/qw/contact/follow/users", slf.ContactFollowUsers).
		POST("/qw/contact/update", slf.UpdateContact)

	required.POST("/qw/acquisitionLink/create", slf.CreateAcquisitionLink)
}

// DoraemonVerify 企微自建应用《哆啦A梦》验证回调
func (slf *WecomHandler) DoraemonVerify(ctx *gin.Context) {
	rs, err := slf.wecomService.DoraemonVerify(ctx.Request)
	if err != nil {
		slf.Log(ctx).Error("企业微信处理回调验证失败", zap.String("应用", "doraemon"), zap.Error(err))
		_ = ctx.Error
		return
	}
	if err = helper.HttpResponseSend(rs, ctx.Writer); err != nil {
		slf.Log(ctx).Error("企业微信处理回调验证失败", zap.String("应用", "doraemon"), zap.Error(err))
		_ = ctx.Error(err)
		return
	}
}

// DoraemonNotify 企微自建应用《哆啦A梦》通知回调
func (slf *WecomHandler) DoraemonNotify(ctx *gin.Context) {
	rs, err := slf.wecomService.DoraemonNotify(ctx.Request)
	if err != nil {
		slf.Log(ctx).Error("企业微信处理回调通知失败", zap.String("应用", "doraemon"), zap.Error(err))
		_ = ctx.Error(err)
		return
	}
	if err = helper.HttpResponseSend(rs, ctx.Writer); err != nil {
		slf.Log(ctx).Error("企业微信处理回调通知失败", zap.String("应用", "doraemon"), zap.Error(err))
		_ = ctx.Error(err)
		return
	}
}

// Departments godoc
// @Summary 获取企业微信部门列表
// @Description 获取企业微信部门列表
// @Tags 企业微信
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param departments body v1.QwDepartmentsRequest true "请求参数"
// @Success 200 {object} v1.QwDepartmentsResponse "成功返回数据"
// @Router /qw/departments [post]
func (slf *WecomHandler) Departments(ctx *gin.Context) {
	var req v1.QwDepartmentsRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	data, err := slf.wecomService.Departments(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// CreateContactWay godoc
// @Summary 配置客户联系「联系我」方式
// @Description 配置客户联系「联系我」方式
// @Tags 企业微信
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param addContactWay body v1.QwCreateContactWayRequest true "请求参数"
// @Success 200 {object} v1.QwCreateContactWayResponse "成功返回数据"
// @Router /qw/contactWay/create [post]
func (slf *WecomHandler) CreateContactWay(ctx *gin.Context) {
	var req v1.QwCreateContactWayRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	data, err := slf.wecomService.CreateContactWay(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// CreateAcquisitionLink godoc
// @Summary 创建获客链接
// @Description 创建获客链接
// @Tags 企业微信
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param createAcquisitionLink body v1.QwCreateAcquisitionLinkRequest true "请求参数"
// @Success 200 {object} v1.QwCreateAcquisitionLinkResponse "成功返回数据"
// @Router /qw/acquisitionLink/create [post]
func (slf *WecomHandler) CreateAcquisitionLink(ctx *gin.Context) {
	var req v1.QwCreateAcquisitionLinkRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	data, err := slf.wecomService.CreateAcquisitionLink(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// UpdateContactWay godoc
// @Summary 更新客户联系「联系我」方式
// @Description 更新客户联系「联系我」方式
// @Tags 企业微信
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param updateContactWay body v1.QwUpdateContactWayRequest true "请求参数"
// @Success 200 {object} v1.QwUpdateContactWayResponse "成功返回数据"
// @Router /qw/contactWay/update [post]
func (slf *WecomHandler) UpdateContactWay(ctx *gin.Context) {
	var req v1.QwUpdateContactWayRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	if err := slf.wecomService.UpdateContactWay(ctx, &req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// DeleteContactWay godoc
// @Summary 删除客户联系「联系我」方式
// @Description 删除客户联系「联系我」方式
// @Tags 企业微信
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param deleteContactWay body v1.QwDeleteContactWayRequest true "请求参数"
// @Success 200 {object} v1.QwDeleteContactWayResponse "成功返回数据"
// @Router /qw/contactWay/delete [post]
func (slf *WecomHandler) DeleteContactWay(ctx *gin.Context) {
	var req v1.QwDeleteContactWayRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	if err := slf.wecomService.DeleteContactWay(ctx, &req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// PageListContactWay godoc
// @Summary 分页查询客户联系「联系我」方式
// @Description 分页查询客户联系「联系我」方式
// @Tags 企业微信
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param pageListContactWay body v1.QwPageListContactWayRequest true "请求参数"
// @Success 200 {object} v1.QwPageListContactWayResponse "成功返回数据"
// @Router /qw/contactWay/pageList [post]
func (slf *WecomHandler) PageListContactWay(ctx *gin.Context) {
	var req v1.QwPageListContactWayRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	data, err := slf.wecomService.PageListContactWay(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// PageListBill godoc
// @Summary 分页查询企微收款记录
// @Description 分页查询企微收款记录
// @Tags 企业微信
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param pageListBill body v1.QwPageListBillRequest true "请求参数"
// @Success 200 {object} v1.QwPageListBillResponse "成功返回数据"
// @Router /qw/bill/pageList [post]
func (slf *WecomHandler) PageListBill(ctx *gin.Context) {
	var req v1.QwPageListBillRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	data, err := slf.wecomService.PageListBill(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// PageListContactFollow godoc
// @Summary 分页查询客户跟进记录
// @Description 分页查询客户跟进记录
// @Tags 企业微信
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param pageListContactFollow body v1.QwPageListContactFollowRequest true "请求参数"
// @Success 200 {object} v1.QwPageListContactFollowResponse "成功返回数据"
// @Router /qw/contact/follow/pageList [post]
func (slf *WecomHandler) PageListContactFollow(ctx *gin.Context) {
	var req v1.QwPageListContactFollowRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	data, err := slf.wecomService.PageListContactFollow(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// ContactFollowUsers godoc
// @Summary 获取客户跟进人员列表
// @Description 获取客户跟进人员列表
// @Tags 企业微信
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param contactFollowUsers body v1.QwContactFollowUsersRequest true "请求参数"
// @Success 200 {object} v1.QwContactFollowUsersResponse "成功返回数据"
// @Router /qw/contact/follow/users [post]
func (slf *WecomHandler) ContactFollowUsers(ctx *gin.Context) {
	var req v1.QwContactFollowUsersRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	data, err := slf.wecomService.ContactFollowUsers(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// UpdateContact godoc
// @Summary 更新客户信息
// @Description 更新客户信息
// @Tags 企业微信
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param updateContact body v1.QwUpdateContactRequest true "请求参数"
// @Success 200 {object} v1.QwUpdateContactResponse "成功返回数据"
// @Router /qw/contact/update [post]
func (slf *WecomHandler) UpdateContact(ctx *gin.Context) {
	var req v1.QwUpdateContactRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	if err := slf.wecomService.UpdateContact(ctx, &req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}
