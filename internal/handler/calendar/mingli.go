package calendar

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type UserMingliHandler struct {
	*handler.BaseHandler
	userMingliService service.UserMingliService
}

func NewUserMingliHandler(
	handler *handler.BaseHandler,
	userMingliService service.UserMingliService,
) *UserMingliHandler {
	return &UserMingliHandler{
		BaseHandler:       handler,
		userMingliService: userMingliService,
	}
}

func (slf *UserMingliHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	required.POST("/userMingli/create", slf.CreateUserMingli).
		POST("/userMingli/update", slf.UpdateUserMingli).
		POST("/userMingli/delete", slf.DeleteUserMingli).
		POST("/userMingli/setDefault", slf.SetDefaultUserMingli).
		POST("/userMingli/setWuxing", slf.SetMingliWuxing).
		POST("/userMingli/list", slf.ListUserMingli).
		POST("/userMingli/paipan", slf.Paipan).
		POST("/userMingli/yunScore", slf.DayunLiunianScore).
		POST("/userMingli/jieqiScore", slf.JieqiScore)

}

// Paipan godoc
// @Summary 八字排盘
// @Schemes
// @Description 八字排盘
// @Tags 命例
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.PaipanRequest true "params"
// @Success 200 {object} v1.PaipanResponse
// @Router /userMingli/paipan [post]
func (slf *UserMingliHandler) Paipan(ctx *gin.Context) {
	var req v1.PaipanRequest
	req.User = slf.Auth(ctx)
	req.UserAgent = ctx.GetHeader("Auth-Agent")
	req.IP = ctx.ClientIP()
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.userMingliService.Paipan(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("userMingliService.Paipan", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// CreateUserMingli godoc
// @Summary 创建命例
// @Schemes
// @Description 创建命例
// @Tags 命例
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.CreateUserMingliRequest true "params"
// @Success 200 {object} v1.CreateUserMingliResponse
// @Router /userMingli/create [post]
func (slf *UserMingliHandler) CreateUserMingli(ctx *gin.Context) {
	var req v1.CreateUserMingliRequest
	req.User = slf.Auth(ctx)
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.AppID = 3
	res, err := slf.userMingliService.CreateUserMingli(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("userMingliService.CreateUserMingli", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// UpdateUserMingli godoc
// @Summary 更新命例
// @Schemes
// @Description 更新命例
// @Tags 命例
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.UpdateUserMingliRequest true "params"
// @Success 200 {object} v1.UpdateUserMingliResponse
// @Router /userMingli/update [post]
func (slf *UserMingliHandler) UpdateUserMingli(ctx *gin.Context) {
	var req v1.UpdateUserMingliRequest
	req.User = slf.Auth(ctx)
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	err := slf.userMingliService.UpdateUserMingli(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("userMingliService.UpdateUserMingli", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// DeleteUserMingli godoc
// @Summary 删除命例
// @Schemes
// @Description 删除命例
// @Tags 命例
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.DeleteUserMingliRequest true "params"
// @Success 200 {object} v1.DeleteUserMingliResponse
// @Router /userMingli/delete [post]
func (slf *UserMingliHandler) DeleteUserMingli(ctx *gin.Context) {
	var req v1.DeleteUserMingliRequest
	req.User = slf.Auth(ctx)
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	if err := slf.userMingliService.DeleteUserMingli(ctx, &req); err != nil {
		slf.Log(ctx).Error("userMingliService.DeleteUserMingli", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// SetDefaultUserMingli godoc
// @Summary 设置默认命例
// @Schemes
// @Description 设置默认命例
// @Tags 命例
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.SetDefaultUserMingliRequest true "params"
// @Success 200 {object} v1.SetDefaultUserMingliResponse
// @Router /userMingli/setDefault [post]
func (slf *UserMingliHandler) SetDefaultUserMingli(ctx *gin.Context) {
	var req v1.SetDefaultUserMingliRequest
	req.User = slf.Auth(ctx)
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	err := slf.userMingliService.SetDefaultMingli(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("userMingliService.SetDefaultMingli", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// SetMingliWuxing godoc
// @Summary 设置命例五行
// @Schemes
// @Description 设置命例五行
// @Tags 命例
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.SetMingliWuxingRequest true "params"
// @Success 200 {object} v1.SetMingliWuxingResponse
// @Router /userMingli/setWuxing [post]
func (slf *UserMingliHandler) SetMingliWuxing(ctx *gin.Context) {
	var req v1.SetMingliWuxingRequest
	req.User = slf.Auth(ctx)
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	if err := slf.userMingliService.SetMingliWuxing(ctx, &req); err != nil {
		slf.Log(ctx).Error("userMingliService.SetMingliWuxing", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// ListUserMingli godoc
// @Summary 获取命例列表
// @Schemes
// @Description 获取命例列表
// @Tags 命例
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.ListUserMingliRequest true "params"
// @Success 200 {object} v1.ListUserMingliResponse
// @Router /userMingli/list [post]
func (slf *UserMingliHandler) ListUserMingli(ctx *gin.Context) {
	var req v1.ListUserMingliRequest
	req.User = slf.Auth(ctx)
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.AppID = 3
	req.GroupID = 0
	res, err := slf.userMingliService.ListUserMingli(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("userMingliService.ListUserMingli", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// DayunLiunianScore godoc
// @Summary 获取大运流年得分
// @Schemes
// @Description 获取大运流年得分
// @Tags 命例
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.MingliDayunliulianScoreRequest true "params"
// @Success 200 {object} v1.MingliDayunliulianScoreResponse
// @Router /userMingli/yunScore [post]
func (slf *UserMingliHandler) DayunLiunianScore(ctx *gin.Context) {
	var req v1.MingliDayunliulianScoreRequest
	req.User = slf.Auth(ctx)
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.userMingliService.DayunLiunianScore(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("userMingliService.DayunLiunianScore", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// JieqiScore godoc
// @Summary 获取节气得分
// @Schemes
// @Description 获取节气得分
// @Tags 命例
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.MingliJieqiScoreRequest true "params"
// @Success 200 {object} v1.MingliJieqiScoreResponse
// @Router /userMingli/jieqiScore [post]
func (slf *UserMingliHandler) JieqiScore(ctx *gin.Context) {
	var req v1.MingliJieqiScoreRequest
	req.User = slf.Auth(ctx)
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.userMingliService.JieqiScore(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("userMingliService.JieqiScore", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
