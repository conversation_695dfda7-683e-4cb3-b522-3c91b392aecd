package wealth

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type DateTimeHandler struct {
	*handler.BaseHandler
	datetimeService service.DatetimeService
}

func NewDateTimeHandler(
	baseHandler *handler.BaseHandler,
	datesubService service.DatetimeService,
) *DateTimeHandler {
	return &DateTimeHandler{
		BaseHandler:     baseHandler,
		datetimeService: datesubService,
	}
}

func (h *DateTimeHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/datetime/fromSizhu", h.FromSizhu)
	nameless.POST("/datetime/bazi", h.Bazi)
}

// Bazi godoc
// @Summary 八字
// @Schemes
// @Description 八字
// @Tags 时间
// @Accept json
// @Produce json
// @Param request body v1.GetBaziRequest true "params"
// @Success 200 {object} v1.GetBaziResponse
// @Router /datetime/bazi [post]
func (h *DateTimeHandler) Bazi(ctx *gin.Context) {
	var req v1.GetBaziRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	res, err := h.datetimeService.Bazi(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("datetimeService.Bazi", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}

// FromSizhu godoc
// @Summary 从四柱获取时间
// @Schemes
// @Description 从四柱获取时间
// @Tags 时间
// @Accept json
// @Produce json
// @Param request body v1.GetDatetimeBySizhuRequest true "params"
// @Success 200 {object} v1.GetDatetimeBySizhuResponse
// @Router /datetime/fromSizhu [post]
func (h *DateTimeHandler) FromSizhu(ctx *gin.Context) {
	var req v1.GetDatetimeBySizhuRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	res, err := h.datetimeService.FromSizhu(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("datetimeService.FromSizhu", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}
