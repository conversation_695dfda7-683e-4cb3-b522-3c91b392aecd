package service

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"sort"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/pkg/array"
	"zodiacus/third_party/corona"
)

// GaoKaoPreference 高考志愿
func (slf *masterService) GaoKaoPreference(ctx context.Context, req *v1.GaoKaoPreferenceRequest) (*v1.GaoKaoPreferenceResponseData, error) {
	startTime, err := time.Parse("2006-01-02 15:04:05", req.StartTime)
	if err != nil {
		return nil, v1.ErrBadRequest
	}
	endTime, err := time.Parse("2006-01-02 15:04:05", req.EndTime)
	if err != nil {
		return nil, v1.ErrBadRequest
	}
	if startTime.Format("2006-01-02") != endTime.Format("2006-01-02") {
		return nil, v1.ErrBadRequest
	}
	dueTime, err := time.Parse("2006-01-02 15:04:05", fmt.Sprintf("%d-08-15 23:59:59", startTime.Year()))
	if err != nil {
		return nil, v1.ErrBadRequest
	}
	if endTime.After(dueTime) || startTime.Before(time.Now()) || startTime.After(endTime) {
		return nil, v1.ErrBadRequest
	}
	var resp = v1.GaoKaoPreferenceResponseData{
		SystemTip: "不管对玄学风水、八字命理、黄道吉日的看法如何，人们总愿意图个吉利。这里我们根据流年、月、日、时的干支五行能量的影响，计算了一些建议的填报志愿的日期。图个吉利，供参考娱乐，切勿迷信。",
		SystemSay: "论玄说：全国填报志愿的地区、批次不同，请输入您填报志愿时间，我们会计算出您具体时间段的运势情况，给予您参考。",
	}
	if req.Birthplace == nil {
		req.Birthplace = []string{}
	}
	if _, err := time.Parse("2006-01-02 15:04:05", req.Birthtime); err != nil {
		return nil, errors.Wrap(err, "time.Parse")
	}
	realSunTimeStr, err := slf.realSunTime(ctx, req.Birthtime, req.Birthplace)
	if err != nil {
		return nil, errors.Wrap(err, "realSunTime")
	}
	paipanAll, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: realSunTimeStr,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.PaipanAll")
	}
	day, err := slf.dateRepo.GetOneDay(ctx, startTime.Format("2006-01-02"))
	if err != nil {
		return nil, err
	}
	if day == nil {
		return nil, v1.ErrBadRequest
	}
	var (
		rigan, rizhi             = string([]rune(day.Bazi3)[0]), string([]rune(day.Bazi3)[1])
		tgWX, dzWX               = slf.GetWuxingByTiangan(rigan), slf.GetWuxingByDizhi(rizhi)
		times                    []int // 时辰集合
		yong, xi, chou, ji, xian = paipanAll.YXCJX[0], paipanAll.YXCJX[1], paipanAll.YXCJX[2], paipanAll.YXCJX[3], paipanAll.YXCJX[4]
		yongxi                   = []string{yong, xi}
		chouji                   = []string{chou, ji}
	)
	/*
		天干五行	地支五行	优先级
		用神	用神	1
		喜神	用神	2
		用神	喜神	3
		闲神	用神	4
		用神	闲神	5
		闲神	喜神	6
		喜神	闲神	7
		仇忌神	喜用神	8
		喜用神	仇忌神	9
		闲神	闲神	10
		仇忌神	闲神	11
		闲神	仇忌神	12
		仇忌神	仇忌神	13
	*/
	shichen := map[int][]int{
		1:  {0},
		2:  {1, 2},
		3:  {3, 4},
		4:  {5, 6},
		5:  {7, 8},
		6:  {9, 10},
		7:  {11, 12},
		8:  {13, 14},
		9:  {15, 16},
		10: {17, 18},
		11: {19, 20},
		12: {21, 22},
		13: {23},
	}
	match := func(sc int) (tmp []int) {
		hours := shichen[sc]
		start := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), hours[0], 0, 0, 0, time.UTC)
		end := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), hours[len(hours)-1], 59, 59, 0, time.UTC)
		if startTime.Unix() <= start.Unix() && endTime.Unix() >= start.Unix() {
			tmp = append(tmp, hours...)
		} else if startTime.Unix() <= start.Unix() && endTime.Unix() < end.Unix() && endTime.Unix() > start.Unix() {
			tmp = append(tmp, hours[0])
		} else if startTime.Unix() > start.Unix() && startTime.Unix() < end.Unix() && endTime.Unix() >= end.Unix() {
			tmp = append(tmp, hours[len(hours)-1])
		}
		return tmp
	}
	if tgWX == yong && dzWX == yong {
		times = append(times, match(1)...)
	}
	if tgWX == xi && dzWX == yong {
		times = append(times, match(2)...)
	}
	if tgWX == yong && dzWX == xi {
		times = append(times, match(3)...)
	}
	if tgWX == xian && dzWX == yong {
		times = append(times, match(4)...)
	}
	if tgWX == yong && dzWX == xian {
		times = append(times, match(5)...)
	}
	if tgWX == xian && dzWX == xi {
		times = append(times, match(6)...)
	}
	if tgWX == xi && dzWX == xian {
		times = append(times, match(7)...)
	}
	if array.Has(chouji, tgWX) && array.Has(yongxi, dzWX) {
		times = append(times, match(8)...)
	}
	if array.Has(yongxi, tgWX) && array.Has(chouji, dzWX) {
		times = append(times, match(9)...)
	}
	if tgWX == xian && dzWX == xian {
		times = append(times, match(10)...)
	}
	if array.Has(chouji, tgWX) && dzWX == xian {
		times = append(times, match(11)...)
	}
	if tgWX == xian && array.Has(chouji, dzWX) {
		times = append(times, match(12)...)
	}
	if array.Has(chouji, tgWX) && array.Has(chouji, dzWX) {
		times = append(times, match(13)...)
	}
	if len(times) > 0 {
		sort.Ints(times)
		resp.Times = times
		resp.TimeRanges = slf.GaoKaoTimeRangeByHour(ctx, startTime.Format("2006-01-02"), times)
	}
	return &resp, nil
}

func (slf *masterService) GaoKaoTimeRangeByHour(ctx context.Context, date string, hours []int) [][2]string {
	if len(hours) == 0 {
		return nil
	}
	sort.Ints(hours)
	var result [][2]string
	start := hours[0]
	prev := hours[0]
	for i := 1; i < len(hours); i++ {
		curr := hours[i]
		if curr != prev+1 {
			from := fmt.Sprintf("%s %02d:00:00", date, start)
			to := fmt.Sprintf("%s %02d:59:59", date, prev)
			result = append(result, [2]string{from, to})
			start = curr
		}
		prev = curr
	}
	from := fmt.Sprintf("%s %02d:00:00", date, start)
	to := fmt.Sprintf("%s %02d:59:59", date, prev)
	result = append(result, [2]string{from, to})
	return result
}

// GaoKao 高考
func (slf *masterService) GaoKao(ctx context.Context, req *v1.GaoKaoRequest) (*v1.GaoKaoResponseData, error) {
	var resp v1.GaoKaoResponseData
	resp.StartYear = req.StartYear
	if req.Birthplace == nil {
		req.Birthplace = []string{}
	}
	birthtime, err := time.Parse("2006-01-02 15:04:05", req.Birthtime)
	if err != nil {
		return nil, errors.Wrap(err, "time.Parse")
	}
	realSunTimeStr, err := slf.realSunTime(ctx, req.Birthtime, req.Birthplace)
	if err != nil {
		return nil, errors.Wrap(err, "realSunTime")
	}
	birthtimeSun, err := time.Parse("2006-01-02 15:04:05", realSunTimeStr)
	if err != nil {
		return nil, errors.Wrap(err, "time.Parse")
	}
	paipanAll, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: realSunTimeStr,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.PaipanAll")
	}

	//2.1 个人基础信息
	{
		resp.BasicInfo = &v1.GaoKaoResponseDataBasicInfo{
			Name:           req.Name,
			Gender:         req.Gender,
			Birthtime:      req.Birthtime,
			BirthtimeSolar: realSunTimeStr,
			BirthtimeLunar: paipanAll.Nongli,
			Birthplace:     req.Birthplace,
			Bazi:           paipanAll.BaziTupleNew[:4],
			Xiyong:         paipanAll.YXCJX,
			Power:          paipanAll.ShishenPower,
		}
	}
	//2.2 五行喜用——选行业
	{
		lcOccupation := slf.Helper.LuncaiOccupation(ctx, paipanAll)
		resp.WuxingXiyong = &v1.GaoKaoResponseDataWuxingXiyong{
			SystemTip:      "从八字的五行喜忌看职业，可用于参考了解到个人性格、禀赋、特长、爱好等等，也最擅长最愿意做哪种类型的事；因五行也分很多种，比如木属性，也分甲木、乙木、寅木、卯木，具象有大树、乔木、花草、藤蔓等等，故不表示从事上述行业或职业一定成功，详情可咨询专业人员。本结果仅供参考娱乐之用。",
			SystemSay:      "论玄说：选专业时，可充分考虑个人喜用的五行；但不必纠结于一定匹配行业，匹配上岗位也是可以的。在一个行业的不同的岗位，其工作性质对应的五行也是不一致的。比如张三喜用水，但进入了电力行业（偏火属），从事了管理工作，也是匹配了的。",
			Occupation:     lcOccupation.Occupation,
			MoreSuggestion: lcOccupation.MoreSuggestion,
		}
	}
	//2.3 十神天赋——看方向
	{
		lcTalent := slf.Helper.LuncaiTalent(ctx, paipanAll)
		resp.ShishenTianfu = &v1.GaoKaoResponseDataShishenTianfu{
			SystemTip:          "从八字的十神能量看个人的天赋，相对与五行的强环境属性，十神天赋更偏向于社会属性，更能展现出人在社会中的天赋属性。本部分结合命局十神能量的强旺与不足，阐述个人先天天赋，更好地为个人的学习成长、职业发展提供一定的参考，通过后天的选择与努力，达到预期。更多详尽内容可咨询专业人员。本结果仅供娱乐参考，切勿迷信。",
			SystemSay:          "论玄说：选专业时，可充分考虑个人的先天天赋与个人期望，认识并初步确定个人的学习成长及事业发展的方向，扬长避短，充分发挥个人天赋与资源的优势，巧妙绕开不删除的领域，规避需要较好毅力、耐心或积累沉淀的专业、岗位与职业。",
			StrongestShishen:   lcTalent.StrongestShishen,
			StrongestAbility:   lcTalent.StrongestAbility,
			SuggestedCareer:    lcTalent.SuggestedCareer,
			WeakestShishen:     lcTalent.WeakestShishen,
			WeakestSuperiority: lcTalent.WeakestSuperiority,
		}
	}
	//2.4 有利提升——选城市
	{
		var (
			yong, xi     = paipanAll.YXCJX[0], paipanAll.YXCJX[1]
			yongSS, xiSS = paipanAll.WuxingShishenMap[yong], paipanAll.WuxingShishenMap[xi]
		)
		resp.FavorableChoice = &v1.GaoKaoResponseDataFavorableChoice{
			SystemTip:       "完美无缺的八字是罕有的，找到八字的不足与缺陷，采取一定的措施补救，可达到一定的改善效果。主要是取八字喜用神，调整后天环境。以下内容是一些后天措施。通过一些选择，改造命运、趋吉避凶的事项，仅供参考娱乐，切勿迷信。",
			SystemSay:       "论玄说：选高校时，充分结合个人喜用，根据推荐的方位或地域，选择匹配的城市环境，可提升个人运势，有利个人学习成长及事业发展。综合性的大城市，虽有五行偏向，但也五行俱全，一定情况下也是能够符合个人喜用的。",
			ChoiceDirection: fmt.Sprintf("以出生地或居住地为中心，您的住屋、办公室、坐位、睡姿头部、求学、就业、求医等朝向%s、%s更为吉利", slf.wuxingEffects[yong].Fangwei, slf.wuxingEffects[xi].Fangwei),
			ChoiceArea:      fmt.Sprintf(`结合您的命理喜用五行及事业性格特点，推荐考虑在五行性质偏"%s"或"%s"的城市发展，或者考虑%s；%s，将更为吉利。`, yong, xi, slf.Helper.TblShishenProperty[yongSS].PreferredRegion, slf.Helper.TblShishenProperty[xiSS].PreferredRegion),
		}
	}
	// 2.5 未来大运——看发展
	if time.Now().After(paipanAll.QiYunTime) {
		stages := slf.GaoKaoDayunStage(paipanAll.DayunQishi, req.StartYear, 20, [2][]string{paipanAll.GetShishenDayunTgList, paipanAll.TianganBenqiSsList})
		for _, stage := range stages {
			for _, shishen := range stage.Shishen {
				shishen.SystemSay = slf.Helper.MapGaokaoSystemSayForShishen[shishen.Name]
				for _, shishenStage := range slf.Helper.MapGaokaoShishenStage[shishen.Name][stage.StateNum] {
					shishen.Items = append(shishen.Items, &v1.GaoKaoResponseDataFutureDayunStageShishenItem{
						TypeName: shishenStage.Type,
						Type: func() int {
							if shishenStage.Type == "特征" {
								return 1
							}
							if shishenStage.Type == "建议" {
								return 2
							}
							return 0
						}(),
						Content: shishenStage.Content,
					})
				}
			}
		}
		resp.FutureDayun = &v1.GaokaoResponseDataFutureDayun{
			SystemTip: "命理八字中大发展，一般会充分考虑大运的影响，通过五行能量变化影响学业、事业、情感、健康。提前洞察其特征，可助于把握机遇，规避风险，结合个人命理与家庭情况，合理科学规划人生发展。以下内容供参考娱乐，切勿迷信。",
			Stages:    stages,
		}
	}

	{
		var userID string
		if req.User != "" {
			userID = req.User
		}
		ret, _ := slf.irs.Query(req.IP)
		if _, err := slf.paipanRecordRepo.CreatePaipanRecord(ctx, &model.PaipanRecord{
			UserID:         userID,
			Name:           req.Name,
			Gender:         lo.Ternary(req.Gender == "男", 1, 2),
			Birthtime:      birthtime,
			BirthtimeSun:   birthtimeSun,
			BirthtimeLunar: paipanAll.Nongli,
			Bazi:           paipanAll.BaziTupleNew[:4],
			Birthplace:     req.Birthplace,
			Type:           1,
			SaveTime:       time.Now(),
			UserAgent:      req.UserAgent,
			IP:             ret.IP,
			Region:         ret.Region(),
			AppID:          7,
			AppPlatformID:  1,
			ExtraInfo: map[string]any{"gaokao": map[string]any{
				"startYear": req.StartYear,
			}},
		}); err != nil {
			return nil, err
		}
	}
	return &resp, nil
}

func (slf *masterService) GaoKaoDayunStage(dyStartYear, startYear, years int, ss [2][]string) []*v1.GaoKaoResponseDataFutureDayunStage {
	var result []*v1.GaoKaoResponseDataFutureDayunStage
	for i := 0; i < years; i += 5 {
		stageStart := startYear + i
		stageEnd := stageStart + 5 - 1
		var segments []*v1.GaoKaoResponseDataFutureDayunStageShishen
		for year := stageStart; year <= stageEnd; {
			yearOffset := year - dyStartYear
			if yearOffset < 0 || yearOffset >= 120 {
				break
			}
			dyIndex := yearOffset / 10
			dyFirstYear := dyStartYear + dyIndex*10
			for j := 0; j < 2; j++ {
				halfStart := dyFirstYear + j*5
				halfEnd := halfStart + 4
				if halfEnd < stageStart || halfStart > stageEnd {
					continue
				}
				segment := &v1.GaoKaoResponseDataFutureDayunStageShishen{
					DayunNum:  dyIndex + 1,
					StartYear: max(halfStart, stageStart),
					EndYear:   min(halfEnd, stageEnd),
					Half:      j + 1,
					Name:      ss[j][dyIndex],
				}
				segments = append(segments, segment)
			}
			year = dyFirstYear + 10
		}
		result = append(result, &v1.GaoKaoResponseDataFutureDayunStage{
			StateNum:  i/5 + 1,
			StartYear: stageStart,
			EndYear:   stageEnd,
			Shishen:   segments,
		})
	}
	return result
}
