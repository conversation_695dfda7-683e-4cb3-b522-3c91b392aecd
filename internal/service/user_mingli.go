package service

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"strings"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/third_party/corona"
)

type UserMingliService interface {
	CreateUserMingli(ctx context.Context, req *v1.CreateUserMingliRequest) (*v1.CreateUserMingliResponseData, error)
	UpdateUserMingli(ctx context.Context, req *v1.UpdateUserMingliRequest) error
	DeleteUserMingli(ctx context.Context, req *v1.DeleteUserMingliRequest) error
	SetDefaultMingli(ctx context.Context, req *v1.SetDefaultUserMingliRequest) error
	SetMingliWuxing(ctx context.Context, req *v1.SetMingliWuxingRequest) error
	ListUserMingli(ctx context.Context, req *v1.ListUserMingliRequest) (v1.ListUserMingliResponseData, error)
	PageListUserMingli(ctx context.Context, req *v1.PageListUserMingliRequest) (*v1.PageListUserMingliResponseData, error)
	Paipan(ctx context.Context, req *v1.PaipanRequest) (*v1.PaipanResponseData, error)
	DayunLiunianScore(ctx context.Context, req *v1.MingliDayunliulianScoreRequest) (*v1.MingliDayunliulianScoreResponseData, error)
	JieqiScore(ctx context.Context, req *v1.MingliJieqiScoreRequest) (*v1.MingliJieqiScoreResponseData, error)
}

func NewUserMingliService(
	service *Service,
	mingliRepo repository.UserMingliRepository,
	calendarRepo repository.DateRepository,
	userPaipanRecordRepo repository.UserPaipanRecordRepository,
	userMingliGroupRepo repository.UserMingliGroupRepository,
) UserMingliService {
	return &userMingliService{
		userMingliRepo:      mingliRepo,
		dateRepo:            calendarRepo,
		Service:             service,
		paipanRecordRepo:    userPaipanRecordRepo,
		userMingliGroupRepo: userMingliGroupRepo,
	}
}

type userMingliService struct {
	userMingliRepo      repository.UserMingliRepository
	dateRepo            repository.DateRepository
	paipanRecordRepo    repository.UserPaipanRecordRepository
	userMingliGroupRepo repository.UserMingliGroupRepository
	*Service
}

func (slf *userMingliService) PageListUserMingli(ctx context.Context, req *v1.PageListUserMingliRequest) (*v1.PageListUserMingliResponseData, error) {
	return slf.userMingliRepo.PageListUserMingli(ctx, req)
}

func (slf *userMingliService) DayunLiunianScore(ctx context.Context, req *v1.MingliDayunliulianScoreRequest) (*v1.MingliDayunliulianScoreResponseData, error) {
	mingli, err := slf.userMingliRepo.FetchUserMingli(ctx, req.User, req.MingliID)
	if err != nil {
		return nil, err
	}
	if mingli == nil {
		return nil, v1.ErrUserMingliNotFound
	}
	score, err := slf.coronaCli.GetDayunLiunianScore(ctx, &corona.GetDayunLiunianScoreRequest{
		Birthtime: mingli.Birthtime.Format("2006-01-02 15:04:05"),
		Gender:    lo.Ternary(mingli.Gender == 1, "男", "女"),
	})
	if err != nil {
		return nil, errors.Wrapf(err, "GetDayunLiunianScore")
	}
	return score, nil
}

func (slf *userMingliService) JieqiScore(ctx context.Context, req *v1.MingliJieqiScoreRequest) (*v1.MingliJieqiScoreResponseData, error) {
	mingli, err := slf.userMingliRepo.FetchUserMingli(ctx, req.User, req.MingliID)
	if err != nil {
		return nil, err
	}
	if mingli == nil {
		return nil, v1.ErrUserMingliNotFound
	}
	score, err := slf.coronaCli.GetJieqiScore(ctx, &corona.GetJieqiScoreRequest{
		Birthtime:   mingli.Birthtime.Format("2006-01-02 15:04:05"),
		Gender:      lo.Ternary(mingli.Gender == 1, "男", "女"),
		CurrentTime: req.Time,
	})
	if err != nil {
		return nil, errors.Wrapf(err, "GetJieqiScore")
	}
	return score, nil
}

func (slf *userMingliService) Paipan(ctx context.Context, req *v1.PaipanRequest) (*v1.PaipanResponseData, error) {
	mingli, err := slf.userMingliRepo.FetchUserMingli(ctx, req.User, req.MingliID)
	if err != nil {
		return nil, err
	}
	if mingli == nil {
		return nil, v1.ErrUserMingliNotFound
	}
	// 九柱
	detail, err := slf.coronaCli.PaipanJiuzhu(ctx, &corona.GetPaipanJiuizhuRequest{Birthday: mingli.Birthtime.Format("2006-01-02 15:04:05"), CurrentDay: req.Time, Gender: mingli.Gender})
	if err != nil {
		return nil, err
	}

	// 创建排盘记录
	ret, _ := slf.irs.Query(req.IP)
	if _, err = slf.paipanRecordRepo.CreatePaipanRecord(ctx, &model.PaipanRecord{
		UserID: func() string {
			if req.User != "" {
				return req.User
			} else {
				return ""
			}
		}(),
		Name:           mingli.Name,
		Gender:         mingli.Gender,
		Birthtime:      mingli.Birthtime,
		BirthtimeSun:   mingli.BirthtimeSun,
		BirthtimeLunar: mingli.BirthtimeLunar,
		Bazi:           mingli.Bazi,
		Type:           1, // 默认
		Birthplace:     mingli.Birthplace,
		UserAgent:      req.UserAgent,
		IP:             ret.IP,
		Region:         ret.Region(),
		AppID:          3, // 万年历
		AppPlatformID:  1, // 未知（默认）
		SaveTime:       time.Now(),
	}); err != nil {
		return nil, err
	}
	return detail.Data, nil
}

func (slf *userMingliService) ListUserMingli(ctx context.Context, req *v1.ListUserMingliRequest) (v1.ListUserMingliResponseData, error) {
	list, err := slf.userMingliRepo.ListUserMingli(ctx, req.User, req.AppID, req.GroupID)
	if err != nil {
		return nil, err
	}
	var res v1.ListUserMingliResponseData
	for _, mingli := range list {
		res = append(res, &v1.UserMingli{
			ID:             mingli.ID,
			Name:           mingli.Name,
			Gender:         mingli.Gender,
			BirthtimeStr:   mingli.Birthtime.Format("2006-01-02 15:04:05"),
			LunarBirthtime: mingli.BirthtimeLunar,
			Address:        mingli.Birthplace,
			IsDefault:      mingli.IsDefault,
			Wuxing:         mingli.Wuxing,
			Bazi:           mingli.Bazi,
			Xiaoyun:        &mingli.Xiaoyun,
			Dayun:          &mingli.Dayun,
		})
	}
	return res, nil
}

func (slf *userMingliService) SetDefaultMingli(ctx context.Context, req *v1.SetDefaultUserMingliRequest) error {
	return slf.tx.Transaction(ctx, func(ctx context.Context) error {
		mingli, err := slf.userMingliRepo.FetchUserMingli(ctx, req.User, req.ID)
		if err != nil {
			return err
		}
		if mingli == nil {
			return v1.ErrUserMingliNotFound
		}
		return slf.userMingliRepo.SetDefaultMingli(ctx, req.User, req.ID)
	})
}

func (slf *userMingliService) SetMingliWuxing(ctx context.Context, req *v1.SetMingliWuxingRequest) error {
	return slf.tx.Transaction(ctx, func(ctx context.Context) error {
		mingli, err := slf.userMingliRepo.FetchUserMingli(ctx, req.User, req.ID)
		if err != nil {
			return err
		}
		if mingli == nil {
			return v1.ErrUserMingliNotFound
		}
		return slf.userMingliRepo.SetMingliWuxing(ctx, req.User, req.ID, req.Wuxing)
	})
}

func (slf *userMingliService) DeleteUserMingli(ctx context.Context, req *v1.DeleteUserMingliRequest) error {
	return slf.tx.Transaction(ctx, func(ctx context.Context) error {
		mingli, err := slf.userMingliRepo.FetchUserMingli(ctx, req.User, req.ID)
		if err != nil {
			return err
		}
		if mingli == nil {
			return v1.ErrUserMingliNotFound
		}
		if err = slf.userMingliRepo.DeleteUserMingli(ctx, req.ID, req.User); err != nil {
			return err
		}
		return nil
	})
}

func (slf *userMingliService) UpdateUserMingli(ctx context.Context, req *v1.UpdateUserMingliRequest) error {
	mingli, err := slf.userMingliRepo.FetchUserMingli(ctx, req.User, req.ID)
	if err != nil {
		return err
	}
	if mingli == nil {
		return v1.ErrUserMingliNotFound
	}
	mingli.Name = req.Name
	mingli.Gender = req.Gender
	mingli.Birthplace = req.Address
	birthtime, err := time.Parse("2006-01-02 15:04:05", req.Birthtime)
	if err != nil {
		return err
	}
	if mingli.Birthtime.Format("2006-01-02 15:04:05") != req.Birthtime {
		mingli.Birthtime = birthtime
		birthtimeSun, err := slf.realSunTime(ctx, birthtime, req.Address)
		if err != nil {
			return err
		}
		mingli.BirthtimeSun = birthtimeSun
		day, err := slf.dateRepo.GetOneDay(ctx, birthtime.Format("2006-01-02"))
		if err != nil {
			return err
		}
		if day != nil {
			mingli.BirthtimeLunar = fmt.Sprintf("%s%s %s", day.LunarYear, "年"+day.LunarDate, slf.GetShichenByHours(birthtime.Hour()))
		}
		xiyong, err := slf.coronaCli.GetSizhuWuxingXiyong(ctx, &corona.GetSizhuWuxingXiyongRequest{
			Birthtime: birthtime.Format("2006-01-02 15:04:05"),
			Gender:    lo.Ternary(req.Gender == 1, "男", "女"),
		})
		if err != nil {
			return err
		}
		mingli.Wuxing = xiyong.XiyongArr
		mingli.Bazi, err = slf.CalculateBazi(ctx, req.Birthtime, req.Gender, req.Address, req.Name)
		if err != nil {
			return err
		}
		xiaoyun, err := slf.CalculateXiaoyun(ctx, req.Birthtime, req.Gender)
		if err != nil {
			return err
		}
		mingli.Xiaoyun = *xiaoyun
		dayun, err := slf.CalculateDayun(ctx, req.Birthtime, req.Gender)
		if err != nil {
			return err
		}
		mingli.Dayun = *dayun
	}
	return slf.tx.Transaction(ctx, func(ctx context.Context) error {
		if err = slf.userMingliRepo.UpdateUserMingli(ctx, mingli); err != nil {
			return err
		}
		if req.IsDefault == nil || !*req.IsDefault {
			return nil
		}
		return slf.userMingliRepo.SetDefaultMingli(ctx, req.User, req.ID)
	})
}

func (slf *userMingliService) CreateUserMingli(ctx context.Context, req *v1.CreateUserMingliRequest) (*v1.CreateUserMingliResponseData, error) {
	mingli := &model.UserMingli{
		AppID:      req.AppID,
		UserID:     req.User,
		Name:       req.Name,
		Gender:     req.Gender,
		Birthplace: req.Address,
	}
	if req.GroupID == 0 {
		// 未传分组ID：默认分组=>查询默认分组
		group, err := slf.userMingliGroupRepo.GetDefaultGroup(ctx, req.User, req.AppID)
		if err != nil {
			return nil, err
		}
		if group == nil {
			mingli.GroupID, err = slf.userMingliGroupRepo.CreateGroup(ctx, &model.UserMingliGroup{
				UserID:    req.User,
				AppID:     req.AppID,
				Name:      "默认",
				IsDefault: true,
			})
			if err != nil {
				return nil, err
			}
		} else {
			mingli.GroupID = group.ID
		}
	} else {
		mingli.GroupID = req.GroupID
	}
	birthtime, err := time.Parse("2006-01-02 15:04:05", req.Birthtime)
	if err != nil {
		return nil, err
	}
	mingli.Birthtime = birthtime
	birthtimeSun, err := slf.realSunTime(ctx, birthtime, req.Address)
	if err != nil {
		return nil, err
	}
	mingli.BirthtimeSun = birthtimeSun
	day, err := slf.dateRepo.GetOneDay(ctx, birthtime.Format("2006-01-02"))
	if err != nil {
		return nil, err
	}
	if day != nil {
		mingli.BirthtimeLunar = fmt.Sprintf("%s%s %s", day.LunarYear, "年"+day.LunarDate, slf.GetShichenByHours(birthtime.Hour()))
	}
	xiyong, err := slf.coronaCli.GetSizhuWuxingXiyong(ctx, &corona.GetSizhuWuxingXiyongRequest{
		Birthtime: birthtime.Format("2006-01-02 15:04:05"),
		Gender:    lo.Ternary(req.Gender == 1, "男", "女"),
	})
	if err != nil {
		return nil, err
	}
	mingli.Wuxing = xiyong.XiyongArr
	mingli.Bazi, err = slf.CalculateBazi(ctx, req.Birthtime, req.Gender, req.Address, req.Name)
	if err != nil {
		return nil, err
	}
	xiaoyun, err := slf.CalculateXiaoyun(ctx, req.Birthtime, req.Gender)
	if err != nil {
		return nil, err
	}
	mingli.Xiaoyun = *xiaoyun
	dayun, err := slf.CalculateDayun(ctx, req.Birthtime, req.Gender)
	if err != nil {
		return nil, err
	}
	mingli.Dayun = *dayun
	if err = slf.tx.Transaction(ctx, func(ctx context.Context) error {
		mingli.ID, err = slf.userMingliRepo.CreateUserMingli(ctx, mingli)
		if err != nil {
			return err
		}
		if req.IsDefault == nil || !*req.IsDefault {
			return nil
		}
		return slf.userMingliRepo.SetDefaultMingli(ctx, req.User, mingli.ID)
	}); err != nil {
		return nil, err
	}
	return &v1.CreateUserMingliResponseData{ID: mingli.ID}, nil
}

func (slf *userMingliService) CalculateBazi(ctx context.Context, birthtime string, gender int, address []string, name string) ([]string, error) {
	resp, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{Birthtime: birthtime, Gender: lo.Ternary(gender == 1, "男", "女")})
	if err != nil {
		return nil, err
	}
	return resp.BaziTupleNew[:4], nil
}

func (slf *userMingliService) CalculateXiaoyun(ctx context.Context, birthtime string, gender int) (*model.UserMingliXiaoyun, error) {
	genderStr := lo.Ternary(gender == 1, "男", "女")
	resp, err := slf.coronaCli.GetXiaoyun(ctx, &corona.GetXiaoyunRequest{
		Birthtime: birthtime,
		Gender:    genderStr,
	})
	if err != nil {
		return nil, err
	}
	return &model.UserMingliXiaoyun{
		SubYear:   resp.SubYearXiaoyun,
		StartYear: resp.XiaoyunQishi,
		EndYear:   resp.XiaoyunJiezhi,
		Values:    resp.GetXiaoyunList,
	}, err
}

func (slf *userMingliService) CalculateDayun(ctx context.Context, birthtime string, gender int) (*model.UserMingliDayun, error) {
	genderStr := lo.Ternary(gender == 1, "男", "女")
	resp, err := slf.coronaCli.GetDayun(ctx, &corona.GetDayunRequest{
		Birthtime: birthtime,
		Gender:    genderStr,
	})
	if err != nil {
		return nil, err
	}
	return &model.UserMingliDayun{
		StartYear: resp.DayunQishi,
		EndYear:   resp.DayunJiezhi,
		Values:    resp.GetShierDayun,
	}, nil
}

func (slf *userMingliService) GetShichenByHours(hour int) string {
	if hour >= 23 || hour < 1 {
		return "子时"
	} else if hour < 3 {
		return "丑时"
	} else if hour < 5 {
		return "寅时"
	} else if hour < 7 {
		return "卯时"
	} else if hour < 9 {
		return "辰时"
	} else if hour < 11 {
		return "巳时"
	} else if hour < 13 {
		return "午时"
	} else if hour < 15 {
		return "未时"
	} else if hour < 17 {
		return "申时"
	} else if hour < 19 {
		return "酉时"
	} else if hour < 21 {
		return "戌时"
	} else {
		return "亥时"
	}
}

// BirthtimeSun 获取真太阳时间
func (slf *userMingliService) realSunTime(ctx context.Context, birthtime time.Time, location []string) (time.Time, error) {
	if len(location) == 0 {
		return time.Time{}, nil
	}
	join := strings.Join(location, "")
	if len(location) == 3 {
		join = "中国" + join
	}
	offset, err := slf.dateRepo.GetOffset4TimeByLocation(ctx, join)
	if err != nil {
		return time.Time{}, err
	}
	sunTime := birthtime.Add(time.Duration(offset) * time.Minute)
	return sunTime, nil
}
