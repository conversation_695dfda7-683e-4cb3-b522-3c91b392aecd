package service

import (
	"context"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/repository"
)

type PaipanRecordService interface {
	PageListPaipanRecord(ctx context.Context, req *v1.PageListPaipanRecordRequest) (*v1.PageListPaipanRecordResponseData, error)
	DeletePaipanRecord(ctx context.Context, req *v1.DeletePaipanRecordRequest) error
	OwnPaipanRecord(ctx context.Context, req *v1.PaipanRecordOwnRequest) error
}

func NewPaipanRecordService(service *Service, paipanRecordRepo repository.UserPaipanRecordRepository) PaipanRecordService {
	return &paipanRecordService{
		paipanRecordRepo: paipanRecordRepo,
		Service:          service,
	}
}

type paipanRecordService struct {
	paipanRecordRepo repository.UserPaipanRecordRepository
	*Service
}

func (slf *paipanRecordService) OwnPaipanRecord(ctx context.Context, req *v1.PaipanRecordOwnRequest) error {
	return slf.paipanRecordRepo.OwnPaipanRecord(ctx, req.User, req.IDs)
}

func (slf *paipanRecordService) DeletePaipanRecord(ctx context.Context, req *v1.DeletePaipanRecordRequest) error {
	return slf.paipanRecordRepo.DeletePaipanRecord(ctx, req)
}

func (slf *paipanRecordService) PageListPaipanRecord(ctx context.Context, req *v1.PageListPaipanRecordRequest) (*v1.PageListPaipanRecordResponseData, error) {
	return slf.paipanRecordRepo.PageListPaipanRecord(ctx, req)
}
