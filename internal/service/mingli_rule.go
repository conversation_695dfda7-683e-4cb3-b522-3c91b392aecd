package service

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"regexp"
	"strings"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/array"
	"zodiacus/third_party/corona"

	"github.com/samber/lo"
)

type MingliRuleService interface {
	CreateMingliRule(ctx context.Context, req *v1.CreateMingliRuleRequest) (*v1.CreateMingliRuleResponseData, error)
	UpdateMingliRule(ctx context.Context, req *v1.UpdateMingliRuleRequest) error
	DeleteMingliRule(ctx context.Context, req *v1.DeleteMingliRuleRequest) error
	PageListMingliRule(ctx context.Context, req *v1.PageListMingliRuleRequest) (*v1.PageListMingliRuleResponseData, error)
	MatchMingliRules(ctx context.Context, req *v1.MatchMingliRuleRequest) (*v1.MatchMingliRulesResponseData, error)
}

func NewMingliRuleService(
	service *Service,
	mingliRuleRepo repository.MingliRuleRepository,
	mingliRuleConditionRepo repository.MingliRuleConditionRepository,
	enumsRepo repository.EnumsRepository,
) MingliRuleService {
	return &mingliRuleService{
		mingliRuleRepo:          mingliRuleRepo,
		mingliRuleConditionRepo: mingliRuleConditionRepo,
		enumsRepo:               enumsRepo,
		Service:                 service,
	}
}

type mingliRuleService struct {
	mingliRuleRepo          repository.MingliRuleRepository
	mingliRuleConditionRepo repository.MingliRuleConditionRepository
	enumsRepo               repository.EnumsRepository
	*Service
}

func (slf *mingliRuleService) CreateMingliRule(ctx context.Context, req *v1.CreateMingliRuleRequest) (*v1.CreateMingliRuleResponseData, error) {
	mingliRule, err := slf.mingliRuleRepo.GetByRuleNo(ctx, req.No)
	if err != nil {
		return nil, err
	}
	if mingliRule != nil {
		return nil, v1.ErrMingliRuleNoAlreadyTaken
	}
	id, err := slf.mingliRuleRepo.Create(ctx, &model.MingliRule{
		No:          req.No,
		Name:        req.Name,
		Module:      req.Module,
		Source:      1,
		IsEnabled:   req.IsEnabled,
		Result:      req.Result,
		Description: req.Description,
		CreatorID:   req.User,
	})
	if err != nil {
		return nil, err
	}
	return &v1.CreateMingliRuleResponseData{ID: id}, nil
}

func (slf *mingliRuleService) UpdateMingliRule(ctx context.Context, req *v1.UpdateMingliRuleRequest) error {
	mingliRule, err := slf.mingliRuleRepo.GetByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if mingliRule == nil {
		return v1.ErrNotFound
	}
	if mingliRule.ID != req.ID {
		return v1.ErrMingliRuleNoAlreadyTaken
	}
	return slf.mingliRuleRepo.Update(ctx, &model.MingliRule{
		ID:          req.ID,
		No:          req.No,
		Name:        req.Name,
		Module:      req.Module,
		CreatorID:   mingliRule.CreatorID,
		Source:      mingliRule.Source,
		IsEnabled:   req.IsEnabled,
		Result:      req.Result,
		Description: req.Description,
	})
}

func (slf *mingliRuleService) DeleteMingliRule(ctx context.Context, req *v1.DeleteMingliRuleRequest) error {
	mingliRule, err := slf.mingliRuleRepo.GetByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if mingliRule == nil {
		return v1.ErrNotFound
	}
	return slf.mingliRuleRepo.DeleteByID(ctx, req.ID)
}

func (slf *mingliRuleService) PageListMingliRule(ctx context.Context, req *v1.PageListMingliRuleRequest) (*v1.PageListMingliRuleResponseData, error) {
	data, err := slf.mingliRuleRepo.PageList(ctx, req)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (slf *mingliRuleService) MatchMingliRules(ctx context.Context, req *v1.MatchMingliRuleRequest) (*v1.MatchMingliRulesResponseData, error) {
	rules, err := slf.mingliRuleRepo.GetAllEnabledRules(ctx)
	if err != nil {
		return nil, err
	}
	ruleIDsMap := make(map[int64]struct{})
	for _, rule := range rules {
		ruleIDsMap[rule.ID] = struct{}{}
	}
	ruleIDs := make([]int64, 0)
	for id := range ruleIDsMap {
		ruleIDs = append(ruleIDs, id)
	}
	enums, err := slf.GetMingliRuleRelatedEnums(ctx)
	if err != nil {
		return nil, err
	}
	jiuzhuData, err := slf.coronaCli.LifeCyclesMonth(ctx, &corona.LifeCyclesMonthRequest{
		Birthday: req.Birthday,
		Gender:   req.Gender,
		Now:      req.CurrentDay,
		Location: []string{},
	})
	if err != nil {
		return nil, err
	}
	if len(jiuzhuData.Tiangan) != 9 {
		return nil, errors.Errorf("tiangan length is not 9")
	}
	jiuzhuData.ZhuXing[2] = "" // 日柱主星固定为空
	wuxingResp, err := slf.coronaCli.GetSizhuWuxingXiyong(ctx, &corona.GetSizhuWuxingXiyongRequest{
		Birthtime: req.Birthday,
		Gender:    lo.Ternary(req.Gender == 1, "男", "女"),
	})
	if wuxingResp == nil {
		return nil, errors.Errorf("GetSizhuWuxingXiyong return nil")
	}
	input := v1.MatchMingliRulesInput{
		Gender:    req.Gender, // 1-男，2-女
		Canggan:   make([][]int64, len(jiuzhuData.Benqi)),
		Fuxing:    make([][]int64, len(jiuzhuData.BenqiShiShen)),
		FuxingStr: make([][]string, len(jiuzhuData.BenqiShiShen)),
		ZuoduiMap: make(map[int64]*model.Zuodui),
	}
	for _, zuodui := range enums.Zuodui {
		input.ZuoduiMap[zuodui.ID] = zuodui
	}
	for _, v := range jiuzhuData.ZhuXing {
		if v == "" {
			input.Zhuxing = append(input.Zhuxing, 0)
			input.ZhuxingStr = append(input.ZhuxingStr, "")
			continue
		}
		shishen, exist := enums.ShishenNamesMap[v]
		if !exist {
			return nil, errors.Errorf("shishen %s not found", v)
		}
		input.Zhuxing = append(input.Zhuxing, shishen.ID)
		input.ZhuxingStr = append(input.ZhuxingStr, v)
	}
	for _, v := range jiuzhuData.Tiangan {
		tiangan, exist := enums.TianganNamesMap[v]
		if !exist {
			return nil, errors.Errorf("tiangan %s not found", v)
		}
		input.Tiangan = append(input.Tiangan, tiangan.ID)
	}
	for _, v := range jiuzhuData.Dizhi {
		dizhi, exist := enums.DizhiNamesMap[v]
		if !exist {
			return nil, errors.Errorf("dizhi %s not found", v)
		}
		input.Dizhi = append(input.Dizhi, dizhi.ID)
	}
	for i, v := range jiuzhuData.Benqi {
		if v == "" {
			continue
		}
		tiangan, exist := enums.TianganNamesMap[v]
		if !exist {
			return nil, errors.Errorf("tiangan %s not found", v)
		}
		input.Canggan[i] = append(input.Canggan[i], tiangan.ID)
	}
	for i, v := range jiuzhuData.Zhongqi {
		if v == "" {
			continue
		}
		tiangan, exist := enums.TianganNamesMap[v]
		if !exist {
			return nil, errors.Errorf("tiangan %s not found", v)
		}
		input.Canggan[i] = append(input.Canggan[i], tiangan.ID)
	}
	for i, v := range jiuzhuData.Yuqi {
		if v == "" {
			continue
		}
		tiangan, exist := enums.TianganNamesMap[v]
		if !exist {
			return nil, errors.Errorf("tiangan %s not found", v)
		}
		input.Canggan[i] = append(input.Canggan[i], tiangan.ID)
	}
	for i, v := range jiuzhuData.BenqiShiShen {
		if v == "" {
			continue
		}
		shishen, exist := enums.ShishenNamesMap[v]
		if !exist {
			return nil, errors.Errorf("shishen %s not found", v)
		}
		input.Fuxing[i] = append(input.Fuxing[i], shishen.ID)
		input.FuxingStr[i] = append(input.FuxingStr[i], v)
	}
	for i, v := range jiuzhuData.ZhongqiShiShen {
		if v == "" {
			continue
		}
		shishen, exist := enums.ShishenNamesMap[v]
		if !exist {
			return nil, errors.Errorf("shishen %s not found", v)
		}
		input.Fuxing[i] = append(input.Fuxing[i], shishen.ID)
		input.FuxingStr[i] = append(input.FuxingStr[i], v)
	}
	for i, v := range jiuzhuData.YuqiShiShen {
		if v == "" {
			continue
		}
		shishen, exist := enums.ShishenNamesMap[v]
		if !exist {
			return nil, errors.Errorf("shishen %s not found", v)
		}
		input.Fuxing[i] = append(input.Fuxing[i], shishen.ID)
		input.FuxingStr[i] = append(input.FuxingStr[i], v)
	}
	for _, v := range jiuzhuData.NaYin {
		nayin, exist := enums.NayinNamesMap[v]
		if !exist {
			return nil, errors.Errorf("nayin %s not found", v)
		}
		input.Nayin = append(input.Nayin, nayin.ID)
	}
	wuxing := strings.Split(wuxingResp.Xiyongjichou, ",")
	for _, v := range wuxing {
		wx, exist := enums.WuxingNamesMap[v]
		if !exist {
			return nil, errors.Errorf("wuxing %s not found", v)
		}
		input.Wuxing = append(input.Wuxing, wx.ID)
	}
	for i, gan := range input.Tiangan {
		for j, zhi := range input.Dizhi {
			if i == j {
				input.Ganzhi = append(input.Ganzhi, enums.GanzhiIDsMap[fmt.Sprintf("%d-%d", gan, zhi)].ID)
			}
		}
	}
	for i, zhu := range input.Zhuxing {
		for j, fus := range input.Fuxing {
			if i == j {
				if zhu != 0 {
					input.Shishen = append(input.Shishen, zhu)
				}
				input.Shishen = append(input.Shishen, fus...)
			}
		}
	}
	// 通过性别筛选（2-男，3-女）对应的条件
	conditions, err := slf.mingliRuleConditionRepo.GetMingliRuleConditionsByRuleIDsAndGender(ctx, ruleIDs, req.Gender+1)
	if err != nil {
		return nil, err
	}
	conditionsMap := make(map[int64][]*model.MingliRuleCondition)
	for _, condition := range conditions {
		if _, exist := conditionsMap[condition.MingliRuleID]; !exist {
			conditionsMap[condition.MingliRuleID] = make([]*model.MingliRuleCondition, 0)
		}
		conditionsMap[condition.MingliRuleID] = append(conditionsMap[condition.MingliRuleID], condition)
	}
	res := &v1.MatchMingliRulesResponseData{}
	for _, rule := range rules {
		matched, err := slf.MatchMingliRule(ctx, rule, conditionsMap[rule.ID], &input)
		if err != nil {
			return nil, err
		}
		if matched != nil {
			res.Rules = append(res.Rules, matched)
		}
	}
	birthtime, err := time.Parse("2006-01-02 15:04:05", req.Birthday)
	if err != nil {
		return nil, err
	}
	calendar, err := slf.enumsRepo.GetCalendarByDate(ctx, birthtime.Format("2006-01-02"))
	if err != nil {
		return nil, err
	}
	if calendar != nil {
		year, exist := enums.GanzhiNamesMap[calendar.Bazi1]
		if !exist {
			res.Birth = append(res.Birth, "")
		}
		res.Birth = append(res.Birth, year.Yiju)
		_, monthName, dayName := func(input string) (string, string, string) {
			input = strings.Replace(input, "闰", "", -1)
			if !strings.HasSuffix(input, "日") {
				input += "日"
			}
			re := regexp.MustCompile(`(.*?)月(.*?)日`)
			matches := re.FindStringSubmatch(input)
			if len(matches) < 3 {
				return input, "", ""
			}
			return input, matches[1] + "月", matches[2] + "日"
		}(calendar.LunarDate)
		month, exist := enums.LunarMonthMap[monthName]
		if !exist {
			res.Birth = append(res.Birth, "")
		}
		res.Birth = append(res.Birth, month.Result)
		day, exist := enums.LunarDateMap[dayName]
		if !exist {
			res.Birth = append(res.Birth, "")
		}
		res.Birth = append(res.Birth, day.Result)
		hourName := func(hour int) string {
			if hour >= 23 || hour < 1 {
				return "子"
			} else if hour < 3 {
				return "丑"
			} else if hour < 5 {
				return "寅"
			} else if hour < 7 {
				return "卯"
			} else if hour < 9 {
				return "辰"
			} else if hour < 11 {
				return "巳"
			} else if hour < 13 {
				return "午"
			} else if hour < 15 {
				return "未"
			} else if hour < 17 {
				return "申"
			} else if hour < 19 {
				return "酉"
			} else if hour < 21 {
				return "戌"
			} else {
				return "亥"
			}
		}(birthtime.Hour())
		hour, exist := enums.LunarTimeMap[hourName]
		if !exist {
			res.Birth = append(res.Birth, "")
		}
		res.Birth = append(res.Birth, hour.Result)
	}
	return res, nil
}

func (slf *mingliRuleService) MatchMingliRule(ctx context.Context, rule *model.MingliRule, conditions []*model.MingliRuleCondition, param *v1.MatchMingliRulesInput) (*v1.MatchMingliRulesResponseDataRule, error) {
	if len(conditions) == 0 {
		return nil, nil
	}
	matched := make([]*v1.MatchMingliRuleResponseDataCondition, 0)
	for _, condition := range conditions {
		ok, err := slf.MatchMingliRuleCondition(ctx, condition, param)
		if err != nil {
			return nil, err
		}
		if !ok {
			continue
		}
		matched = append(matched, &v1.MatchMingliRuleResponseDataCondition{
			ID:        condition.ID,
			Criterion: condition.Criterion,
		})
	}
	if len(matched) == 0 {
		return nil, nil
	}
	return &v1.MatchMingliRulesResponseDataRule{
		ID:         rule.ID,
		Result:     rule.Result,
		Conditions: matched,
	}, nil
}

func (slf *mingliRuleService) MatchMingliRuleCondition(ctx context.Context, condition *model.MingliRuleCondition, param *v1.MatchMingliRulesInput) (bool, error) {
	switch condition.Category {
	case 2:
		return slf.MatchMingliRuleConditionZuodui(ctx, condition, param)
	case 3:
		return slf.MatchMingliRuleConditionXiji(ctx, condition, param)
	default:
		if ok, err := slf.MatchMingliRuleConditionZuodui(ctx, condition, param); err != nil {
			return false, err
		} else if ok {
			return true, nil
		}
		return slf.MatchMingliRuleConditionXiji(ctx, condition, param)
	}
}

func (slf *mingliRuleService) MatchMingliRuleConditionZuodui(_ context.Context, condition *model.MingliRuleCondition, param *v1.MatchMingliRulesInput) (bool, error) {
	kvs := array.Filter(condition.Zuodui, func(kv *model.MingliRuleConditionKV) bool {
		return kv.Checked
	})
	typ, exist := param.ZuoduiMap[condition.Type]
	if !exist || typ == nil {
		return false, nil
	}
	zuo := array.Copy(condition.WeizhiZuo, func(i int) int { return i - 1 })
	dui := array.Copy(condition.WeizhiDui, func(i int) int { return i - 1 })
	var (
		keyIDs []int64
	)
	switch typ.Zuo {
	case "干":
		keyIDs = array.SubArray(param.Tiangan, zuo...)
	case "支":
		keyIDs = array.SubArray(param.Dizhi, zuo...)
	case "干支":
		keyIDs = array.SubArray(param.Ganzhi, zuo...)
	case "纳音":
		keyIDs = array.SubArray(param.Nayin, zuo...)
	case "十神":
		keyIDs = array.SubArray(param.Shishen, zuo...)
	case "干十神":
		keyIDs = array.SubArray(param.Zhuxing, zuo...)
	case "支十神":
		array.Range(array.SubArray(param.Fuxing, zuo...), func(ids []int64) {
			keyIDs = append(keyIDs, ids...)
		})
	case "天干自计算":
		for _, kv := range kvs {
			found := false
			for _, tiangan := range param.Tiangan {
				if kv.Key == tiangan {
					found = true
					break
				}
			}
			if !found {
				return false, nil
			}
		}
		return true, nil
	case "地支自计算":
		for _, kv := range kvs {
			found := false
			for _, dizhi := range param.Dizhi {
				if kv.Key == dizhi {
					found = true
					break
				}
			}
			if !found {
				return false, nil
			}
		}
		return true, nil
	case "干支一柱":
		for _, kv := range kvs {
			found := false
			for _, ganzhi := range param.Ganzhi {
				if kv.Key == ganzhi {
					found = true
					break
				}
			}
			if !found {
				return false, nil
			}
		}
		return true, nil
	default:
	}
	keyIDs = array.Unique(keyIDs)
	keyIDs = array.Filter(keyIDs, func(id int64) bool {
		return id > 0
	})
	items := array.Filter(kvs, func(kv *model.MingliRuleConditionKV) bool {
		return array.Exist(keyIDs, func(id int64) bool {
			return kv.Key == id
		})
	})
	var (
		valueIDs []int64
	)
	switch typ.Dui {
	case "干":
		valueIDs = array.SubArray(param.Tiangan, dui...)
	case "支":
		valueIDs = array.SubArray(param.Dizhi, dui...)
	case "干支":
		valueIDs = array.SubArray(param.Ganzhi, dui...)
	case "十神":
		valueIDs = array.SubArray(param.Shishen, dui...)
	case "干十神":
		valueIDs = array.SubArray(param.Zhuxing, dui...)
	case "支十神":
		array.Range(array.SubArray(param.Fuxing, dui...), func(ids []int64) {
			valueIDs = append(valueIDs, ids...)
		})
	case "纳音":
		valueIDs = array.SubArray(param.Nayin, dui...)
	default:
		// do nothing
	}
	valueIDs = array.Unique(valueIDs)
	valueIDs = array.Filter(valueIDs, func(id int64) bool {
		return id > 0
	})
	return array.Exist(items, func(kv *model.MingliRuleConditionKV) bool {
		return array.Exist(valueIDs, func(id int64) bool {
			return array.Exist(kv.Values, func(value model.MingliRuleConditionValue) bool {
				return value.Value == id
			})
		})
	}), nil
}

func (slf *mingliRuleService) MatchMingliRuleConditionXiji(_ context.Context, condition *model.MingliRuleCondition, param *v1.MatchMingliRulesInput) (bool, error) {
	return array.Exist(condition.Xiji, func(kv *model.MingliRuleConditionKV) bool {
		if !kv.Checked {
			return false
		}
		for _, value := range kv.Values {
			found := false
			switch value.Type {
			case 4: // 十神
				for _, shishen := range param.Shishen {
					if shishen == 0 {
						continue
					}
					if shishen == value.Value {
						found = true
						break
					}
				}
			case 5: // 五行
				if param.Wuxing[kv.Key-1] == value.Value {
					found = true
				}
			}
			if found {
				return true
			}
		}
		return false
	}), nil
}

func (slf *mingliRuleService) GetMingliRuleRelatedEnums(ctx context.Context) (*v1.MingliRuleRelatedEnums, error) {
	var (
		err   error
		enums = &v1.MingliRuleRelatedEnums{}
	)
	enums.Zuodui, err = slf.enumsRepo.GetAllZuodui(ctx)
	if err != nil {
		return nil, err
	}
	enums.Wuxing, err = slf.enumsRepo.GetAllWuxing(ctx)
	if err != nil {
		return nil, err
	}
	enums.WuxingNamesMap = make(map[string]*model.Wuxing)
	for _, v := range enums.Wuxing {
		enums.WuxingNamesMap[v.Wuxing] = v
	}
	enums.Tiangan, err = slf.enumsRepo.GetAllTiangan(ctx)
	if err != nil {
		return nil, err
	}
	enums.TianganNamesMap = make(map[string]*model.Tiangan)
	for _, v := range enums.Tiangan {
		enums.TianganNamesMap[v.Tiangan] = v
	}
	enums.Dizhi, err = slf.enumsRepo.GetAllDizhi(ctx)
	if err != nil {
		return nil, err
	}
	enums.DizhiNamesMap = make(map[string]*model.Dizhi)
	for _, v := range enums.Dizhi {
		enums.DizhiNamesMap[v.Dizhi] = v
	}
	enums.Ganzhi, err = slf.enumsRepo.GetAllGanzhi(ctx)
	if err != nil {
		return nil, err
	}
	enums.GanzhiNamesMap = make(map[string]*model.Ganzhi)
	for _, v := range enums.Ganzhi {
		enums.GanzhiNamesMap[v.Name] = v
	}
	enums.GanzhiIDsMap = make(map[string]*model.Ganzhi)
	for _, v := range enums.Ganzhi {
		enums.GanzhiIDsMap[fmt.Sprintf("%d-%d", v.TianganID, v.DizhiID)] = v
	}
	enums.Shishen, err = slf.enumsRepo.GetAllShishen(ctx)
	if err != nil {
		return nil, err
	}
	enums.ShishenNamesMap = make(map[string]*model.Shishen)
	for _, v := range enums.Shishen {
		enums.ShishenNamesMap[v.Name] = v
	}
	enums.Nayin, err = slf.enumsRepo.GetAllNayin(ctx)
	if err != nil {
		return nil, err
	}
	enums.NayinNamesMap = make(map[string]*model.Nayin)
	for _, v := range enums.Nayin {
		enums.NayinNamesMap[v.Nayin] = v
	}
	enums.Xiji, err = slf.enumsRepo.GetAllXiji(ctx)
	if err != nil {
		return nil, err
	}
	enums.XijiNamesMap = make(map[string]*model.Xiji)
	for _, v := range enums.Xiji {
		enums.XijiNamesMap[v.Name] = v
	}
	enums.LunarMonth, err = slf.enumsRepo.GetAllLunarMonth(ctx)
	if err != nil {
		return nil, err
	}
	enums.LunarMonthMap = make(map[string]*model.LunarMonth)
	for _, v := range enums.LunarMonth {
		enums.LunarMonthMap[v.Name] = v
	}
	enums.LunarDate, err = slf.enumsRepo.GetAllLunarDate(ctx)
	if err != nil {
		return nil, err
	}
	enums.LunarDateMap = make(map[string]*model.LunarDate)
	for _, v := range enums.LunarDate {
		enums.LunarDateMap[v.Name] = v
	}
	enums.LunarTime, err = slf.enumsRepo.GetAllLunarTime(ctx)
	if err != nil {
		return nil, err
	}
	enums.LunarTimeMap = make(map[string]*model.LunarTime)
	for _, v := range enums.LunarTime {
		enums.LunarTimeMap[v.Name] = v
	}
	return enums, nil
}
