package service

import (
	"context"
	contactWayRequest "github.com/ArtisanCloud/PowerWeChat/v3/src/work/externalContact/contactWay/request"
	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/pkg/errors"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/third_party/wecom"
)

type KefuService interface {
	Qrcode(ctx context.Context, user *casdoorsdk.User) (string, error)
}

func NewKefuService(service *Service, qwRepo repository.QwRepository,
	doraemon *wecom.Application,
) KefuService {
	return &kefuService{
		Service:  service,
		qwRepo:   qwRepo,
		doraemon: doraemon,
	}
}

type kefuService struct {
	*Service
	qwRepo   repository.QwRepository
	doraemon *wecom.Application
}

func (slf *kefuService) Qrcode(ctx context.Context, user *casdoorsdk.User) (string, error) {
	state, err := slf.qwRepo.FetchContactWayByAddState(ctx, user.Id)
	if err != nil {
		return "", err
	}
	if state == nil {
		qwResp, err := slf.doraemon.ExternalContactContactWay.Add(ctx, &contactWayRequest.RequestAddContactWay{
			Type:       2,
			Scene:      2,
			Remark:     "论玄-客服",
			SkipVerify: true,
			User:       []string{"XuanBuJiuFei"},
			State:      user.Id,
		})
		if err != nil {
			return "", err
		}
		if qwResp.ErrCode != 0 {
			return "", errors.Errorf("创建联系我失败：code=%d, msg=%s", qwResp.ErrCode, qwResp.ErrMsg)
		}
		state = &model.QwContactWay{
			Type:       1,
			AppID:      2,
			PlatformID: 1,
			Name:       "论玄-客服",
			Link:       qwResp.QRCode,
			UserIDs:    []string{"XuanBuJiuFei"},
			ConfigID:   qwResp.ConfigID,
			SkipVerify: true,
			AddState:   user.Id,
		}
		if _, err = slf.qwRepo.CreateContactWay(ctx, state); err != nil {
			return "", err
		}
	}
	return state.Link, nil
}
