package service

import (
	"context"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/repository"
	"zodiacus/third_party/wecom"
)

type KefuService interface {
	Qrcode(ctx context.Context, req *v1.QwKefuQrcodeRequest) (string, error)
	VipQrcode(ctx context.Context, req *v1.QwKefuVipQrcodeRequest) (string, error)
}

func NewKefuService(service *Service, qwRepo repository.QwRepository,
	doraemon *wecom.Application,
) KefuService {
	return &kefuService{
		Service:  service,
		qwRepo:   qwRepo,
		doraemon: doraemon,
	}
}

type kefuService struct {
	*Service
	qwRepo   repository.QwRepository
	doraemon *wecom.Application
}

func (slf *kefuService) Qrcode(ctx context.Context, req *v1.QwKefuQrcodeRequest) (string, error) {
	// 查询记录
}

func (slf *kefuService) VipQrcode(ctx context.Context, req *v1.QwKefuVipQrcodeRequest) (string, error) {
	//TODO implement me
	panic("implement me")
}
