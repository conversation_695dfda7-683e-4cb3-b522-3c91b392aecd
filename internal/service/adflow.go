package service

import (
	"context"
	"fmt"
	contactWayRequest "github.com/ArtisanCloud/PowerWeChat/v3/src/work/externalContact/contactWay/request"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/algo"
	"zodiacus/pkg/array"
	"zodiacus/pkg/strs"
	"zodiacus/third_party/corona"
	"zodiacus/third_party/submail"
	"zodiacus/third_party/wecom"
)

type AdflowService interface {
	MingliBazi(ctx context.Context, req *v1.MingliBaziRequest) (*v1.MingliBaziResponseData, error)
	MingliBaziReplay(ctx context.Context, req *v1.MingliBaziReplayRequest) (*v1.MingliBaziReplayResponseData, error)
	MingliBaziSMS(ctx context.Context, req *v1.MingliBaziSMSRequest) error
	MingliBaziYear(ctx context.Context, req *v1.MingliBaziYearRequest) (*v1.MingliBaziYearResponseData, error)
	MingliBaziQwLink(ctx context.Context, req *v1.MingliBaziQwLinkRequest) (*v1.MingliBaziQwLinkResponseData, error)
	MingliBaziClick(ctx context.Context) error
}

func NewAdflowService(
	service *Service,
	luncaiRepo repository.LuncaiRepository,
	paipanRepo repository.UserPaipanRecordRepository,
	orderRepo repository.UserOrderRepository,
	yunshiRepo repository.YunshiRepository,
	doraemon *wecom.Application,
	qwRepository repository.QwRepository,
	adflowRepo repository.AdflowRepository,
	dateRepo repository.DateRepository,
	submailRepo repository.SubMailRepository,
	userEventRepo repository.UserEventRepository,
	submailer *submail.Client,
) AdflowService {
	return &adflowService{
		Service:          service,
		luncaiRepo:       luncaiRepo,
		paipanRecordRepo: paipanRepo,
		orderRepo:        orderRepo,
		yunshiRepo:       yunshiRepo,
		doraemon:         doraemon,
		qwRepository:     qwRepository,
		adflowRepo:       adflowRepo,
		dateRepo:         dateRepo,
		submailRepo:      submailRepo,
		submailer:        submailer,
		userEventRepo:    userEventRepo,
		dizhiWuxing: map[string]string{
			"子": "水",
			"丑": "土",
			"寅": "木",
			"卯": "木",
			"辰": "土",
			"巳": "火",
			"午": "火",
			"未": "土",
			"申": "金",
			"酉": "金",
			"戌": "土",
			"亥": "水",
		},
		tianganWuxing: map[string]string{
			"甲": "木",
			"乙": "木",
			"丙": "火",
			"丁": "火",
			"戊": "土",
			"己": "土",
			"庚": "金",
			"辛": "金",
			"壬": "水",
			"癸": "水",
		},
		wuxingXiangsheng: map[string]string{
			"木": "火",
			"火": "土",
			"土": "金",
			"金": "水",
			"水": "木",
		},
		dyGanzhiShishenWangshuai: map[string][]string{
			"比肩-强": {
				"交际圈扩大，与朋友、合作伙伴交往频繁，这无疑会给命主来带许多赚钱的门路，不管是合伙做生意还是从事其他领域，表现出自信和果断。但易招小人，竞争加剧.需要更加警惕和谨慎，以免受到不利的影响。",
				"因过于义气钱财遭到损耗，与人合作事业容易中途散伙，从而导致钱财过度支出。",
			},
			"比肩-弱": {
				"交际圈扩大，与朋友、合作伙伴交往频繁，这无疑会给命主来带许多赚钱的门路，不管是合伙做生意还是从事其他领域，都能产生很好的效益。舍得付出，善于动员身边人的力量，有难时容易得到他人相助，摆脱困境。",
				"不计较钱财得失，主要是合作来钱财，适合投资拓展事业。",
			},
			"劫财-强": {
				"会遇到比较强劲的竞争对手，容易独断专行，不计后果，往往导致钱财损失严重。在人际交往中，切记不要被主观感受蒙蔽，冲动下决策，要留意周围人是否只为了钱财接近自己，凡事多留心眼总是没错的。",
				"有时过分重视金钱而因小失大，容易交友不慎，钱财被他人分夺。",
			},
			"劫财-弱": {
				"不满足现状，容易开创事业，敢于接受新挑战，浑身充满动力与征服欲，目标感强烈。在钱财方面开销大，不吝啬手中钱财，通常用做投资事业和呼朋请客。执行力强、行事效率高，容易在竞争中脱颖而出，赚得盆满钵满。",
				"胆识而有魄力，敢于去冒险赚钱，易爆发横财，喜欢花钱不储蓄。",
			},
			"食神-强": {
				"对金钱意识敏锐，善于发现商机，若是通过经营商铺、公司等获取收入，则赚钱会比以往轻松许多。对于打工族来说，则是努力学习，提高技术水平的有利时期，随着技能提升，不断有机会主动送上门，报酬也会越来越高。",
				"智慧和才能得到充分发挥，大多能抓住赚钱机遇，至而财源不断。",
			},
			"食神-弱": {
				"虽有机会见多识广，但由于内心发奋力不足，做事容易半途而废，开销往往大于收入。理想与现实脱骨，无形中容易养成自负不凡的习性，所以这步运势想要把握好，就一定要行动起来。",
				"注意分清事情的轻重缓急，集中精力办大事，避免事业陷入瓶颈。",
			},
			"伤官-强": {
				"是时候踏出一步了，无需太多犹豫不决，适宜在擅长的领域做出一番成就。同时，好运的加持也激活了命主的悟性，使其能迅速融入其他行业，看清商业模式，从而开辟多个赚钱渠道，发家致富。",
				"能接触各种新事物，可能另辟事业蹊径，从不同领域获得满意的财富。",
			},
			"伤官-弱": {
				"整体来说想法多变，尤为对物质条件要求高，常奔于忙碌，难得清闲。不喜受世俗规矩和约束而发生抵抗心理，喜欢高谈阔论但又难以承担重任，容易走上逆境之路，行险侥幸，切记勿耍小聪明，否则容易损失钱财。",
				"需注意，事业容易横遭变数，导致求财之路坎坷而急功近利。",
			},
			"偏财-强": {
				"善于利用周遭资源，会获得意想不到的额外收入。愿意为身边人花钱，舍得用钱财连接更多资源，与他人互换利益，借力发财。随着交际手腕能力的提高，财富来源也越为广泛。",
				"能预见发财商机，投机意识重，敢下笃定，容易踏上经商和副业之路。",
			},
			"偏财-弱": {
				"心态专注稳定，焦虑减少，认真踏实地工作态度，易获得同辈人的爱戴，每逢困难都有贵人化解，深得长辈和领导在经济方面的扶持。与此同时，还能够结交一些对自己有帮助的人士。",
				"很大可能会花钱购置车房或是做投资，一旦遇到赚钱机会则不加辨别出手，可能带来潜在隐患。",
			},
			"正财-强": {
				"虽然对金钱更执着，但是谋财的方式步步为营，不求意外收获，凡事求稳。花钱有计划性，每笔钱财都能花在刀刃上。通常需要经过勤恳付出换取报酬，若是从事生意，在投资方面会显得谨慎，不敢博大。",
				"财富观念加强，求稳为主，较为注重物质享受。",
			},
			"正财-弱": {
				"会更注重现实，容易以金钱衡量事情。生活中比较节俭，即使收入多也会花得小心翼翼，遇到有前景的项目也不敢冒然大投，虽然挣大钱的良机不多，但能够维持现状，为自己留下余钱，以便更好对未来做财富规划。",
				"为赚钱较为辛苦，薪资待遇不是太乐观，付出较多回报较低。",
			},
			"七杀-强": {
				"勇于战胜恶劣环境，擅长解决各种疑难问题，虽然担子重，但都在自己预料之中，以结果为导向的态度，往往能收获意外财富，在机遇面前，可放手大干，但要注意避免小人和是非。",
				"善于把握时机，容易担任核心职位，从而实现财富阶级的跨越。",
			},
			"七杀-弱": {
				"工作头绪多显得力不从心，四周环境容易给自己带来压力，即使遇到艰巨任务也不轻易妥协认输，会想方设法地为自己争取利益。需要提防事业中遭遇小人，低调地把熬过去，多勤恳做事，结交良人。",
				"事业出现转变，自身赚钱比较费力，收入增长缓慢，容易过度消费。",
			},
			"正官-强": {
				"受大运约束的影响，即便耕耘没有结果，也会更注重事业的发展。对生活更有进取心，相比其它阶段更有一种运筹帷幄、壮志来酬的气场，学会把握机遇，让自己的事业再上一个新台阶。",
				"在工作中往往能得到上级的认可，薪资随着职位的晋升而提高。",
			},
			"正官-弱": {
				"大事小事都要扛，比较琐碎，工作中易流于优柔寡断，受到严苛对待，太在乎别人眼光，导致无法按自己的原则或目标完成事情。需注意调节自身和有效的管理方法，才能稳步实现扩张，更好驾驭本大运。",
				"当心与小人缠斗内耗，临事优柔寡断，往往会错失良机，求财较为劳碌。",
			},
			"偏印-强": {
				"心性会变得比以往保守，很少花时间维持人际关系，更愿意做自己喜欢的事情，若是遇到一丝小赚的机会也不愿意放过。事业容易出现变故让命主面临经济危机，宜提前规划，主动预防，多留余地。",
				"容易轻信他人而损失钱财，注意避免在职场上受小人拖累，影响事业前途。",
			},
			"偏印-弱": {
				"患得患失的心态明显减弱，懂得正视缺点并虚心改正。偶遇贵人扶持事业的机会多，即使不会太过于投入本身的财力，就能享有来自贵人给予的丰厚报酬，财运往往随着事业地位的提高而增加。",
				"财运增加，薪水收入高，易获得意外之财，习惯性储蓄钱财。",
			},
			"正印-强": {
				"追求高度精神的生活，惰性较强，容易孤芳自赏而轻视金钱，即便钱财到了捉襟见肘的地步，依然保有多赚多花的心态，不过还是避免胡乱开销为好。注意集思广益，多听取朋友、前辈或家人的意见。",
				"升职空间小，薪资待遇不是很乐观，付出多回报少，同时应注意不要上当受骗。",
			},
			"正印-弱": {
				"心态专注稳定，焦虑减少，认真踏实地工作态度，易获得同辈人的爱戴，每逢困难都有贵人化解，深得长辈和领导在经济方面的扶持。与此同时，还能够结交一些对自己有帮助的人士。",
				"薪资待遇提高，收入相对稳定，也会有更多的余钱做资产配置。",
			},
		},
	}
}

type adflowService struct {
	*Service
	luncaiRepo               repository.LuncaiRepository
	orderRepo                repository.UserOrderRepository
	paipanRecordRepo         repository.UserPaipanRecordRepository
	yunshiRepo               repository.YunshiRepository
	doraemon                 *wecom.Application
	qwRepository             repository.QwRepository
	adflowRepo               repository.AdflowRepository
	dateRepo                 repository.DateRepository
	submailRepo              repository.SubMailRepository
	userEventRepo            repository.UserEventRepository
	submailer                *submail.Client
	dizhiWuxing              map[string]string   // 地支五行
	tianganWuxing            map[string]string   // 天干五行
	wuxingXiangsheng         map[string]string   // 五行相生
	dyGanzhiShishenWangshuai map[string][]string // 大运干支十神旺衰
}

func (slf *adflowService) MingliBaziClick(ctx context.Context) error {
	if _, err := slf.userEventRepo.CreateUserEvent(ctx, &model.UserEvent{
		AppID:     6, // 在线投放
		EventType: 1, // 点击命理八字排盘
		EventTime: time.Now(),
	}); err != nil {
		return err
	}
	return nil
}

func (slf *adflowService) MingliBaziSMS(ctx context.Context, req *v1.MingliBaziSMSRequest) error {
	// 检查订单
	order, err := slf.orderRepo.FetchOrderByMingliBaziID(ctx, req.ID)
	if err != nil {
		return err
	}
	if order == nil {
		return v1.ErrBadRequest
	}
	data, exist := order.ExtraInfo["minglibazi"]
	if !exist {
		return v1.ErrBadRequest
	}
	bazi := data.(map[string]any)
	if bazi["phone"] != nil {
		return v1.ErrBadRequest
	}
	var (
		smsAddress      = req.Phone
		smsTemplateID   = "bsdwT"
		smsTemplateVars = map[string]any{
			"code": fmt.Sprintf("MLBZ%d", req.ID),
		}
	)
	res, err := slf.submailer.SMS().XSend(ctx, &submail.SMSXSendRequest{
		To:      smsAddress,
		Project: smsTemplateID,
		Vars:    smsTemplateVars,
	})
	if err != nil {
		return err
	}
	if res.Status != "success" {
		return errors.Errorf("发送短信失败：%s", res.Msg)
	}
	// 更新订单
	bazi["phone"] = req.Phone
	order.ExtraInfo["minglibazi"] = bazi
	if err = slf.orderRepo.UpdateOrderExtraInfo(ctx, order.OrderNo, order.ExtraInfo); err != nil {
		return err
	}
	// 记录短信发送记录
	if _, err = slf.submailRepo.CreateSubMailSMS(ctx, &model.SubMailSMS{
		App:        slf.submailer.AppID,
		Address:    req.Phone,
		TemplateID: smsTemplateID,
		Vars:       smsTemplateVars,
		SendID:     res.SendID,
	}); err != nil {
		return err
	}
	return nil
}

func (slf *adflowService) MingliBaziQwLink(ctx context.Context, req *v1.MingliBaziQwLinkRequest) (*v1.MingliBaziQwLinkResponseData, error) {
	record, err := slf.paipanRecordRepo.FetchPaipanRecordByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if record == nil || record.AppID != 6 {
		return nil, v1.ErrBadRequest
	}
	order, err := slf.orderRepo.FetchOrderByMingliBaziID(ctx, record.ID)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, v1.ErrBadRequest
	}
	var (
		addState = fmt.Sprintf("ADFLOW_MLBZ_%d_%s", record.ID, req.Button)
		name     = "在线投放-命理八字"
	)
	user, err := slf.adflowRepo.FetchQwUserIDs(ctx)
	if err != nil {
		return nil, err
	}
	if len(user) == 0 {
		return nil, v1.ErrBadRequest
	}
	var linkUrl string
	if req.Type == 1 { // 联系我
		qwResp, err := slf.doraemon.ExternalContactContactWay.Add(ctx, &contactWayRequest.RequestAddContactWay{
			Type:       1,        // 多人
			Scene:      2,        // 二维码
			Remark:     name,     // 名称
			SkipVerify: true,     // 是否跳过验证
			User:       user,     // 员工ID
			State:      addState, // 添加参数
		})
		if err != nil {
			return nil, err
		}
		if qwResp.ErrCode != 0 {
			return nil, errors.Errorf("创建联系我失败：code=%d, msg=%s", qwResp.ErrCode, qwResp.ErrMsg)
		}
		linkUrl = qwResp.QRCode
		if err = slf.tx.Transaction(ctx, func(ctx context.Context) error {
			if _, err = slf.qwRepository.CreateContactWay(ctx, &model.QwContactWay{
				Type:       req.Type,
				AppID:      6,
				PlatformID: 1,
				Name:       name,
				Link:       qwResp.QRCode,
				UserIDs:    user,
				ConfigID:   qwResp.ConfigID,
				SkipVerify: true,
				AddState:   addState,
			}); err != nil {
				return err
			}
			if _, err = slf.adflowRepo.InsertQwLink(ctx, &model.AdflowQwLink{
				AddState: addState,
				UserIds:  user,
				QrCode:   qwResp.QRCode,
				ConfigId: qwResp.ConfigID,
				PaipanId: req.ID,
				Phone:    req.Phone,
			}); err != nil {
				return err
			}
			return nil
		}); err != nil {
			return nil, err
		}
	} else { // 查询已有的记录
		linkUrl = "https://work.weixin.qq.com/ca/cawcde801e8ce78a5b" // RenSheng
		linkUrl = func() string {
			fullURL := fmt.Sprintf("%s?customer_channel=%s", linkUrl, addState)
			//encodedURL := url.QueryEscape(fullURL)
			//scheme := fmt.Sprintf("weixin://biz/ww/profile/%s", encodedURL)
			//return scheme
			return fullURL
		}()
	}
	return &linkUrl, nil
}

func (slf *adflowService) MingliBaziYear(ctx context.Context, req *v1.MingliBaziYearRequest) (*v1.MingliBaziYearResponseData, error) {
	record, err := slf.paipanRecordRepo.FetchPaipanRecordByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if record == nil || record.AppID != 6 {
		return nil, nil
	}
	order, err := slf.orderRepo.FetchOrderByMingliBaziID(ctx, record.ID)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, nil
	}
	data, exist := order.ExtraInfo["minglibazi"]
	if !exist {
		return nil, nil
	}
	bazi := data.(map[string]any)
	req.Name = bazi["name"].(string)
	req.Gender = lo.Ternary(bazi["gender"] == 1, "男", "女")
	req.Birthtime = bazi["birthtime"].(string)
	tmp := bazi["birthplace"].([]any)
	req.Birthplace = func() []string {
		var result []string
		for _, item := range tmp {
			result = append(result, item.(string))
		}
		return result
	}()

	var tianganMapWuxing4Shishen = map[string][]string{
		"甲": {"水", "木", "火", "土", "金"},
		"乙": {"水", "木", "火", "土", "金"},
		"丙": {"木", "火", "土", "金", "水"},
		"丁": {"木", "火", "土", "金", "水"},
		"戊": {"火", "土", "金", "水", "木"},
		"己": {"火", "土", "金", "水", "木"},
		"庚": {"土", "金", "水", "木", "火"},
		"辛": {"土", "金", "水", "木", "火"},
		"壬": {"金", "水", "木", "火", "土"},
		"癸": {"金", "水", "木", "火", "土"},
	}

	if req.Birthplace == nil {
		req.Birthplace = []string{}
	}
	birthtime, err := time.Parse("2006-01-02 15:04:05", req.Birthtime)
	if err != nil {
		return nil, errors.Wrap(err, "time.Parse")
	}
	birthtimeSun, err := slf.realSunTime(ctx, birthtime, req.Birthplace)
	if err != nil {
		return nil, err
	}
	realSunTimeStr := birthtimeSun.Format("2006-01-02 15:04:05")
	result := &v1.YunshiResponseData{
		Guaxiang:    nil,
		Score:       &v1.YunshiScore{},
		Suggestions: make([]string, 0),
	}
	// 排盘ALL
	paipanAll, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: realSunTimeStr,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, err
	}
	result.Mingzhu = &v1.YunshiMinzhu{
		Name:           req.Name,
		Birthtime:      req.Birthtime,
		BirthtimeLunar: strs.Digit2ZhUpper(paipanAll.Nongli),
		Birthplace:     req.Birthplace,
		Gender:         req.Gender,
		Bazi:           paipanAll.Sizhu,
		Wuxing:         strings.SplitN(paipanAll.Xiyongjichou, ",", 5),
		Zodiac:         paipanAll.Shengxiao,
	}

	// 论财-卦象
	paipanDayun, err := slf.coronaCli.GetDayun(ctx, &corona.GetDayunRequest{
		Birthtime: req.Birthtime,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, err
	}
	ganzhiName4Dayun, err := func(current, start int, dayunList []string) (string, error) {
		diff := current - start
		if diff < 0 || current > start+10*len(dayunList)-1 {
			return "", v1.ErrInputYearOutOfRange
		}
		return dayunList[diff/10], nil
	}(req.CurrentYear, paipanDayun.DayunQishi, paipanDayun.GetShierDayun)
	if err != nil {
		return nil, err
	}
	shizhuCurrentYear, err := slf.coronaCli.Shizhu(ctx, &corona.GetShizhuRequest{Birthtime: fmt.Sprintf("%d-03-01 00:00:00", req.CurrentYear)})
	if err != nil {
		return nil, err
	}
	ganzhiName4CurrentYear := shizhuCurrentYear.Nianzhu
	ganzhi4Dayun, err := slf.yunshiRepo.GetGanzhiByName(ctx, ganzhiName4Dayun)
	if err != nil {
		return nil, err
	}
	ganzhi4CurrentYear, err := slf.yunshiRepo.GetGanzhiByName(ctx, ganzhiName4CurrentYear)
	if err != nil {
		return nil, err
	}
	value := int(ganzhi4Dayun.ID+ganzhi4CurrentYear.ID) % 64
	if value == 0 {
		value = 64
	}
	guaxiang, err := slf.yunshiRepo.GetGuaxiangByValue(ctx, value)
	if err != nil {
		return nil, err
	}
	result.Guaxiang = guaxiang

	// 论财-运势分
	yunshi := result.Score
	// 计分系数：固定值0.5
	req.Coefficient = 0.5
	var (
		timeStrCurrentYear = fmt.Sprintf("%d-03-01 00:00:00", req.CurrentYear)
		timeStrLastYear    = fmt.Sprintf("%d-03-01 00:00:00", req.CurrentYear-1)
	)
	paipanJieqiScoreCurrentYear, err := slf.coronaCli.GetJieqiScore(ctx, &corona.GetJieqiScoreRequest{
		Birthtime:   req.Birthtime,
		Gender:      req.Gender,
		CurrentTime: timeStrCurrentYear,
	})
	if err != nil {
		return nil, err
	}
	paipanJieqiScoreLastYear, err := slf.coronaCli.GetJieqiScore(ctx, &corona.GetJieqiScoreRequest{
		Birthtime:   req.Birthtime,
		Gender:      req.Gender,
		CurrentTime: timeStrLastYear,
	})
	if err != nil {
		return nil, err
	}
	type MonthObj struct {
		Jieqi   string    // 节气
		Score   int       // 分数
		TimeStr string    // 时间字符串
		Time    time.Time // 时间
		Ganzhi  string    // 干支
		LnScore int       // 流年分数
		DyScore int       // 大运分数
	}
	var months []MonthObj
	splitN := strings.SplitN(paipanJieqiScoreLastYear.PaiyueJq[12-1], ": ", 2)
	parse, err := time.Parse("2006-01-02 15:04:05", splitN[1])
	if err != nil {
		return nil, err
	}
	months = append(months, MonthObj{
		Jieqi:   splitN[0],
		Score:   paipanJieqiScoreLastYear.NowMonthFen[12-1],
		TimeStr: splitN[1],
		Time:    parse,
		Ganzhi:  paipanJieqiScoreLastYear.MonthGanzhiList[12-1],
	})
	for i, jieqi := range paipanJieqiScoreCurrentYear.PaiyueJq {
		splitN := strings.SplitN(jieqi, ": ", 2)
		parse, err := time.Parse("2006-01-02 15:04:05", splitN[1])
		if err != nil {
			return nil, err
		}
		months = append(months, MonthObj{
			Jieqi:   splitN[0],
			Score:   paipanJieqiScoreCurrentYear.NowMonthFen[i],
			TimeStr: splitN[1],
			Time:    parse,
			Ganzhi:  paipanJieqiScoreCurrentYear.MonthGanzhiList[i],
		})
	}
	paipanDayunLiulianScore, err := slf.coronaCli.GetDayunLiunianScore(ctx, &corona.GetDayunLiunianScoreRequest{
		Birthtime: req.Birthtime,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, err
	}
	var (
		lnIndex1 = months[0].Time.Year() - paipanDayun.DayunQishi // 流年索引1（1月取上一年）
		lnIndex2 = months[1].Time.Year() - paipanDayun.DayunQishi // 流年索引2（2月及以后取当前年）
		lnScore1 = paipanDayunLiulianScore.Lnian[lnIndex1]
		lnScore2 = paipanDayunLiulianScore.Lnian[lnIndex2]
		dyScore1 = paipanDayunLiulianScore.Dyun[lnIndex1/5]
		dyScore2 = paipanDayunLiulianScore.Dyun[lnIndex2/5]
	)
	for i, obj := range months {
		if i == 12 {
			continue
		}
		if i == 0 {
			obj.LnScore = lnScore1
			obj.DyScore = dyScore1
		} else {
			obj.LnScore = lnScore2
			obj.DyScore = dyScore2
		}
	}
	yunshi.Dayun = &v1.YunshiScoreDayun{
		Ganzhi: ganzhiName4Dayun,
		Score:  float32(dyScore2),
	}
	var (
		lnGanzhi       = shizhuCurrentYear.Nianzhu
		lnOverallScore = float32(dyScore2)*req.Coefficient + float32(lnScore2)*(1-req.Coefficient)
		lnFinalScore   = lnOverallScore*0.5 + 50
	)
	yunshi.Liunian = &v1.YunshiNianScore{
		InitialScore: float32(lnScore2),
		OverallScore: lnOverallScore,
		FinalScore:   lnFinalScore,
		Keyword: func(score float32) string {
			if score >= 90 {
				return "如日中天"
			} else if score >= 80 && score < 90 {
				return "顺风顺水"
			} else if score >= 70 && score < 80 {
				return "平平稳稳"
			} else if score >= 60 && score < 70 {
				return "砥砺前行"
			} else {
				return "步履维艰"
			}
		}(lnFinalScore),
		Ganzhi: lnGanzhi,
	}
	for i, obj := range months {
		if i == 12 { // 第13个月只返回节气（用于前端显示最佳/谨慎月份时间范围）
			yunshi.Liuyue = append(yunshi.Liuyue, &v1.YunshiYueScore{
				Jieqi:     obj.Jieqi,
				JieqiTime: obj.TimeStr,
			})
			continue
		}
		var (
			lyOverallScore = (float32(obj.DyScore)*req.Coefficient+float32(obj.LnScore)*(1-req.Coefficient))*req.Coefficient + float32(obj.Score)*(1-req.Coefficient)
			lyFinalScore   = lyOverallScore*0.5 + 50
		)
		yunshi.Liuyue = append(yunshi.Liuyue, &v1.YunshiYueScore{
			Jieqi:        obj.Jieqi,
			Ganzhi:       obj.Ganzhi,
			JieqiTime:    obj.TimeStr,
			InitialScore: float32(obj.Score),
			OverallScore: lyOverallScore,
			FinalScore:   lyFinalScore,
		})
	}

	// 论财-建议
	// 本命年
	shizhuBirthYear, err := slf.coronaCli.Shizhu(ctx, &corona.GetShizhuRequest{Birthtime: req.Birthtime})
	if err != nil {
		return nil, err
	}
	mzNianGanzhi := shizhuBirthYear.Nianzhu
	mzNianGan := string([]rune(mzNianGanzhi)[0])
	mzShiGanzhi := shizhuBirthYear.Shizhu
	splitN = strings.SplitN(paipanJieqiScoreCurrentYear.PaiyueJq[0], ": ", 2)
	parse, err = time.Parse("2006-01-02 15:04:05", splitN[1])
	if err != nil {
		return nil, err
	}
	if string([]rune(mzNianGanzhi)[1]) == string([]rune(lnGanzhi)[1]) {
		result.Suggestions = append(result.Suggestions, "今年值本命年，凡事须小心。")
	}
	// 流年地支喜忌
	paipanShizhuBirthBaziAndShengxiao := []string{
		shizhuBirthYear.Nianzhu,
		shizhuBirthYear.Yuezhu,
		shizhuBirthYear.Rizhu,
		shizhuBirthYear.Shizhu,
		shizhuBirthYear.Zodiac,
	}
	mzYueGanzhi := paipanShizhuBirthBaziAndShengxiao[1]
	mzYueGan := string([]rune(mzYueGanzhi)[0])
	mzYueZhi := string([]rune(mzYueGanzhi)[1])
	mzRiGanzhi := paipanShizhuBirthBaziAndShengxiao[2]
	mzRiGan := string([]rune(mzRiGanzhi)[0])
	mzRiZhi := string([]rune(mzRiGanzhi)[1])
	lnGan := string([]rune(lnGanzhi)[0])
	lnZhi := string([]rune(lnGanzhi)[1])
	lnShishen, err := slf.yunshiRepo.GetShishenByRiyuan(ctx, mzRiGan, lnZhi)
	if err != nil {
		return nil, err
	}
	shishen := lnShishen.Shishen
	sls, err := slf.yunshiRepo.GetShishenLiunianSuggestion(ctx, shishen, req.Gender, result.Score.Liunian.OverallScore > 50)
	if err != nil {
		return nil, err
	}
	if sls != nil {
		result.Suggestions = append(result.Suggestions, sls.Suggestion)
	}
	// 流年神煞
	nayinList, err := slf.yunshiRepo.Nayins(ctx)
	if err != nil {
		return nil, err
	}
	//shenshaRules, err := slf.yunshiRepo.Shenshas(ctx)
	//if err != nil {
	//	return nil, err
	//}
	nayinMap := make(map[string]*model.Nayin)
	for _, item := range nayinList {
		nayinMap[item.Zhu] = item
	}

	shenshaResult, err := slf.coronaCli.Shensha(ctx, &corona.GetShenshaRequest{
		Bazi: []string{
			paipanShizhuBirthBaziAndShengxiao[0],
			paipanShizhuBirthBaziAndShengxiao[1],
			paipanShizhuBirthBaziAndShengxiao[2],
			paipanShizhuBirthBaziAndShengxiao[3],
			ganzhiName4Dayun,
			lnGanzhi,
		},
		Gender: req.Gender,
	})
	if err != nil {
		return nil, err
	}
	nianShenshaSet := make(map[string]bool)
	for _, item := range shenshaResult.GetByIndex(0) {
		nianShenshaSet[item] = true
	}
	yueShenshaSet := make(map[string]bool)
	for _, item := range shenshaResult.GetByIndex(1) {
		yueShenshaSet[item] = true
	}
	riShenshaSet := make(map[string]bool)
	for _, item := range shenshaResult.GetByIndex(2) {
		riShenshaSet[item] = true
	}
	shiShenshaSet := make(map[string]bool)
	for _, item := range shenshaResult.GetByIndex(3) {
		shiShenshaSet[item] = true
	}
	dyShenshaSet := make(map[string]bool)
	for _, item := range shenshaResult.GetByIndex(4) {
		dyShenshaSet[item] = true
	}
	lnShenshasSet := make(map[string]bool)
	for _, item := range shenshaResult.GetByIndex(5) {
		lnShenshasSet[item] = true
	}
	if lnShenshasSet["驿马"] && !lnShenshasSet["禄神"] {
		result.Suggestions = append(result.Suggestions, "今年奔走较为频繁。")
	}
	if lnShenshasSet["驿马"] && lnShenshasSet["禄神"] {
		result.Suggestions = append(result.Suggestions, "今年升迁有望，但不可掉以轻心，把握机会。")
	}
	if lnShenshasSet["华盖"] {
		result.Suggestions = append(result.Suggestions, "今年需要谨慎婚姻，学术研究方面或有成就。")
	}
	if lnShenshasSet["桃花"] {
		result.Suggestions = append(result.Suggestions, "今年可能有艳遇，多有恋爱之事，已婚人士请谨慎。")
	}
	if lnShenshasSet["灾煞"] {
		result.Suggestions = append(result.Suggestions, "今年可能有不好的事情发生，建议谨慎行事。")
	}
	if lnShenshasSet["羊刃"] {
		result.Suggestions = append(result.Suggestions, "今年可能有凶灾或破损耗财的事情发生，建议谨慎行事。")
	}
	if lnShenshasSet["空亡"] {
		result.Suggestions = append(result.Suggestions, "今年发现特殊buff，增益效果减少50%，减益效果减少50%。")
	}
	// 流年大运十神
	dyGan := string([]rune(ganzhiName4Dayun)[0])
	dyZhi := string([]rune(ganzhiName4Dayun)[1])
	riTiangan := string([]rune(paipanShizhuBirthBaziAndShengxiao[2])[0])
	dyShishen, err := slf.yunshiRepo.GetShishenByRiyuan(ctx, riTiangan, dyZhi)
	if err != nil {
		return nil, err
	}
	jiuzhuData, err := slf.coronaCli.LifeCyclesMonth(ctx, &corona.LifeCyclesMonthRequest{
		Birthday: req.Birthtime,
		Now:      fmt.Sprintf("%d-03-01 00:00:00", req.CurrentYear),
		Gender:   lo.Ternary(req.Gender == "男", 1, 2),
		Location: []string{},
	})
	if err != nil {
		return nil, err
	}
	sizhuHasCai := jiuzhuData.ZhuXing[0] == "正财" || jiuzhuData.ZhuXing[0] == "偏财" ||
		jiuzhuData.ZhuXing[1] == "正财" || jiuzhuData.ZhuXing[1] == "偏财" ||
		jiuzhuData.ZhuXing[2] == "正财" || jiuzhuData.ZhuXing[2] == "偏财" ||
		jiuzhuData.ZhuXing[3] == "正财" || jiuzhuData.ZhuXing[3] == "偏财" ||
		jiuzhuData.BenqiShiShen[0] == "正财" || jiuzhuData.BenqiShiShen[0] == "偏财" ||
		jiuzhuData.BenqiShiShen[1] == "正财" || jiuzhuData.BenqiShiShen[1] == "偏财" ||
		jiuzhuData.BenqiShiShen[2] == "正财" || jiuzhuData.BenqiShiShen[2] == "偏财" ||
		jiuzhuData.BenqiShiShen[3] == "正财" || jiuzhuData.BenqiShiShen[3] == "偏财" ||
		jiuzhuData.ZhongqiShiShen[0] == "正财" || jiuzhuData.ZhongqiShiShen[0] == "偏财" ||
		jiuzhuData.ZhongqiShiShen[1] == "正财" || jiuzhuData.ZhongqiShiShen[1] == "偏财" ||
		jiuzhuData.ZhongqiShiShen[2] == "正财" || jiuzhuData.ZhongqiShiShen[2] == "偏财" ||
		jiuzhuData.ZhongqiShiShen[3] == "正财" || jiuzhuData.ZhongqiShiShen[3] == "偏财" ||
		jiuzhuData.YuqiShiShen[0] == "正财" || jiuzhuData.YuqiShiShen[0] == "偏财" ||
		jiuzhuData.YuqiShiShen[1] == "正财" || jiuzhuData.YuqiShiShen[1] == "偏财" ||
		jiuzhuData.YuqiShiShen[2] == "正财" || jiuzhuData.YuqiShiShen[2] == "偏财" ||
		jiuzhuData.YuqiShiShen[3] == "正财" || jiuzhuData.YuqiShiShen[3] == "偏财"
	if (lnShishen.Shishen == "正财" || lnShishen.Shishen == "偏财" || dyShishen.Shishen == "正财" || dyShishen.Shishen == "偏财") &&
		(nianShenshaSet["羊刃"] || yueShenshaSet["羊刃"] || riShenshaSet["羊刃"] || shiShenshaSet["羊刃"]) &&
		(dyShenshaSet["羊刃"] || lnShenshasSet["羊刃"]) {
		if sizhuHasCai {
			result.Suggestions = append(result.Suggestions, "今年可能发生财物耗散。")
		} else {
			result.Suggestions = append(result.Suggestions, "今年可能发生轻微的财务耗散。")
		}
	}
	sizhuHasZhengguan := jiuzhuData.ZhuXing[0] == "正官" || jiuzhuData.ZhuXing[1] == "正官" || jiuzhuData.ZhuXing[2] == "正官" || jiuzhuData.ZhuXing[3] == "正官" ||
		jiuzhuData.BenqiShiShen[0] == "正官" || jiuzhuData.BenqiShiShen[1] == "正官" || jiuzhuData.BenqiShiShen[2] == "正官" || jiuzhuData.BenqiShiShen[3] == "正官" ||
		jiuzhuData.ZhongqiShiShen[0] == "正官" || jiuzhuData.ZhongqiShiShen[1] == "正官" || jiuzhuData.ZhongqiShiShen[2] == "正官" || jiuzhuData.ZhongqiShiShen[3] == "正官" ||
		jiuzhuData.YuqiShiShen[0] == "正官" || jiuzhuData.YuqiShiShen[1] == "正官" || jiuzhuData.YuqiShiShen[2] == "正官" || jiuzhuData.YuqiShiShen[3] == "正官"
	if req.Gender == "女" && sizhuHasCai && sizhuHasZhengguan {
		if lnShishen.Shishen == "伤官" && dyShishen.Shishen == "劫财" {
			result.Suggestions = append(result.Suggestions, "今年你的婚姻或者你老公的事业财运，存在些许波折。")
		}
		if lnShishen.Shishen == "劫财" && dyShishen.Shishen == "伤官" {
			result.Suggestions = append(result.Suggestions, "今年你的婚姻或者你老公的事业财运，存在些许波折。")
		}
	}
	if req.Gender == "女" && func() bool {
		num := 0
		for i := 0; i < 4; i++ {
			if jiuzhuData.ZhuXing[i] == "比劫" {
				num++
			}
			if jiuzhuData.BenqiShiShen[i] == "比劫" {
				num++
			}
			if jiuzhuData.ZhongqiShiShen[i] == "比劫" {
				num++
			}
			if jiuzhuData.YuqiShiShen[i] == "比劫" {
				num++
			}
		}
		return num >= 4
	}() {
		if lnShishen.Shishen == "官杀" || dyShishen.Shishen == "官杀" {
			result.Suggestions = append(result.Suggestions, "今年你的婚姻或者你老公的事业财运，存在些许波折。")
		}
	}
	riShishen, err := slf.yunshiRepo.GetShishenByRiyuan(ctx, riTiangan, jiuzhuData.Dizhi[2])
	if err != nil {
		return nil, err
	}
	if riShishen.Shishen == "正财" || riShishen.Shishen == "偏财" {
		if (dyShishen.Shishen == "正财" || dyShishen.Shishen == "偏财") ||
			(lnShishen.Shishen == "正财" || lnShishen.Shishen == "偏财") {
			result.Suggestions = append(result.Suggestions, "今年走财运，容易发财。")
		}
	}
	paipanWuxing, err := slf.coronaCli.GetSizhuWuxingXiyong(ctx, &corona.GetSizhuWuxingXiyongRequest{
		Birthtime: req.Birthtime,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, err
	}
	wuxingName := tianganMapWuxing4Shishen[riTiangan][0]
	wuxingIndex := 0
	for i, name := range strings.Split(paipanWuxing.SaveLiliangWuXingMingzi, ",") {
		if wuxingName == name {
			wuxingIndex = i
			break
		}
	}
	yinxiaoNengliang := paipanWuxing.SaveLiliangNum[wuxingIndex]
	if yinxiaoNengliang >= 150 {
		changsheng, err := slf.yunshiRepo.GetChangshengByName(ctx, "帝旺")
		if err != nil {
			return nil, err
		}
		if changsheng.Mapping[riTiangan] == lnZhi || changsheng.Mapping[riTiangan] == dyZhi {
			result.Suggestions = append(result.Suggestions, "今年登科有望，读书考试即为顺利，但也要好好学习，切勿掉以轻心。")
		}
	}
	if (lnShishen.Shishen == "食神" || lnShishen.Shishen == "伤官") &&
		(dyShishen.Shishen == "食神" || dyShishen.Shishen == "伤官") {
		result.Suggestions = append(result.Suggestions, "今年务必要注重安全，小心因粗心或意外造成的事情。")
	}
	mzNianzhi := string([]rune(mzNianGanzhi)[1])
	mzShizhi := string([]rune(mzShiGanzhi)[1])
	dzxc, err := slf.yunshiRepo.GetDizhiXiangchong(ctx, mzNianzhi, mzShizhi)
	if err != nil {
		return nil, err
	}
	if dzxc != nil && (dyZhi == mzShizhi || lnZhi == mzShizhi) {
		result.Suggestions = append(result.Suggestions, "建议今年多多关心父母身体与安全。")
	}
	lnGan1, err := slf.yunshiRepo.GetTiangan(ctx, lnGan)
	if err != nil {
		return nil, err
	}
	mzNianGan1, err := slf.yunshiRepo.GetTiangan(ctx, mzNianGan)
	if err != nil {
		return nil, err
	}
	wxxk, err := slf.yunshiRepo.GetWuxingXiangke(ctx, lnGan1.Wuxing, mzNianGan1.Wuxing)
	if err != nil {
		return nil, err
	}
	if wxxk != nil {
		mzNianBenqi := jiuzhuData.Benqi[0]
		changsheng, err := slf.yunshiRepo.GetChangshengByName(ctx, "墓")
		if err != nil {
			return nil, err
		}
		if changsheng.Mapping[mzNianBenqi] == lnZhi {
			result.Suggestions = append(result.Suggestions, "建议今年多多关心父母，尤其是父亲的身体与安全。")
		}
	}
	var (
		dzxcIdx = 0
		isDzxc  = false
	)
	for i := 0; i < 4; i++ {
		dizhiMz := jiuzhuData.Dizhi[i]
		dzxc1, err := slf.yunshiRepo.GetDizhiXiangchong(ctx, lnZhi, dizhiMz)
		if err != nil {
			return nil, err
		}
		dzxc2, err := slf.yunshiRepo.GetDizhiXiangchong(ctx, dyZhi, dizhiMz)
		if err != nil {
			return nil, err
		}
		if dzxc1 != nil || dzxc2 != nil {
			dzxcIdx = i
			isDzxc = true
			break
		}
	}
	if isDzxc && func() bool {
		hasThOrYr := false
		for _, shensha := range jiuzhuData.ShenShaJiShen[dzxcIdx] {
			if shensha == "桃花" || shensha == "羊刃" {
				hasThOrYr = true
				break
			}
		}
		return hasThOrYr
	}() {
		result.Suggestions = append(result.Suggestions, "今年运冲桃花，注意不要因色犯刑。")
	}
	if (lnShishen.Shishen == "正财" || lnShishen.Shishen == "偏财") && (dyShishen.Shishen == "正财" || dyShishen.Shishen == "偏财") {
		if func() bool {
			hasYm := false
			for i := 0; i < 4; i++ {
				for _, shensha := range jiuzhuData.ShenShaJiShen[i] {
					if shensha == "驿马" {
						hasYm = true
						break
					}
				}
			}
			return hasYm
		}() {
			result.Suggestions = append(result.Suggestions, "今年有机会大发其财，请好好把握。")
		}
	}
	if req.CurrentYear-birthtimeSun.Year() > 60 && func() bool {
		hasYm := false
		for i := 4; i < 6; i++ {
			for _, shensha := range jiuzhuData.ShenShaJiShen[i] {
				if shensha == "驿马" {
					hasYm = true
					break
				}
			}
		}
		return hasYm
	}() {
		result.Suggestions = append(result.Suggestions, "今年易发生气虚，注意腰腿方面的毛病。")
	}
	if (lnGanzhi == "戊戌" || lnGanzhi == "庚辰" || lnGanzhi == "庚戌" || lnGanzhi == "壬辰") &&
		(mzRiGanzhi == "戊戌" || mzRiGanzhi == "庚辰" || mzRiGanzhi == "庚戌" || mzRiGanzhi == "壬辰") {
		result.Suggestions = append(result.Suggestions, "公职为官之人，今年有提升方面的喜事。（青天大老爷心里多念念百姓）")
	}
	wuhe, err := slf.yunshiRepo.GetTianganWuhe(ctx, mzYueGan, lnGan)
	if err != nil {
		return nil, err
	}
	liuhe, err := slf.yunshiRepo.GetDizhiLiuhe(ctx, mzYueZhi, lnZhi)
	if err != nil {
		return nil, err
	}
	if wuhe != nil && liuhe != nil {
		result.Suggestions = append(result.Suggestions, "今年天合地合，家有喜事。")
	}
	tiangan, err := slf.yunshiRepo.GetTiangan(ctx, mzRiGan)
	if err != nil {
		return nil, err
	}
	wuxing, err := slf.yunshiRepo.GetWuxingAboutXiangkeByName(ctx, tiangan.Wuxing)
	dizhiList := []string{dyZhi, mzNianzhi, mzYueZhi, mzRiZhi, mzShizhi}
	dizhiSet := make(map[string]struct{})
	for _, dz := range dizhiList {
		dizhiSet[dz] = struct{}{}
	}
	dizhiList = []string{}
	for s := range dizhiSet {
		dizhiList = append(dizhiList, s)
	}
	array := algo.CombinationsFromArray(dizhiList, 3)
	mzYueZhiWuxing, err := slf.yunshiRepo.GetDizhi(ctx, mzYueZhi)
	if err != nil {
		return nil, err
	}
	mzRiGanWuxing, err := slf.yunshiRepo.GetTiangan(ctx, mzRiGan)
	if err != nil {
		return nil, err
	}
	riyuan := paipanAll.Riyuan
	if func() bool {
		isFound := false
		for _, com := range array {
			sanhe, err := slf.yunshiRepo.GetDizhiSanhe(ctx, com[0], com[1], com[2])
			if err != nil {
				return false
			}
			if sanhe != nil && wuxing.Xiangke == sanhe.Hehua {
				isFound = true
				break
			}
		}
		return isFound
	}() {
		if riyuan == "平和" {
			xs1, err := slf.yunshiRepo.GetWuxingXiangsheng(ctx, mzYueZhiWuxing.Wuxing, mzRiGanWuxing.Wuxing)
			if err != nil {
				return nil, err
			}
			xs2, err := slf.yunshiRepo.GetWuxingXiangsheng(ctx, mzRiGanWuxing.Wuxing, mzYueZhiWuxing.Wuxing)
			if err != nil {
				return nil, err
			}
			xk1, err := slf.yunshiRepo.GetWuxingXiangke(ctx, mzYueZhiWuxing.Wuxing, mzRiGanWuxing.Wuxing)
			if err != nil {
				return nil, err
			}
			xk2, err := slf.yunshiRepo.GetWuxingXiangke(ctx, mzRiGanWuxing.Wuxing, mzYueZhiWuxing.Wuxing)
			if err != nil {
				return nil, err
			}
			if xs1 != nil || mzYueZhiWuxing.Wuxing == mzRiGanWuxing.Wuxing {
				riyuan = "身强"
			} else if xs2 != nil || (xk1 != nil || xk2 != nil) {
				riyuan = "身弱"
			}
		}
		if riyuan == "身强" || riyuan == "从弱" {
			result.Suggestions = append(result.Suggestions, "今年的大运财运极好。")
		}
		if riyuan == "身弱" || riyuan == "从强" {
			result.Suggestions = append(result.Suggestions, "今年的大运钱财花销损耗较大。")
		}
	}
	wuhe, err = slf.yunshiRepo.GetTianganWuhe(ctx, dyGan, mzRiGan)
	if err != nil {
		return nil, err
	}
	if wuhe != nil && (riyuan == "身强" || riyuan == "从弱") {
		xk, err := slf.yunshiRepo.GetWuxingXiangke(ctx, mzRiGanWuxing.Wuxing, wuhe.Tiangan1)
		if err != nil {
			return nil, err
		}
		if xk != nil {
			result.Suggestions = append(result.Suggestions, "今年的流年财运较好。")
		}
	}
	if func() bool {
		return dyShishen.Shishen == "正财" || dyShishen.Shishen == "偏财"
	}() && (riyuan == "身弱" || riyuan == "从强") {
		result.Suggestions = append(result.Suggestions, "财运方面，易为钱财奔波劳累。")
	}
	// 大运用神到位（大运地支五行为喜用）
	xyjcx := strings.SplitN(paipanWuxing.Xiyongjichou, ",", 5)
	dyZhiWuxing, err := slf.yunshiRepo.GetDizhi(ctx, dyZhi)
	if err != nil {
		return nil, err
	}
	if dyZhiWuxing.Wuxing == xyjcx[0] || dyZhiWuxing.Wuxing == xyjcx[1] {
		result.Suggestions = append(result.Suggestions, "今年大运到位，今年总体平安、兴旺、发达。")
	}
	// 大运最后一年
	if (req.CurrentYear >= paipanDayun.DayunQishi) && ((req.CurrentYear-paipanDayun.DayunQishi)%10 == 9) {
		result.Suggestions = append(result.Suggestions, "今年是大运将交未交的最后一年，往往有人生重大之变动，建议谨慎度过。")
	}
	// 相关事宜
	switch lnShishen.Shishen {
	case "比肩", "劫财":
		result.Suggestions = append(result.Suggestions, "今年主要发生和朋友，同事，兄弟姐妹有关的事。")
	case "食神", "伤官":
		result.Suggestions = append(result.Suggestions, "今年主要发生和女命的子女、晚辈、下属相关的，或者作品，演说，言论，演出，跳舞，展示，投资，策划之类事情。")
	case "正财", "偏财":
		if req.Gender == "男" {
			result.Suggestions = append(result.Suggestions, "今年主要发生和同妻子，父亲，财运，身体健康工作以及婚姻，感情方面之事。")
		} else {
			result.Suggestions = append(result.Suggestions, "今年主要发生和同父亲，财运，身体健康、工作方面之事。")
		}
	case "正官", "七杀":
		if req.Gender == "男" {
			result.Suggestions = append(result.Suggestions, "今年主要发生和同父亲，工作，职务，职业，名誉，官司，病伤灾等方面管你之事。")
		} else {
			result.Suggestions = append(result.Suggestions, "今年主要发生和同老公，工作，职务，职业，名誉，官司，病伤灾等方面管你之事。")
		}
	case "正印", "偏印":
		result.Suggestions = append(result.Suggestions, "今年主要发生和母亲，学习，工作，单位，名誉，票据，住房，疾病，财运有关之事。")
	default:
	}
	return result, nil
}

func (slf *adflowService) MingliBazi(ctx context.Context, req *v1.MingliBaziRequest) (*v1.MingliBaziResponseData, error) {
	if req.Birthplace == nil {
		req.Birthplace = []string{}
	}
	birthtime, err := time.Parse("2006-01-02 15:04:05", req.Birthtime)
	if err != nil {
		return nil, errors.Wrap(err, "time.Parse")
	}
	birthtimeSun, err := slf.realSunTime(ctx, birthtime, req.Birthplace)
	if err != nil {
		return nil, errors.Wrap(err, "realSunTime")
	}
	realSunTimeStr := birthtimeSun.Format("2006-01-02 15:04:05")
	currentTime, err := time.Parse("2006-01-02 15:04:05", req.CurrentTime)
	if err != nil {
		return nil, errors.Wrap(err, "time.Parse")
	}
	paipanAll, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: realSunTimeStr,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.PaipanAll")
	}
	paipanJiuzhu, err := slf.coronaCli.LifeCyclesMonth(ctx, &corona.LifeCyclesMonthRequest{
		Birthday: realSunTimeStr,
		Now:      currentTime.Format("2006-01-02 15:04:05"),
		Gender:   lo.Ternary(req.Gender == "男", 1, 2),
		Location: []string{},
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.PaipanJiuzhu")
	}
	day, err := slf.dateRepo.GetOneDay(ctx, fmt.Sprintf("%d-03-01", currentTime.Year()))
	if err != nil {
		return nil, err
	}
	// =================权限检查=================
	isShow := func() bool {
		// 是否重放（单词购买后根据先前的luncai_id查看结果）
		if req.IsReplay && req.IsPaid { // 重放且支付后显示全部结果
			return true
		}
		return false
	}()
	// =================权限检查=================
	result := &v1.MingliBaziResponseData{
		IsShow:  isShow,
		Liunian: fmt.Sprintf("%d%s%s", currentTime.Year(), day.Bazi1, day.Shengxiao),
	}
	wuxingPower := func() map[string]int {
		m := make(map[string]int)
		splitN1 := strings.SplitN(paipanAll.SaveLiliangWuXingMingzi, ",", 5)
		splitN2 := strings.SplitN(paipanAll.SaveLiliangNum, ",", 5)
		for i, s := range splitN1 {
			m[s], _ = strconv.Atoi(splitN2[i])
		}
		return m
	}()

	// 1.命主信息
	result.Mingzhu = v1.LuncaiMinzhu{
		Name:           req.Name,
		Birthtime:      realSunTimeStr,
		BirthtimeLunar: strs.Digit2ZhUpper(paipanAll.Nongli),
		Birthplace:     req.Birthplace,
		Gender:         req.Gender,
		Bazi:           paipanAll.Sizhu,
		Wuxing:         strings.SplitN(paipanAll.Xiyongjichou, ",", 5),
		Riyuan:         paipanAll.Riyuan,
		Zodiac:         paipanAll.Shengxiao,
		Tiangan:        paipanJiuzhu.Tiangan,
		TianganWuxing: func() []string {
			var res []string
			for _, t := range paipanJiuzhu.Tiangan {
				res = append(res, slf.tianganWuxing[t])
			}
			return res
		}(),
		Dizhi: paipanJiuzhu.Dizhi,
		DizhiWuxing: func() []string {
			var res []string
			for _, d := range paipanJiuzhu.Dizhi {
				res = append(res, slf.dizhiWuxing[d])
			}
			return res
		}(),
		DizhiShishen: func() []string {
			var res []string
			rg := paipanJiuzhu.Tiangan[2] // 日干
			for _, s := range paipanJiuzhu.Dizhi {
				shishen, _ := slf.luncaiRepo.GetShishenByRiyuan(ctx, rg, s)
				res = append(res, shishen.Shishen)
			}
			return res
		}(),
		Zhuxing:        paipanJiuzhu.ZhuXing,
		Benqi:          paipanJiuzhu.Benqi,
		BenqiShishen:   paipanJiuzhu.BenqiShiShen,
		Zhongqi:        paipanJiuzhu.Zhongqi,
		ZhongqiShishen: paipanJiuzhu.ZhongqiShiShen,
		Yuqi:           paipanJiuzhu.Yuqi,
		YuqiShishen:    paipanJiuzhu.YuqiShiShen,
		ShenshaJishen:  paipanJiuzhu.ShenShaJiShen,
		Nayin:          paipanJiuzhu.NaYin,
		WuxingWxxqs: func() map[string]string {
			m := make(map[string]string)
			splitN := strings.SplitN(paipanAll.SaveWxxqsMingzi, ",", 5)
			for _, s := range splitN {
				tmp := strings.SplitN(s, "", 2)
				m[tmp[1]] = tmp[0]
			}
			return m
		}(),
		Qiyun:   paipanAll.RetQyStr,
		Jiaoyun: paipanAll.RetJyStr,
		WuxingNeed: func() string {
			var minKey string
			minValue := math.MaxInt
			for key, value := range wuxingPower {
				if value < minValue {
					minValue = value
					minKey = key
				}
			}
			return minKey
		}(),
	}
	// 性格
	/*
		日干	日干日主	日主心性	诗曰
		甲	甲木日主，	个性是有积极性的开拓精神，精力旺盛，重感情，刚直豪迈，勤勉随和，但好自我显示，直爽当中隐蔽着一点点的虚伪。	甲木日主最聪明，深谋远虑是英雄，五湖四海交朋友，仗义疏财有声名，为友能舍黄膘马，两胁插刀不嫌疼，别人若有为难事，舍已为人也现成。
		乙	乙木日主，	个性沉着是警戒心强，表面现象不愿与人争执，温柔含蓄，但感情脆弱。随风转舵，看人说话。	乙木日主最耿直，心实见景有心机，今日吃他一杯酒，不到明天就还席。为人办事多公道，从来不会占便宜，通情达理多尊让，能忍能让心最慈。
		丙	丙火日主，	个性热情豪爽，自信好胜，善言健谈，欠缺沉着，但情绪不稳，冲动易怒，缺乏仔细、认真。	丙火日主心最刚，心中有话不隐藏，有一句来说一句，心实不怕把人伤，本公办事多公道，通情达理好商量，别人有点为难事，千方百计把忙帮。
		丁	丁火日主，	个性是敦厚朴实，重信守义，但任性逞强，防止受骗，有时缺乏耐力，办事易半途而废。专劲不够。	丁火日主心性急，心中有话当面提，脾气来了赛猛虎，性情发了了不的，心直口快无好处，怕虑灾星在后期，为人在先多快乐，暴打不平属第一。
		戊	戊土日主，	个性是有积极性，干一行易于对事物热衷，但性急，任性而逞强。好胜，欲望高，不满足感强烈，个别时候小气。	戊土日主无转回，一条路上跑到黑，有人不投你的意，话不投机皱双眉，有人若随你心意，吃亏让人百事宜，千日交来万日好，一日无情如扫灰。
		己	已土日主，	个性和缓，谨慎，心灵手巧，但疑虑多，欠果断。容易因情耗财，防止被人牵着走。	已土日主多主才，放出账去要不来，在家发下天大恨，见面生情抹不开，说上几句顺情话，心慈面软你回来，任何自己过不去，好心慈善闷在怀。
		庚	庚金日主，	个性是积极进取，勇敢果决，自尊心强，有才能，善于结交各方面朋友，平衡、稳重，但过于操劳，防止被别人耍花样、弄权术而迷惑受骗受害。	庚金日主性情绵，一生不好讨人嫌。见了人家有灾难，人家落泪你心酸，你待人家一盆火，人家待你冬月寒，时常你就上了当，人家做事你花钱。
		辛	辛金日主，	个性沉着坦直无私，待人接物，认真细致，脚踏实地，但温柔敦厚而寓涵固执，有些自信，劳碌奔波欢快。	辛金日主好为人，交的朋友成了群，君子小人常在内，长长不离你家门，交下君子厚又厚，交来交去报你恩，交下小人无情义，日后骗你要小心。
		壬	壬水日主，	个性是心胸宽广，机智灵敏，喜欢照顾他人，聪明，有官运，或者驾驭他人，但是为人操劳，冲动易怒。	壬水日主心最实，为人不好占便宜，吃了他人一杯酒，不到明天就还席，你待人家千般好，人家待你总是虚，今后有事酌量办，深谋远虑总为宜。
		癸	癸水日主，	个性是逞强顽固，是努力家，有耐力，热情大方，聪颖机灵，但心地狭窄，容易小气。	癸水日主心最公，别人有苦你伤情，你有好心总是虚，恩人无义未有功，实心待人亏待已，竹蓝打水一场空，今后处人要仔细，不由粗心切记清。
	*/
	var mXinge = map[string][]string{
		"甲": {
			"甲木日主，",
			"个性是有积极性的开拓精神，精力旺盛，重感情，刚直豪迈，勤勉随和，但好自我显示，直爽当中隐蔽着一点点的虚伪。",
			"甲木日主最聪明，深谋远虑是英雄，五湖四海交朋友，仗义疏财有声名，为友能舍黄膘马，两胁插刀不嫌疼，别人若有为难事，舍已为人也现成。",
		},
		"乙": {
			"乙木日主，",
			"个性沉着是警戒心强，表面现象不愿与人争执，温柔含蓄，但感情脆弱。随风转舵，看人说话。",
			"乙木日主最耿直，心实见景有心机，今日吃他一杯酒，不到明天就还席。为人办事多公道，从来不会占便宜，通情达理多尊让，能忍能让心最慈。",
		},
		"丙": {
			"丙火日主，",
			"个性热情豪爽，自信好胜，善言健谈，欠缺沉着，但情绪不稳，冲动易怒，缺乏仔细、认真。",
			"丙火日主心最刚，心中有话不隐藏，有一句来说一句，心实不怕把人伤，本公办事多公道，通情达理好商量，别人有点为难事，千方百计把忙帮。",
		},
		"丁": {
			"丁火日主，",
			"个性是敦厚朴实，重信守义，但任性逞强，防止受骗，有时缺乏耐力，办事易半途而废。专劲不够。",
			"丁火日主心性急，心中有话当面提，脾气来了赛猛虎，性情发了了不的，心直口快无好处，怕虑灾星在后期，为人在先多快乐，暴打不平属第一。",
		},
		"戊": {
			"戊土日主，",
			"个性是有积极性，干一行易于对事物热衷，但性急，任性而逞强。好胜，欲望高，不满足感强烈，个别时候小气。",
			"戊土日主无转回，一条路上跑到黑，有人不投你的意，话不投机皱双眉，有人若随你心意，吃亏让人百事宜，千日交来万日好，一日无情如扫灰。",
		},
		"己": {
			"已土日主，",
			"个性和缓，谨慎，心灵手巧，但疑虑多，欠果断。容易因情耗财，防止被人牵着走。",
			"已土日主多主才，放出账去要不来，在家发下天大恨，见面生情抹不开，说上几句顺情话，心慈面软你回来，任何自己过不去，好心慈善闷在怀。",
		},
		"庚": {
			"庚金日主，",
			"个性是积极进取，勇敢果决，自尊心强，有才能，善于结交各方面朋友，平衡、稳重，但过于操劳，防止被别人耍花样、弄权术而迷惑受骗受害。",
			"庚金日主性情绵，一生不好讨人嫌。见了人家有灾难，人家落泪你心酸，你待人家一盆火，人家待你冬月寒，时常你就上了当，人家做事你花钱。",
		},
		"辛": {
			"辛金日主，",
			"个性沉着坦直无私，待人接物，认真细致，脚踏实地，但温柔敦厚而寓涵固执，有些自信，劳碌奔波欢快。",
			"辛金日主好为人，交的朋友成了群，君子小人常在内，长长不离你家门，交下君子厚又厚，交来交去报你恩，交下小人无情义，日后骗你要小心。",
		},
		"壬": {
			"壬水日主，",
			"个性是心胸宽广，机智灵敏，喜欢照顾他人，聪明，有官运，或者驾驭他人，但是为人操劳，冲动易怒。",
			"壬水日主心最实，为人不好占便宜，吃了他人一杯酒，不到明天就还席，你待人家千般好，人家待你总是虚，今后有事酌量办，深谋远虑总为宜。",
		},
		"癸": {
			"癸水日主，",
			"个性是逞强顽固，是努力家，有耐力，热情大方，聪颖机灵，但心地狭窄，容易小气。",
			"癸水日主心最公，别人有苦你伤情，你有好心总是虚，恩人无义未有功，实心待人亏待已，竹蓝打水一场空，今后处人要仔细，不由粗心切记清。",
		},
	}

	// 2.命理概述
	paipanBaziMap, err := slf.coronaCli.PaipanBaziMap(ctx, &corona.GetPaipanBaziMapRequest{
		Birthday: realSunTimeStr,
		Gender:   lo.Ternary(req.Gender == "男", 1, 2),
		IsLunar:  false,
		Location: req.Birthplace,
		Name:     req.Name,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.HoroscopeBaziMap")
	}
	paipanDetail, err := slf.coronaCli.PaipanDetail(ctx, &corona.GetPaipanDetailRequest{
		Birthday: realSunTimeStr,
		Gender:   lo.Ternary(req.Gender == "男", 1, 2),
		IsLunar:  false,
		Location: req.Birthplace,
		Name:     req.Name,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.HoroscopeInformation")
	}
	result.Mingli = v1.LuncaiMingli{
		TongYi:          paipanBaziMap.Data.TongYi,
		TongYiPercent:   paipanBaziMap.Data.TongYiPercent,
		YingYang:        paipanBaziMap.Data.YingYang,
		YingYangPercent: paipanBaziMap.Data.YingYangPercent,
		MapTransfer:     &paipanBaziMap.Data.MapTransfer,
		LiuTongNum:      paipanBaziMap.Data.LiuTongNum,
		ZuaiNum:         paipanBaziMap.Data.ZuaiNum,
		Pingfen:         paipanBaziMap.Data.Pingfen,
		Wangshuai:       paipanAll.Riyuan,
		Gejucankao:      paipanDetail.Data.GejuCankao,
		Tiaohou:         paipanDetail.Data.TiaoHouYongShen,
		Wuxing:          strings.SplitN(paipanAll.Xiyongjichou, ",", 5),
	}
	result.Xingge = fmt.Sprintf("%s%s您好！我们先来看下你的八字 %s，你是%s%s又有诗曰%s",
		req.Name,
		lo.Ternary(req.Gender == "男", "先生", "女士"),
		strings.Join(result.Mingzhu.Bazi, "、"),
		mXinge[result.Mingzhu.Tiangan[2]][0],
		mXinge[result.Mingzhu.Tiangan[2]][1],
		mXinge[result.Mingzhu.Tiangan[2]][2],
	)
	// 3.五行喜用看职业：命理信息已包含五行用喜，可根据五行职业枚举自行判断。
	// 4.能量与求财方式
	// 5.发展建议：根据喜用五行从枚举中查询。
	result.Nengliang = paipanBaziMap.Data.ShishenMapPower
	for i, item := range result.Nengliang {
		item.Num = paipanBaziMap.Data.WuxingNum[i]
		item.CangNum = paipanBaziMap.Data.WuxingCanNum[i]
		item.Liliang = wuxingPower[item.Wuxing]
	}
	// 五行旺衰
	/*
		五行	过旺影响	缺乏影响
		木	仁善，追求平等与和谐，但可能固执和高傲，在婚姻中可能因性格不稳定、脾气暴躁而争吵较多。可能导致肝气郁结，情绪暴躁，头痛，高血压，消化不良。	可能缺乏创造力、同情心，容易在逆境中感到压抑或缺乏主动性。木主肝胆，缺木者可能有肝胆功能弱或筋骨方面的问题。可能不擅长长远规划，或难以适应需要灵活变通的工作。
		火	放纵急躁，语言快速，外向热烈，过于急躁可能影响婚姻和人际关系。可能导致心火过旺，心悸，失眠，焦虑，易怒，皮肤问题。	可能表现为冷静甚至冷淡，缺乏热情和动力，不擅长社交。火主心脏和血液循环，可能有心血管系统问题，或体温偏低。可能缺少明确的目标和执行力，难以快速抓住机会。
		土	头脑僵化，内向好静。性格内向可能影响社交活动。可能导致脾胃功能过强，消化系统疾病，情绪沉重，思维迟缓，行动迟缓。	可能表现为不够脚踏实地，容易缺乏安全感或稳定性。土主脾胃，缺土者可能有消化系统问题，或体质虚弱。难以承受压力，容易因情绪波动影响工作效率。
		金	性格刚毅，做事果断，但可能冲动，有勇无谋，不考虑后果,过于强势，可能影响婚姻和人际关系，需要配偶和亲人给予更多支持。身体健康上，可能导致心脏和血压问题，肺功能过强，呼吸系统疾病，情绪压抑。	可能优柔寡断，缺乏决断力和执行力，容易被动或逃避责任。金主肺和呼吸系统，可能有呼吸系统疾病，或皮肤问题。可能在需要果断决策的场合表现得不够强势。
		水	犹豫不决，飘荡贪淫，好说是非，情绪波动可能影响与他人的相处。可能导致肾功能过强，生殖系统疾病，情绪波动大，多疑，易疲劳。	可能缺乏灵活性和智慧，过于固执或情绪干涸。水主肾脏和泌尿系统，可能有肾功能或生殖系统的弱点。在需要创新和灵感的领域可能表现不足，适应变化的能力较弱。
	*/
	var mWuxingWangshuai = map[string][]string{
		"木": {
			"仁善，追求平等与和谐，但可能固执和高傲，在婚姻中可能因性格不稳定、脾气暴躁而争吵较多。可能导致肝气郁结，情绪暴躁，头痛，高血压，消化不良。",
			"可能缺乏创造力、同情心，容易在逆境中感到压抑或缺乏主动性。木主肝胆，缺木者可能有肝胆功能弱或筋骨方面的问题。可能不擅长长远规划，或难以适应需要灵活变通的工作。",
		},
		"火": {
			"放纵急躁，语言快速，外向热烈，过于急躁可能影响婚姻和人际关系。可能导致心火过旺，心悸，失眠，焦虑，易怒，皮肤问题。",
			"可能表现为冷静甚至冷淡，缺乏热情和动力，不擅长社交。火主心脏和血液循环，可能有心血管系统问题，或体温偏低。可能缺少明确的目标和执行力，难以快速抓住机会。",
		},
		"土": {
			"头脑僵化，内向好静。性格内向可能影响社交活动。可能导致脾胃功能过强，消化系统疾病，情绪沉重，思维迟缓，行动迟缓。",
			"可能表现为不够脚踏实地，容易缺乏安全感或稳定性。土主脾胃，缺土者可能有消化系统问题，或体质虚弱。难以承受压力，容易因情绪波动影响工作效率。",
		},
		"金": {
			"性格刚毅，做事果断，但可能冲动，有勇无谋，不考虑后果,过于强势，可能影响婚姻和人际关系，需要配偶和亲人给予更多支持。身体健康上，可能导致心脏和血压问题，肺功能过强，呼吸系统疾病，情绪压抑。",
			"可能优柔寡断，缺乏决断力和执行力，容易被动或逃避责任。金主肺和呼吸系统，可能有呼吸系统疾病，或皮肤问题。可能在需要果断决策的场合表现得不够强势。",
		},
		"水": {
			"犹豫不决，飘荡贪淫，好说是非，情绪波动可能影响与他人的相处。可能导致肾功能过强，生殖系统疾病，情绪波动大，多疑，易疲劳。",
			"可能缺乏灵活性和智慧，过于固执或情绪干涸。水主肾脏和泌尿系统，可能有肾功能或生殖系统的弱点。在需要创新和灵感的领域可能表现不足，适应变化的能力较弱。",
		},
	}

	result.WuxingTiaohe = &v1.MingliBaziWuxingTiaohe{
		Pingheng: func() []string {
			var res []string
			res = append(res, "五行平衡，指的是一个人的生辰八字中所包含的五行（金、木、水、火、土）相对和谐，能够相互生扶、制约，达到一种动态的平衡状态。这并不意味着五行各自的数量必须完全相等，而是根据个人八字的具体组合，以及五行之间的相生相克关系来判断。比如上图中的党异中的同党和异党的数值，如果相对差不多，就表示五行相对平衡。")
			res = append(res, fmt.Sprintf("%s%s您的八字，%s",
				req.Name,
				lo.Ternary(req.Gender == "男", "先生", "女士"),
				lo.Ternary(paipanBaziMap.Data.TongYiPercent[0] >= 45 && paipanBaziMap.Data.TongYiPercent[0] <= 55,
					"五行相对平衡，意味着您的运势平稳、顺利，往往性格温和、稳重，善于处理人际关系，能够维持和谐的生活氛围；概率会导致您缺乏明确的目标和动力。缺乏强烈的进取心和决断力，容易随波逐流，缺乏主动追求事业和成功的动力。此外，五行过于平衡还可能使命主在面对机遇和挑战时犹豫不决，难以迅速做出决策。这种犹豫不决的性格特点可能会错失良机，影响个人的发展和成就。",
					"五行失衡，意味着您与外界环境难以达到一个相对的平衡稳定的情况，可能导致性格缺陷，健康受损，事业波折。然而，发现自己五行不平衡，不必过于担心，一方面，简单地通过饮食、色彩、方位、布局、结交贵人等多种方法进行改善，也可以咨询专业人员结合您的八字情况，针对性的制定专属调和方案；另一方面，福兮祸所依，祸兮福所伏，一些波动挑战，蕴含着风险的时候，也是机遇。"),
			))
			return res
		}(),
		Wangshuai: func() []string {
			var res []string
			for wuxing, power := range wuxingPower {
				if power <= 40 {
					res = append(res, fmt.Sprintf("缺%s，能量衰弱，%s", wuxing, mWuxingWangshuai[wuxing][1]))
				} else if power >= 150 {
					res = append(res, fmt.Sprintf("%s过旺，能量过强，%s", wuxing, mWuxingWangshuai[wuxing][0]))
				}
			}
			if len(res) == 0 {
				res = append(res, "五行齐全，旺衰适宜,身体健康。")
			}
			return res
		}(),
	}

	// 更多职业建议
	var (
		shishenSet        = make(map[string]bool) // 命局
		shishenNum        = make(map[string]int)  // 命局
		ZhuXingShishenSet = make(map[string]bool) // 命局
		ZhuXingShishenNum = make(map[string]int)  // 命局
		fuxingShishenSet  = make(map[string]bool) // 命局
		fuxingShishenNum  = make(map[string]int)  // 命局
		shenshaSet        = make(map[string]bool) // 神煞
	)
	for i := 0; i < 4; i++ { // 命局：年、月、日、时
		for _, list := range paipanJiuzhu.ShenShaJiShen {
			for _, s := range list {
				shenshaSet[s] = true
			}
		}
		shishenSet[paipanJiuzhu.BenqiShiShen[i]] = true
		shishenNum[paipanJiuzhu.BenqiShiShen[i]]++
		ZhuXingShishenSet[paipanJiuzhu.BenqiShiShen[i]] = true
		ZhuXingShishenNum[paipanJiuzhu.BenqiShiShen[i]]++
		shishenSet[paipanJiuzhu.ZhongqiShiShen[i]] = true
		for _, name := range paipanJiuzhu.BenqiShiShen {
			shishenSet[name] = true
			shishenNum[name]++
			fuxingShishenSet[name] = true
			fuxingShishenNum[name]++
		}
		shishenSet[paipanJiuzhu.ZhongqiShiShen[i]] = true
		shishenNum[paipanJiuzhu.ZhongqiShiShen[i]]++
		fuxingShishenSet[paipanJiuzhu.ZhongqiShiShen[i]] = true
		fuxingShishenNum[paipanJiuzhu.ZhongqiShiShen[i]]++
		shishenSet[paipanJiuzhu.YuqiShiShen[i]] = true
		shishenNum[paipanJiuzhu.YuqiShiShen[i]]++
		fuxingShishenSet[paipanJiuzhu.YuqiShiShen[i]] = true
		fuxingShishenNum[paipanJiuzhu.YuqiShiShen[i]]++
	}
	//- 食伤泄秀
	riyuan := paipanAll.Riyuan // 日元
	xyjcx := strings.SplitN(paipanAll.Xiyongjichou, ",", 5)
	wuxingYong := xyjcx[0]                 // 五行用
	wuxingXi := xyjcx[1]                   // 五行喜
	mzRiGanName := paipanJiuzhu.Tiangan[2] // 日干
	mzRiGan, err := slf.luncaiRepo.GetTiangan(ctx, mzRiGanName)
	if err != nil {
		return nil, err
	}
	mzYueZhiName := paipanJiuzhu.Dizhi[1] // 月支
	mzYueZhiShishen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, mzRiGanName, mzYueZhiName)
	if err != nil {
		return nil, err
	}
	if (riyuan == "偏强" || riyuan == "身强" || riyuan == "从弱") || (riyuan == "平和" && ((mzYueZhiShishen.Shishen == "正印" || mzYueZhiShishen.Shishen == "偏印") || (mzYueZhiShishen.Shishen == "比肩" || mzYueZhiShishen.Shishen == "劫财"))) {
		xs1, err := slf.luncaiRepo.GetWuxingXiangsheng(ctx, mzRiGan.Wuxing, wuxingXi)
		if err != nil {
			return nil, err
		}
		xs2, err := slf.luncaiRepo.GetWuxingXiangsheng(ctx, mzRiGan.Wuxing, wuxingYong)
		if err != nil {
			return nil, err
		}
		if xs1 != nil || xs2 != nil {
			if !shishenSet["正官"] && !shishenSet["七杀"] && (shishenSet["比肩"] || shishenSet["劫财"]) {
				result.CareerSuggestion = append(result.CareerSuggestion, "您的命理存在食神泄秀格局，宜从事文学、书画、文教、艺术等职业，如文学家、影星、歌星、舞蹈家、作曲家以及美术家等。")
			}
		}
	}
	//- 杀印相生
	if (riyuan == "偏弱" || riyuan == "身弱" || riyuan == "从强") || (riyuan == "平和" && !((mzYueZhiShishen.Shishen == "正印" || mzYueZhiShishen.Shishen == "偏印") || (mzYueZhiShishen.Shishen == "比肩" || mzYueZhiShishen.Shishen == "劫财"))) {
		if shishenSet["七杀"] && shishenSet["正印"] {
			var (
				qishaPower, zhengyinPower float64
			)
			for _, item := range paipanBaziMap.Data.ShishenMapPower {
				for i, shishen := range item.ShiShenArr {
					if shishen == "七杀" {
						qishaPower = item.PowerBfbArr[i]
					}
					if shishen == "正印" {
						zhengyinPower = item.PowerBfbArr[i]
					}
				}
			}
			if qishaPower <= 3.2*zhengyinPower {
				result.CareerSuggestion = append(result.CareerSuggestion, "您的命理存在杀印相生格局，宜从事公务员、事业单位、军事、公检法、律师、情报人员，或外科医师，或企业之高管、或创业者。")
			}
		}
	}
	//- 官印双清
	if shishenSet["正官"] && shishenSet["正印"] {
		result.CareerSuggestion = append(result.CareerSuggestion, "您的命理中官印双清，表明品行端正、对工作认真负责、按部就班、按照社会认可的准则去做，宜学习政治，法律。")
	}
	//- 财官相辅
	if (shishenSet["正官"] || shishenSet["七杀"]) && (shishenSet["正财"] || shishenSet["偏财"]) {
		result.CareerSuggestion = append(result.CareerSuggestion, "您的命理中财官相辅， 建议提升您的文化素养和艺术修养，从事政治、法律、财政经济、管理等领域的工作。")
	}
	//- 食伤生财
	if (riyuan == "偏强" || riyuan == "身强" || riyuan == "从弱") || (riyuan == "平和" && ((mzYueZhiShishen.Shishen == "正印" || mzYueZhiShishen.Shishen == "偏印") || (mzYueZhiShishen.Shishen == "比肩" || mzYueZhiShishen.Shishen == "劫财"))) {
		if (shishenSet["食神"] || shishenSet["伤官"]) && (shishenSet["正财"] || shishenSet["偏财"]) {
			result.CareerSuggestion = append(result.CareerSuggestion, "您的命理存在食伤生财，适合从事与金融、投资、财务等相关的职业；同时也可从事需要才华、技能和创意的职业，如艺术家、作家、设计师、教师、科研人员等。")
		}
	}
	//- 伤官伤尽
	var (
		zhengguanPower, qishaPower float64
		shangguanPower             float64
	)
	for _, item := range paipanBaziMap.Data.ShishenMapPower {
		for i, s := range item.ShiShenArr {
			if s == "正官" {
				zhengguanPower = item.PowerBfbArr[i]
			}
			if s == "七杀" {
				qishaPower = item.PowerBfbArr[i]
			}
			if s == "伤官" {
				shangguanPower = item.PowerBfbArr[i]
			}
		}
	}
	if shishenNum["伤官"] >= 1 && zhengguanPower+qishaPower == 0 && shangguanPower >= 60 {
		result.CareerSuggestion = append(result.CareerSuggestion, "您的命理存在伤官伤尽的情况，宜武备，如军事，警备等。")
	}
	//- 杀刃相生
	if ZhuXingShishenSet["七杀"] && shenshaSet["羊刃"] {
		result.CareerSuggestion = append(result.CareerSuggestion, "您的命理中杀刃相生，适合从事需要勇气、决断力和竞争力的职业，如军事、武备、警备、外科医生、工程界等。")
	}
	//- 比劫重叠
	if shishenNum["比肩"]+shishenNum["劫财"] >= 4 {
		result.CareerSuggestion = append(result.CareerSuggestion, "建议从事医生，会计，教师等自由职业为宜，不建议为宦或从事工商事业。")
	}
	//- 日柱过弱
	if riyuan == "身弱" || riyuan == "从强" {
		result.CareerSuggestion = append(result.CareerSuggestion, "建议您学习农工百艺，养其一技之长。")
	}
	//- 正官五行
	wx, err := slf.luncaiRepo.GetWuxingXiangkeByXiangke(ctx, mzRiGan.Wuxing)
	if err != nil {
		return nil, err
	}
	switch wx.Wuxing {
	case "水":
		result.CareerSuggestion = append(result.CareerSuggestion, "您的个性随和，同时也很有理性，有智谋，虽然适合在工商界发展，不过，如果能作出有关知性方面的工作的话，将会发挥您的个性与意愿。")
	case "木":
		result.CareerSuggestion = append(result.CareerSuggestion, "您的人格廉直而仁慈，同时也懂得控制自己，而和社会，团体取得协调，适合作行政，司法，总务等管理工作。")
	case "火":
		result.CareerSuggestion = append(result.CareerSuggestion, "您的个性很强，因此，常会路见不平拔刀相助，有威严，适合担任竞争性相对较少的文化，艺术，教育工作。")
	case "土":
		result.CareerSuggestion = append(result.CareerSuggestion, "您的个性温和，同时又是正直的人，在各方面表现都很方正和宽容,虽然适合多方面的工作，不过比较适合农林或土木制造方面的工作。")
	case "金":
		result.CareerSuggestion = append(result.CareerSuggestion, "您处理事情果断，义气，同时经济概念也很正确，适合作财政，经济，金融，军队警察的工作。")
	}

	// 财富所属
	var (
		mzTianganZhengcaiNum, mzTianganPiancaiNum int
		mzDizhiZhengcaiNum, mzDizhiPiancaiNum     int
	)
	{
		tbl := &v1.LuncaiBelongTable{}
		for i := 0; i < 4; i++ {
			switch i {
			case 0:
				tbl.Gongwei = append(tbl.Gongwei, []string{"祖辈宫"})
				tbl.Sizhu = append(tbl.Sizhu, "年柱")
				tbl.LifeStage = append(tbl.LifeStage, "少年")
				tbl.Nianling = append(tbl.Nianling, "1-18岁")
			case 1:
				tbl.Gongwei = append(tbl.Gongwei, []string{"事业宫", "父母宫", "兄弟宫"})
				tbl.Sizhu = append(tbl.Sizhu, "月柱")
				tbl.LifeStage = append(tbl.LifeStage, "青年")
				tbl.Nianling = append(tbl.Nianling, "19-36岁")
			case 2:
				tbl.Gongwei = append(tbl.Gongwei, []string{"夫妻宫"})
				tbl.Sizhu = append(tbl.Sizhu, "日柱")
				tbl.LifeStage = append(tbl.LifeStage, "中年")
				tbl.Nianling = append(tbl.Nianling, "37-54岁")
			case 3:
				tbl.Gongwei = append(tbl.Gongwei, []string{"子女宫"})
				tbl.Sizhu = append(tbl.Sizhu, "时柱")

				tbl.LifeStage = append(tbl.LifeStage, "晚年")
				tbl.Nianling = append(tbl.Nianling, "55岁及以上")
			}
			tbl.Tiangan = append(tbl.Tiangan, paipanJiuzhu.Tiangan[i])
			tbl.Dizhi = append(tbl.Dizhi, paipanJiuzhu.Dizhi[i])
			// 天干财位计算：
			tgShishen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, paipanJiuzhu.Tiangan[2], paipanJiuzhu.Tiangan[i])
			if err != nil {
				return nil, err
			}
			tbl.TianganShishen = append(tbl.TianganShishen, tgShishen.Shishen)
			if (tgShishen.Shishen == "正财" || tgShishen.Shishen == "偏财") && (i != 2) { // 日柱天干不参与计算
				tbl.TianganCaiwei = append(tbl.TianganCaiwei, tgShishen.Shishen)
			} else {
				tbl.TianganCaiwei = append(tbl.TianganCaiwei, "")
			}
			// 地支财位计算：
			dzShishen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, paipanJiuzhu.Tiangan[2], paipanJiuzhu.Dizhi[i])
			if err != nil {
				return nil, err
			}
			tbl.DizhiShishen = append(tbl.DizhiShishen, dzShishen.Shishen)
			if paipanJiuzhu.Benqi[i] != "" {
				dzBenqiShiShen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, paipanJiuzhu.Tiangan[2], paipanJiuzhu.Benqi[i])
				if err != nil {
					return nil, err
				}
				tbl.BenqiShishen = append(tbl.BenqiShishen, dzBenqiShiShen.Shishen)
			} else {
				tbl.BenqiShishen = append(tbl.BenqiShishen, "")
			}
			if paipanJiuzhu.Zhongqi[i] != "" {
				dzZhongqiShiShen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, paipanJiuzhu.Tiangan[2], paipanJiuzhu.Zhongqi[i])
				if err != nil {
					return nil, err
				}
				tbl.ZhongqiShishen = append(tbl.ZhongqiShishen, dzZhongqiShiShen.Shishen)
			} else {
				tbl.ZhongqiShishen = append(tbl.ZhongqiShishen, "")
			}
			if paipanJiuzhu.Yuqi[i] != "" {
				dzYuqiShiShen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, paipanJiuzhu.Tiangan[2], paipanJiuzhu.Yuqi[i])
				if err != nil {
					return nil, err
				}
				tbl.YuqiShishen = append(tbl.YuqiShishen, dzYuqiShiShen.Shishen)
			} else {
				tbl.YuqiShishen = append(tbl.YuqiShishen, "")
			}
			if dzShishen.Shishen == "正财" || dzShishen.Shishen == "偏财" {
				tbl.DizhiCaiwei = append(tbl.DizhiCaiwei, dzShishen.Shishen)
			} else {
				tbl.DizhiCaiwei = append(tbl.DizhiCaiwei, "")
			}
		}
		dtl := &v1.LuncaiBelongDetail{
			Cangcai: &v1.LuncaiBelongDetailCangcai{
				TianganCaiNum: func() int {
					var num int
					for i, s := range tbl.TianganShishen {
						if (s == "正财" || s == "偏财") && (i != 2) {
							num++
						}
						if s == "正财" {
							mzTianganZhengcaiNum++
						}
						if s == "偏财" {
							mzTianganPiancaiNum++
						}
					}
					return num
				}(),
				DizhiCaiNum: func() int {
					var num int
					for i := 0; i < 4; i++ {
						if tbl.BenqiShishen[i] == "正财" || tbl.BenqiShishen[i] == "偏财" {
							num++
						}
						if tbl.BenqiShishen[i] == "正财" {
							mzDizhiZhengcaiNum++
						}
						if tbl.BenqiShishen[i] == "偏财" {
							mzDizhiPiancaiNum++
						}
						if tbl.ZhongqiShishen[i] == "正财" || tbl.ZhongqiShishen[i] == "偏财" {
							num++
						}
						if tbl.ZhongqiShishen[i] == "正财" {
							mzDizhiZhengcaiNum++
						}
						if tbl.ZhongqiShishen[i] == "偏财" {
							mzDizhiPiancaiNum++
						}
						if tbl.YuqiShishen[i] == "正财" || tbl.YuqiShishen[i] == "偏财" {
							num++
						}
						if tbl.YuqiShishen[i] == "正财" {
							mzDizhiZhengcaiNum++
						}
						if tbl.YuqiShishen[i] == "偏财" {
							mzDizhiPiancaiNum++
						}
					}
					return num
				}(),
				Deling: func() bool {
					yzShishen := tbl.DizhiShishen[1]
					return yzShishen == "正印" || yzShishen == "偏印" || yzShishen == "比肩" || yzShishen == "劫财"
				}(),
				Shiling: func() bool {
					yzShishen := tbl.DizhiShishen[1]
					return yzShishen != "正印" && yzShishen != "偏印" && yzShishen != "比肩" && yzShishen != "劫财"
				}(),
				AncangRuku: func() bool {
					for i := 0; i < 4; i++ {
						if (paipanJiuzhu.Tiangan[i] == "辰" || paipanJiuzhu.Tiangan[i] == "戌" || paipanJiuzhu.Tiangan[i] == "丑" || paipanJiuzhu.Tiangan[i] == "未") ||
							(paipanJiuzhu.Dizhi[i] == "辰" || paipanJiuzhu.Dizhi[i] == "戌" || paipanJiuzhu.Dizhi[i] == "丑" || paipanJiuzhu.Dizhi[i] == "未") {
							if (tbl.DizhiShishen[i] == "正财" || tbl.DizhiShishen[i] == "偏财") ||
								(tbl.BenqiShishen[i] == "正财" || tbl.BenqiShishen[i] == "偏财") ||
								(tbl.ZhongqiShishen[i] == "正财" || tbl.ZhongqiShishen[i] == "偏财") ||
								(tbl.YuqiShishen[i] == "正财" || tbl.YuqiShishen[i] == "偏财") {
								return true
							}
						}
					}
					return false
				}(),
			},
			CaixingXiji: func() string {
				splitN := strings.SplitN(paipanAll.Xiyongjichoushishen, ",", 5)
				if (splitN[0] == "财星" || splitN[1] == "财星") ||
					((splitN[4] == "财星") && (riyuan == "身强" || riyuan == "从弱")) {
					return "财星为喜用神，大利求财，公私可求，从事商业经营可得财。"
				}
				if (splitN[2] == "财星" || splitN[3] == "财星") ||
					((splitN[4] == "财星") && (riyuan == "身弱" || riyuan == "从强")) {
					return "财星为仇忌神，求财不易，须多费苦心经营。"
				}
				return ""
			}(),
			Opportunity: func() string {
				var (
					ssg   = corona.NewShishenGetter(paipanAll.GetShishenBaziTupleNewTgList[:4], paipanAll.TianganBenqiSsListLiuAll[:4], paipanAll.TianganZhongqiSsListLiuAll[:4], paipanAll.TianganYuqiSsListLiuAll[:4])
					ids   []int
					tgArr []string
				)
				for i := 0; i < 4; i++ {
					if i != 2 && array.Has([]string{"正财", "偏财"}, ssg.Tg[i]) {
						ids = append(ids, i)
						switch i {
						case 0:
							tgArr = append(tgArr, "年干")
						case 1:
							tgArr = append(tgArr, "月干")
						case 2:
							tgArr = append(tgArr, "日干")
						case 3:
							tgArr = append(tgArr, "时干")
						}
					}
					if array.Has([]string{"正财", "偏财"}, ssg.Bq[i]) {
						ids = append(ids, i)
						switch i {
						case 0:
							tgArr = append(tgArr, "年支本气藏干")
						case 1:
							tgArr = append(tgArr, "月支本气藏干")
						case 2:
							tgArr = append(tgArr, "日支本气藏干")
						case 3:
							tgArr = append(tgArr, "时支本气藏干")
						}
					}
					if array.Has([]string{"正财", "偏财"}, ssg.Zq[i]) {
						ids = append(ids, i)
						switch i {
						case 0:
							tgArr = append(tgArr, "年支中气藏干")
						case 1:
							tgArr = append(tgArr, "月支中气藏干")
						case 2:
							tgArr = append(tgArr, "日支中气藏干")
						case 3:
							tgArr = append(tgArr, "时支中气藏干")
						}
					}
					if array.Has([]string{"正财", "偏财"}, ssg.Yq[i]) {
						ids = append(ids, i)
						switch i {
						case 0:
							tgArr = append(tgArr, "年支余气藏干")
						case 1:
							tgArr = append(tgArr, "月支余气藏干")
						case 2:
							tgArr = append(tgArr, "日支余气藏干")
						case 3:
							tgArr = append(tgArr, "时支余气藏干")
						}
					}
				}
				ids = array.Unique(ids)
				if len(ids) == 0 {
					return "四柱无财星"
				}
				var (
					detail = fmt.Sprintf("财星位于%s", strings.Join(tgArr, "、"))
				)
				if array.Has(ids, 0) { // 年柱有财才
					if array.Has(ids, 1, 2) { // 月柱、日柱都有财才
						detail += "，代表您的财富来源于祖辈或国家体制内，或在您的青中年时期拼搏收获"
					} else if array.Has(ids, 1) { // 月柱有财才
						detail += "，代表您的财富来源于祖辈或国家体制内，或在您的青年时期拼搏收获"
					} else if array.Has(ids, 2) { // 日柱有财才
						detail += "，代表您的财富来源于祖辈或国家体制内，或在您的青年时期拼搏收获"
					} else {
						detail += "，代表您的财富来源于祖辈或国家体制内"
					}
				} else {
					if array.Has(ids, 1, 2) { // 月柱、日柱都有财才
						detail += "，代表您的财富或在您的青中年时期拼搏收获"
					} else if array.Has(ids, 1) { // 月柱有财才
						detail += "，代表您的财富或在您的青年时期拼搏收获"
					} else if array.Has(ids, 2) { // 日柱有财才
						detail += "，代表您的财富或在您的青年时期拼搏收获"
					}
				}
				if array.Has(ids, 3) {
					detail += "，晚年享有财富"
				}
				detail += "。"
				return detail
			}(),
		}
		result.Belong = &v1.LuncaiBelong{
			Table:  tbl,
			Detail: dtl,
		}
	}
	// 事业宫
	{
		sanhe, err := slf.luncaiRepo.GetDizhiSanheByOne(ctx, mzYueZhiName)
		if err != nil {
			return nil, err
		}
		var sanheDizhi1, sanheDizhi2 string
		if sanhe.Dizhi1 == mzYueZhiName {
			sanheDizhi1 = sanhe.Dizhi2
			sanheDizhi2 = sanhe.Dizhi3
		} else if sanhe.Dizhi2 == mzYueZhiName {
			sanheDizhi1 = sanhe.Dizhi1
			sanheDizhi2 = sanhe.Dizhi3
		} else {
			sanheDizhi1 = sanhe.Dizhi1
			sanheDizhi2 = sanhe.Dizhi2
		}
		liuhe, err := slf.luncaiRepo.GetDizhiLiuheByOne(ctx, mzYueZhiName)
		if err != nil {
			return nil, err
		}
		var liuheDizhi string
		if liuhe.Dizhi1 == mzYueZhiName {
			liuheDizhi = liuhe.Dizhi2
		} else {
			liuheDizhi = liuhe.Dizhi1
		}
		mzYuezhi, err := slf.luncaiRepo.GetDizhi(ctx, mzYueZhiName)
		if err != nil {
			return nil, err
		}
		result.CareerPalace = &v1.LuncaiCareerPalace{
			Middle:  mzYueZhiName,
			Left:    sanheDizhi1,
			Right:   sanheDizhi2,
			Down:    liuheDizhi,
			Shishen: mzYueZhiShishen.Shishen,
			Yueling: v1.LuncaiCareerPalaceYueling{
				Xiyong: mzYuezhi.Dizhi + mzYuezhi.Wuxing,
				Dizhi: []string{
					sanheDizhi1, sanheDizhi2, liuheDizhi,
				},
			},
		}
	}
	// 财源与求财意向
	{
		totalZhengzaiNum, totalPiancaiNum := mzTianganZhengcaiNum+mzDizhiZhengcaiNum, mzTianganPiancaiNum+mzDizhiPiancaiNum
		// 财源
		if totalZhengzaiNum > 0 && totalPiancaiNum > 0 {
			result.Caiyuan = append(result.Caiyuan, "您的财富来源多样，除稳定的收入外，有副业收入的机会，偶尔会有意外之财；建议您把握好机会，同时提升自己能力与修养，维系好关键关系，加强个人财富获取的核心竞争力。")
		} else if totalZhengzaiNum > 0 && totalPiancaiNum == 0 {
			result.Caiyuan = append(result.Caiyuan, "您的财富来源多样性一般，主要是稳定的收入，很少获得意外之财。")
		} else if totalZhengzaiNum == 0 && totalPiancaiNum > 0 {
			result.Caiyuan = append(result.Caiyuan, "您的财富来源多样性一般，财富来源较为不稳定，易获得意外之财。")
		} else {
			result.Caiyuan = append(result.Caiyuan, "您的财富情况较为特殊，需要结合其他方法推算财源的多样性。如需详细了解，建议咨询资深专业人员。")
		}
		var percent float64
		for _, item := range result.Nengliang {
			if item.ShiShenArr[0] == "正财" || item.ShiShenArr[0] == "偏财" {
				percent = item.PowerBfbArr[0] + item.PowerBfbArr[1]
				break
			}
		}
		if percent < 0.05 {
			result.Caiyuan = append(result.Caiyuan, fmt.Sprintf("您的财星能量占比为%.2f%%，求财意向不强。", percent))
		} else if percent >= 0.05 && percent <= 0.1 {
			result.Caiyuan = append(result.Caiyuan, fmt.Sprintf("您的财星能量占比为%.2f%%，求财意向一般。", percent))
		} else {
			result.Caiyuan = append(result.Caiyuan, fmt.Sprintf("您的财星能量占比为%.2f%%，求财意向较强。", percent))
		}
	}
	// 风险与偏好
	tbl := result.Belong.Table
	result.Mingli.Shishen = [][]string{
		{tbl.TianganShishen[0], tbl.DizhiShishen[0]},
		{tbl.TianganShishen[1], tbl.DizhiShishen[1]},
		{"日主", tbl.DizhiShishen[2]},
		{tbl.TianganShishen[3], tbl.DizhiShishen[3]},
	}
	{
		result.Risk = &v1.LuncaiRisk{}
		result.Risk.Shishen = result.Mingli.Shishen
		numSet := make(map[string]int)
		for _, arr := range result.Risk.Shishen {
			for _, s := range arr {
				if s != "日主" {
					numSet[s]++
				}
			}
		}
		redNum := numSet["七杀"] + numSet["伤官"] + numSet["劫财"] + numSet["偏财"]
		blackNum := numSet["正官"] + numSet["正印"] + numSet["食神"] + numSet["正财"]
		//grayNum := set["比肩"] + set["偏印"]
		if redNum == 0 {
			result.Risk.Detail = append(result.Risk.Detail, "谨慎型：不喜欢冒险，在保本的前提下获取收益，适合稳定职业，理财上选择国债、定期大额存单、货币基金等理财产品。")
		} else if blackNum > redNum {
			result.Risk.Detail = append(result.Risk.Detail, "稳健型：较害怕风险，但是又希望保本的基础上有一定的收益，适合谋取稳定职业，如，或者买债券、货币基金、存款类理财产品、银行中短期理财产品等。")
		} else if blackNum == redNum {
			result.Risk.Detail = append(result.Risk.Detail, "平衡型：能接受一定的风险，会综合考虑风险和收益，适合谋取相对稳定职业，或者尝试货币基金、基金投资、股票、外汇、银行固定预期收益理财等组合方式投资。")
		} else if redNum > blackNum {
			result.Risk.Detail = append(result.Risk.Detail, "激进型：倾向于有风险高收益的理财投资，适合创业，理财偏向于股票型基金、私募基金等投资方式。")
		}
		powerSet := make(map[string]float64)
		for _, item := range result.Nengliang {
			for j, s := range item.ShiShenArr {
				powerSet[s] += item.PowerBfbArr[j]
			}
		}
		redPower := powerSet["七杀"] + powerSet["伤官"] + powerSet["劫财"] + powerSet["偏财"]
		blackPower := powerSet["正官"] + powerSet["正印"] + powerSet["食神"] + powerSet["正财"]
		if redPower == blackPower {
			result.Risk.Detail = append(result.Risk.Detail, "您的动星与静星能量较为平衡，总体来说既具有，缜密稳重、和顺的一面，表现出对体面工作的追求和稳定性格的偏好；同时又具备生性好动、反应灵敏、积极进取的特质，不安于现状，敢于冒险。具体会受流年大运的影响，即在不同时间，会有不同的偏向。")
		} else if redPower > blackPower {
			result.Risk.Detail = append(result.Risk.Detail, "您的动星较多，总体来说，偏于生性好动，反应灵敏，偏于积极进取，不安于现状，热情、激情喜形于色，爱僧分明，有一种挑战心理，敢于冒险，比较开放，灵活机动，具有开拓精神。不论什么工作、体面与否，只要能赚钱都可以干，追求物质上的享受。一生多从事流动性的，具有主动性大的行业。需要时，能主动求人；被人求他也痛快。社会活动能力强，有社交能力。在企业人事安排上，开拓市场就用动星多的人，动有鲁莽性，醋也，动有武，不拘小节，偏于外向。")
		} else {
			result.Risk.Detail = append(result.Risk.Detail, "您的静星较多，总体来说，缜密稳重，工作事业在乎体面，不会上街叫卖。好吃素，性格和顺，一生工作稳定性较强，不喜变化，有从事公职之像，如从事私营往往也是体面性，稳定性的工作，比较安稳，流动性不强。宁肯少挣钱，也不干多挣钱不体面的工作，不善于搞生意，善于做有益于社会的事，正星的人忧患意识强。在企业人事安排上，搞管理就用静星多的人，心细好静。")
		}
	}
	// 担财
	{

		var (
			ability, recommendation string
			isNormalGeju            = func() bool {
				return map[string]bool{
					"正官格":                 true,
					"七杀格":                 true,
					"正印格":                 true,
					"偏印（枭神）格":           true,
					"偏印格":                 true,
					"枭神格":                 true,
					"食神格":                 true,
					"伤官格":                 true,
					"偏财格":                 true,
					"正财格":                 true,
					"比肩格(建禄格)":         true,
					"比肩格":                 true,
					"建禄格":                 true,
					"劫财格(羊刃格或阳刃格)": true,
					"劫财格":                 true,
					"羊刃格":                 true,
					"阳刃格":                 true,
				}[paipanAll.Geju]
			}()
		)
		if (riyuan == "身弱" || riyuan == "偏弱") && isNormalGeju {
			ability = "较弱"
			recommendation = "建议您在实现事业与财富追求的过程中，注重健康与心态，提升个人能力与气场，合理把握身边的平台、人脉等资源；合理分配已获取的财务，如配偶子女等，同时可考虑存款转化为产业、房产或者无形的资产;在面对机会时，可以通过听取多方建议，提升出击的把握和信心。"
		} else if riyuan == "身强" || riyuan == "偏强" {
			ability = "较强"
			recommendation = "建议您在实现事业与财富追求的过程中，加强个人心态建设及能力提升，合理把握可用资源；面对机遇时，综合考虑、当机立断地迅抓住机遇，让事业更上一层楼。此外也要克服自大、武断等心理，避免因人际因素而受到损失。"
		} else if riyuan == "从强" || riyuan == "从弱" {
			ability = "极强"
			recommendation = "建议您在实现事业与财富追求的过程中，加强个人心态建设与能力提升，同时避免因财富造成其他人际关系、健康方面的问题；遇到合适的时机，综合考虑，当机立断地迅抓住机遇，让事业更上一层楼。此外也要避免刚愎自用、武断等心理，避免因人际因素而受到损失。"
		} else if !isNormalGeju {
			ability = "极强"
			recommendation = "建议您在实现事业与财富追求的过程中，加强个人心态建设与能力提升，同时避免因财富造成其他人际关系、健康方面的问题；遇到合适的时机，综合考虑，当机立断地迅抓住机遇，让事业更上一层楼。此外也要避免刚愎自用、武断等心理，避免因人际因素而受到损失。"
		}
		result.Dancai = &v1.LuncaiDancai{
			Dangyi:         paipanBaziMap.Data.TongYi,
			DangyiPercent:  paipanBaziMap.Data.TongYiPercent,
			Wangshuai:      riyuan,
			GejuCankao:     paipanAll.Geju,
			Ability:        ability,
			Recommendation: recommendation,
		}
	}
	// 神煞看财运
	{
		sizhuShenshaSet := make(map[string]bool)
		for _, arr := range paipanJiuzhu.ShenShaJiShen {
			for _, shensha := range arr[0:4] {
				sizhuShenshaSet[shensha] = true
			}
		}
		var (
			mSet   = make(map[string]string)
			hasTCW bool
		)
		for shensha := range sizhuShenshaSet {
			switch shensha {
			case "天乙贵人":
				mSet["您天乙贵人入命，在事业和财运上容易得到贵人的帮助和提携，从而事业顺利，财运亨通。"] = shensha
			case "天德贵人":
				mSet["您天德贵人入命，通常预示着命主在事业和财运上能够因为自身的良好品德和正直为人而获得他人的尊重和信任，从而有助于事业的发展。"] = shensha
			case "月德贵人":
				mSet["您月德贵人入命，意味着命主在事业和财运上容易得到上天的庇佑和好运的眷顾，从而事业有成，财源广进。"] = shensha
			case "福星贵人":
				mSet["您福星贵人入命，预示着命主在事业和财运上能够享受到长久的福气和好运，从而事业顺利，财运亨通。"] = shensha
			case "禄神":
				mSet["您禄神入命，意味着命主在事业和财运上容易得到稳定的收入和财富，同时也能够享受到生活中的各种福气和好运。"] = shensha
			case "太极贵人", "词馆", "文昌贵人":
				if hasTCW {
					continue
				}
				mSet[fmt.Sprintf("您的命理中存在%s，即您在才华和学识方面天赋较佳，易在文学或创作方面获得成就。", shensha)] = shensha
				hasTCW = true
			case "华盖":
				mSet["您的命理中存在华盖，即您聪明、有才华，孤独能耐住寂寞，尤其在玄学宗教方面较有兴趣与天赋。"] = shensha
			case "驿马":
				mSet["您的命理中存在驿马，即您在事业方面奔波与调动较多，可能去远方发展。"] = shensha
			}
		}
		for s := range mSet {
			result.Shensha = append(result.Shensha, s)
		}
	}
	// 流年运势
	{
		dyStart, dyEnd := paipanAll.DayunQishi, paipanAll.DayunJiezhi
		dyLiunian := strings.Split(paipanAll.StrDayunLiuYear, ",")
		paipanLiunian, err := slf.coronaCli.GetDayunLiunianScore(ctx, &corona.GetDayunLiunianScoreRequest{
			Birthtime: realSunTimeStr,
			Gender:    req.Gender,
		})
		if err != nil {
			return nil, errors.Wrap(err, "paipan.GetDayunLiulianScore")
		}
		dyScoreList := func() []int {
			end := len(paipanLiunian.Zscore)
			for end > 0 && paipanLiunian.Zscore[end-1] == 0 {
				end--
			}
			arr := paipanLiunian.Zscore[:end]
			return arr[len(arr)-120:]
		}()
		result.Dayunliunian = &v1.LuncaiDayunliunian{
			StartYear:  dyStart,
			EndYear:    dyEnd,
			ScoreList:  dyScoreList,
			DayunList:  paipanAll.GetShierDayun,
			GanzhiList: dyLiunian,
			YearList: func() []int {
				var arr []int
				for i := dyStart; i <= dyEnd; i++ {
					arr = append(arr, i)
				}
				return arr
			}(),
		}
	}
	// 当前大运
	result.CurrentDayun = &v1.MingliBaziCurrentDayun{}
	{
		year := currentTime.Year()
		inDayun := year >= result.Dayunliunian.StartYear
		result.CurrentDayun.InDayun = inDayun
		var (
			dyGanzhi string
			dyIndex  int
		)
		if inDayun {
			dyGanzhi, dyIndex, err = func(current, start int, dayunList []string) (string, int, error) {
				diff := current - start
				if diff < 0 || current > start+10*len(dayunList)-1 {
					return "", 0, v1.ErrInputYearOutOfRange
				}
				return dayunList[diff/10], diff / 10, nil
			}(year, result.Dayunliunian.StartYear, result.Dayunliunian.DayunList)
			if err != nil {
				return nil, err
			}
		} else {
			// 取第一个
			dyGanzhi = result.Dayunliunian.DayunList[0]
			dyIndex = 0
		}
		dyGan, dyZhi := string([]rune(dyGanzhi)[0]), string([]rune(dyGanzhi)[1])
		dyTianganShishen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, result.Mingzhu.Tiangan[2], dyGan)
		if err != nil {
			return nil, err
		}
		dyDizhiShishen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, result.Mingzhu.Tiangan[2], dyZhi)
		if err != nil {
			return nil, err
		}
		detail := fmt.Sprintf("今年%d年%s%s，是", year, day.Bazi1, day.Shengxiao)
		if year < result.Dayunliunian.StartYear {
			detail += "未交运阶段，今年暂时不受大运影响。"
		} else {
			yearNum := (year - result.Dayunliunian.StartYear) % 10
			detail += fmt.Sprintf("第%s大运%s的第%s阶段，",
				strs.Digit2ZhNumber(strconv.Itoa(dyIndex+1)),
				dyGanzhi,
				lo.Ternary(yearNum < 5, "一", "二"),
			)
			var wuxing string
			if yearNum < 5 {
				wuxing = slf.tianganWuxing[dyGan]
				detail += fmt.Sprintf("%s%s%s", dyGan, wuxing, dyTianganShishen.Shishen)
			} else {
				wuxing = slf.dizhiWuxing[dyZhi]
				detail += fmt.Sprintf("%s%s%s", dyZhi, wuxing, dyDizhiShishen.Shishen)
			}
			detail += "主导大运，"
			for i, s := range result.Mingzhu.Wuxing {
				if s == wuxing {
					switch i {
					case 0:
						detail += "往往能够充分发挥自己的潜能，容易遇到贵人相助，事业蒸蒸日上，身体健康，家庭和睦。"
					case 1:
						detail += "运用神到位，往往能够感受到外界的助力，事业顺利，人际关系和谐，心情愉悦。"
					case 2:
						detail += "大运用神还未到位，往往会感到外界的压力增大，事业受阻，人际关系紧张，情绪波动较大，需要格外小心应对各种挑战，感觉必要时可寻找化解之法。"
					case 3:
						detail += "大运用神还未到位，往往会感到外界的压力增大，事业受阻，人际关系紧张，情绪波动较大，需要保持冷静和耐心，感觉必要时可寻找化解之法。"
					case 4:
						detail += "大运未能较好地给你提供保住，运势相对平稳，但也因流年的变化而产生波动，可以通过调整生活方式、改变行为习惯等方式来减少不利影响，提升自己的运势。"
					default:
					}
					break
				}
			}
		}
		result.CurrentDayun.Dayun = detail
		riganWuxing, yuezhiWuxing := slf.tianganWuxing[result.Mingzhu.Tiangan[2]], slf.dizhiWuxing[result.Mingzhu.Dizhi[1]]
		if func() bool {
			switch result.Mingzhu.Riyuan {
			// 身强、偏强、从弱，及值为平和
			case "身强", "偏强", "从弱", "平和":
				return true
			}
			return false
		}() && (slf.wuxingXiangsheng[yuezhiWuxing] == riganWuxing || riganWuxing == yuezhiWuxing) {
			result.CurrentDayun.Wangshuai = &v1.MingliBaziCurrentDayunWangshuai{
				Tiangan: slf.dyGanzhiShishenWangshuai[dyTianganShishen.Shishen+"-强"][1],
				Dizhi:   slf.dyGanzhiShishenWangshuai[dyDizhiShishen.Shishen+"-强"][1],
			}
		} else if func() bool {
			// 身弱、偏弱、从强，及值为平和
			switch result.Mingzhu.Riyuan {
			case "身弱", "偏弱", "从强", "平和":
				return true
			}
			return false
		}() && (slf.wuxingXiangsheng[yuezhiWuxing] != riganWuxing || riganWuxing != yuezhiWuxing) {
			result.CurrentDayun.Wangshuai = &v1.MingliBaziCurrentDayunWangshuai{
				Tiangan: slf.dyGanzhiShishenWangshuai[dyTianganShishen.Shishen+"-弱"][0],
				Dizhi:   slf.dyGanzhiShishenWangshuai[dyDizhiShishen.Shishen+"-弱"][0],
			}
		}
		if inDayun && (year-result.Dayunliunian.StartYear)%10 == 9 {
			result.CurrentDayun.Jiaoyun = "今年是大运将交未交的最后一年，往往有人生重大之变动，建议谨慎度过。"
		}
		startYear := currentTime.Year()
		if startYear < result.Dayunliunian.StartYear {
			startYear = result.Dayunliunian.StartYear
		}
		startIndex := 0
		for i, y := range result.Dayunliunian.YearList {
			if y == startYear {
				startIndex = i
				break
			}
		}
		bestIndex, WorstIndex := func(arr []int, startIndex int, n int) ([]int, []int) {
			length := len(arr) - startIndex
			if length > 20 {
				length = 20
			}
			if length <= 0 || n <= 0 {
				return nil, nil
			}
			type scoreIndex struct {
				index int
				score int
			}
			scores := make([]scoreIndex, length)
			for i := 0; i < length; i++ {
				scores[i] = scoreIndex{
					index: startIndex + i,
					score: arr[startIndex+i],
				}
			}
			topScores := make([]scoreIndex, length)
			copy(topScores, scores)
			sort.Slice(topScores, func(i, j int) bool {
				return topScores[i].score > topScores[j].score
			})
			sort.Slice(scores, func(i, j int) bool {
				return scores[i].score < scores[j].score
			})
			topIndices := []int{}
			if len(topScores) > 0 {
				currentScore := topScores[0].score
				for i := 0; i < length && len(topIndices) < n; i++ {
					if topScores[i].score == currentScore {
						topIndices = append(topIndices, topScores[i].index)
					} else if len(topIndices) < n {
						currentScore = topScores[i].score
						topIndices = append(topIndices, topScores[i].index)
					}
				}
			}
			bottomIndices := []int{}
			if len(scores) > 0 {
				currentScore := scores[0].score
				for i := 0; i < length && len(bottomIndices) < n; i++ {
					if scores[i].score == currentScore {
						bottomIndices = append(bottomIndices, scores[i].index)
					} else if len(bottomIndices) < n {
						currentScore = scores[i].score
						bottomIndices = append(bottomIndices, scores[i].index)
					}
				}
			}
			return topIndices, bottomIndices
		}(result.Dayunliunian.ScoreList, startIndex, 2)
		result.CurrentDayun.BestYear = func() [][]any {
			var arr [][]any
			for _, i := range bestIndex {
				arr = append(arr, []any{
					result.Dayunliunian.YearList[i],
					result.Dayunliunian.GanzhiList[i],
					result.Dayunliunian.ScoreList[i],
				})
			}
			return arr
		}()
		result.CurrentDayun.WorstYear = func() [][]any {
			var arr [][]any
			for _, i := range WorstIndex {
				arr = append(arr, []any{
					result.Dayunliunian.YearList[i],
					result.Dayunliunian.GanzhiList[i],
					result.Dayunliunian.ScoreList[i],
				})
			}
			return arr
		}()
	}

	{
		str1 := "你的八字其实感情上的运势不算差，还是有些波折，但是因为命局中五行喜用不够强旺，导致泄运比较严重，建议进行补旺调理，对你的格局来说比较关键。"
		str2 := func() string {
			var res string
			switch result.Mingzhu.Riyuan {
			case "从弱", "身弱", "偏弱":
				res = "您的命局中，日元衰弱，容易财大伤身，还是需要学会强身，要不断增强自己的体质和实力，体质不好容易生病，则很难担得起财，身体好了才有精力赚钱，因此改善自身的健康有助于生财。"
			case "偏强", "身强", "从强":
				res = "您的命局中，日元较旺，相对他人，生病较少，但并不是说不会生病。财大伤身，在日常生活和追求财富的过程中，还是需要学会强身，要不断增强自己的体质和实力，体质不好容易生病，则很难担得起财，身体好了才有精力赚钱，因此改善自身的健康有助于生财。"
			case "平和":
				res = "您的命局中，日元不强，得令而不得地得势，或得地得势而不得令，日元不够强旺，身体偶尔会有些小病。财大伤身，在日常生活和追求财富的过程中，还是需要学会强身，要不断增强自己的体质和实力，体质不好容易生病，则很难担得起财，身体好了才有精力赚钱，因此改善自身的健康有助于生财。"
			}
			var arr []string
			for wuxing, power := range wuxingPower {
				if power <= 40 {
					arr = append(arr, fmt.Sprintf("缺%s，能量衰弱，%s。", wuxing, mWuxingWangshuai[wuxing][1]))
				} else if power >= 150 {
					arr = append(arr, fmt.Sprintf("%s过旺，能量过强，%s。", wuxing, mWuxingWangshuai[wuxing][0]))
				}
			}
			if len(arr) > 0 {
				res += fmt.Sprintf("%s%s，您的命盘中，%s",
					req.Name,
					lo.Ternary(req.Gender == "男", "先生", "女士"),
					strings.Join(arr, "，"),
				)
			}
			res += "条件允许时，选择合适的锻炼的方式，如饭后散步、竞走、太极、游泳、骑行，记得定期体检。如感觉不顺时，也可以选择一些适合自己五行属性的配饰。"
			return res
		}()
		str3 := fmt.Sprintf("你的命局%s，%s。另外食伤为财之原神，您的%s。%s",
			func() string {
				var nl int
				for _, item := range result.Nengliang {
					if item.ShiShenArr[0] == "正财" || item.ShiShenArr[0] == "偏财" || item.ShiShenArr[1] == "正财" || item.ShiShenArr[1] == "偏财" {
						nl = item.Liliang
					}
				}
				if nl < 40 {
					return "财星较弱"
				} else if nl >= 40 && nl < 90 {
					return "财星较强"
				}
				return "财星很旺"
			}(),
			func() string {
				var caiWx string
				for _, item := range result.Nengliang {
					if item.ShiShenArr[0] == "正财" || item.ShiShenArr[0] == "偏财" || item.ShiShenArr[1] == "正财" || item.ShiShenArr[1] == "偏财" {
						caiWx = item.Wuxing
					}
				}
				if caiWx == result.Mingzhu.Wuxing[1] ||
					caiWx == result.Mingzhu.Wuxing[0] ||
					(caiWx == result.Mingzhu.Wuxing[4] && (result.Mingzhu.Riyuan == "身弱" || result.Mingzhu.Riyuan == "从强")) {
					return "财星为喜用神，大利求财，公私可求，从事商业经营可得财。"
				} else if caiWx == result.Mingzhu.Wuxing[2] ||
					caiWx == result.Mingzhu.Wuxing[3] ||
					(caiWx == result.Mingzhu.Wuxing[4] && (result.Mingzhu.Riyuan == "身强" || result.Mingzhu.Riyuan == "从弱")) {
					return "财星为仇忌神，求财不易，须多费苦心经营。"
				}
				return ""
			}(),
			func() string {
				var nl int
				for _, item := range result.Nengliang {
					if item.ShiShenArr[0] == "食神" || item.ShiShenArr[0] == "伤官" || item.ShiShenArr[1] == "食神" || item.ShiShenArr[1] == "伤官" {
						nl = item.Liliang
					}
				}
				if nl < 40 {
					return "食伤弱，钱财不变，赚钱门路少，得小财而已。"
				} else if nl >= 40 && nl < 90 {
					return "食伤不旺，财源不广或不好，大财难求。"
				}
				return "食伤健旺，财源宽广且较好，钱财不断。"
			}(),
			"财运提升建议可以补旺财库。这样即便是在差的大运流年也是有机会的到很大改善的，在好的流年更是锦上添花。",
		)
		str4 := func() string {
			if !currentTime.After(birthtimeSun) {
				return "未来之人，暂无大运傍身。"
			}
			if currentTime.Year() < result.Dayunliunian.StartYear {
				return "“您今年暂未上运。"
			}
			if currentTime.Year() > result.Dayunliunian.EndYear {
				return "百岁之后，不再看大运流年运势。"
			}
			return ""
		}()
		result.Advice = []string{
			str1,
			str2,
			str3,
			str4,
		}
	}
	// 创建排盘记录
	if !req.IsReplay && !req.IsExample {
		ret, _ := slf.irs.Query(req.IP)
		if result.ID, err = slf.paipanRecordRepo.CreatePaipanRecord(ctx, &model.PaipanRecord{
			Name:           req.Name,
			Gender:         lo.Ternary(req.Gender == "男", 1, 2),
			Birthtime:      birthtime,
			BirthtimeSun:   birthtimeSun,
			BirthtimeLunar: result.Mingzhu.BirthtimeLunar,
			Bazi:           result.Mingzhu.Bazi,
			Birthplace:     req.Birthplace,
			UserAgent:      req.UserAgent,
			IP:             ret.IP,
			Region:         ret.Region(),
			AppID:          6, // 在线投放
			AppPlatformID:  1, // 未知（默认）
			Scene:          req.Scene,
			Type:           1,
			SaveTime:       time.Now(),
		}); err != nil {
			return nil, err
		}
	}
	//// 根据权限返回数据
	if !isShow {
		result.Nengliang = nil
		result.CareerSuggestion = nil
		result.Belong = nil
		result.CareerPalace = nil
		result.Caiyuan = nil
		result.Risk = nil
		result.Dancai = nil
		result.Shensha = nil
		result.Dayunliunian = nil
	}
	return result, nil
}

func (slf *adflowService) MingliBaziReplay(ctx context.Context, req *v1.MingliBaziReplayRequest) (*v1.MingliBaziReplayResponseData, error) {
	record, err := slf.paipanRecordRepo.FetchPaipanRecordByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if record == nil || record.AppID != 6 {
		return nil, nil
	}
	order, err := slf.orderRepo.FetchOrderByMingliBaziID(ctx, record.ID)
	if err != nil {
		return nil, err
	}
	newReq := &v1.MingliBaziRequest{
		Birthtime:   record.Birthtime.Format("2006-01-02 15:04:05"),
		CurrentTime: req.CurrentTime,
		Gender:      lo.Ternary(record.Gender == 1, "男", "女"),
		Name:        record.Name,
		Birthplace:  record.Birthplace,
		IsReplay:    true,
	}
	if order != nil {
		newReq.IsPaid = true
	}
	return slf.MingliBazi(ctx, newReq)
}

func (slf *adflowService) realSunTime(ctx context.Context, birthtime time.Time, location []string) (time.Time, error) {
	if len(location) == 0 {
		return birthtime, nil
	}
	join := strings.Join(location, "")
	if len(location) == 3 {
		join = "中国" + join
	}
	offset, err := slf.luncaiRepo.GetOffset4TimeByLocation(ctx, join)
	if err != nil {
		return time.Time{}, err
	}
	return birthtime.Add(time.Duration(offset) * time.Minute), nil
}
