package service

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"strings"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/algo"
	"zodiacus/pkg/strs"
	"zodiacus/third_party/corona"
)

type YunshiService interface {
	Yunshi(ctx context.Context, req *v1.YunshiRequest) (*v1.YunshiResponseData, error)
}

func NewYunshiService(
	service *Service,
	yunshiRepo repository.YunshiRepository,
	paipanRecordRepo repository.UserPaipanRecordRepository,
) YunshiService {
	return &yunshiService{
		Service:          service,
		yunshiRepo:       yunshiRepo,
		paipanRecordRepo: paipanRecordRepo,
		tianganMapWuxing4Shishen: map[string][]string{
			"甲": {"水", "木", "火", "土", "金"},
			"乙": {"水", "木", "火", "土", "金"},
			"丙": {"木", "火", "土", "金", "水"},
			"丁": {"木", "火", "土", "金", "水"},
			"戊": {"火", "土", "金", "水", "木"},
			"己": {"火", "土", "金", "水", "木"},
			"庚": {"土", "金", "水", "木", "火"},
			"辛": {"土", "金", "水", "木", "火"},
			"壬": {"金", "水", "木", "火", "土"},
			"癸": {"金", "水", "木", "火", "土"},
		},
	}
}

type yunshiService struct {
	*Service
	yunshiRepo               repository.YunshiRepository
	paipanRecordRepo         repository.UserPaipanRecordRepository
	tianganMapWuxing4Shishen map[string][]string // 日干对应十神的五行，根据五行的顺序找对应索引的能量值。十神顺序为：印枭、比劫、食伤、财才、官杀
}

func (slf *yunshiService) Yunshi(ctx context.Context, req *v1.YunshiRequest) (*v1.YunshiResponseData, error) {
	if req.Birthplace == nil {
		req.Birthplace = []string{}
	}
	birthtime, err := time.Parse("2006-01-02 15:04:05", req.Birthtime)
	if err != nil {
		return nil, errors.Wrap(err, "time.Parse")
	}
	birthtimeSun, err := slf.realSunTime(ctx, birthtime, req.Birthplace)
	if err != nil {
		return nil, err
	}
	realSunTimeStr := birthtimeSun.Format("2006-01-02 15:04:05")
	result := &v1.YunshiResponseData{
		Guaxiang:    nil,
		Score:       &v1.YunshiScore{},
		Suggestions: make([]string, 0),
	}
	// 排盘ALL
	paipanAll, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: realSunTimeStr,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, err
	}
	result.Mingzhu = &v1.YunshiMinzhu{
		Name:           req.Name,
		Birthtime:      req.Birthtime,
		BirthtimeLunar: strs.Digit2ZhUpper(paipanAll.Nongli),
		Birthplace:     req.Birthplace,
		Gender:         req.Gender,
		Bazi:           paipanAll.Sizhu,
		Wuxing:         strings.SplitN(paipanAll.Xiyongjichou, ",", 5),
		Zodiac:         paipanAll.Shengxiao,
	}

	// 论财-卦象
	paipanDayun, err := slf.coronaCli.GetDayun(ctx, &corona.GetDayunRequest{
		Birthtime: req.Birthtime,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, err
	}
	ganzhiName4Dayun, err := func(current, start int, dayunList []string) (string, error) {
		diff := current - start
		if diff < 0 || current > start+10*len(dayunList)-1 {
			return "", v1.ErrInputYearOutOfRange
		}
		return dayunList[diff/10], nil
	}(req.CurrentYear, paipanDayun.DayunQishi, paipanDayun.GetShierDayun)
	if err != nil {
		return nil, err
	}
	shizhuCurrentYear, err := slf.coronaCli.Shizhu(ctx, &corona.GetShizhuRequest{Birthtime: fmt.Sprintf("%d-03-01 00:00:00", req.CurrentYear)})
	if err != nil {
		return nil, err
	}
	ganzhiName4CurrentYear := shizhuCurrentYear.Nianzhu
	ganzhi4Dayun, err := slf.yunshiRepo.GetGanzhiByName(ctx, ganzhiName4Dayun)
	if err != nil {
		return nil, err
	}
	ganzhi4CurrentYear, err := slf.yunshiRepo.GetGanzhiByName(ctx, ganzhiName4CurrentYear)
	if err != nil {
		return nil, err
	}
	value := int(ganzhi4Dayun.ID+ganzhi4CurrentYear.ID) % 64
	if value == 0 {
		value = 64
	}
	guaxiang, err := slf.yunshiRepo.GetGuaxiangByValue(ctx, value)
	if err != nil {
		return nil, err
	}
	result.Guaxiang = guaxiang

	// 论财-运势分
	yunshi := result.Score
	// 计分系数：固定值0.5
	req.Coefficient = 0.5
	var (
		timeStrCurrentYear = fmt.Sprintf("%d-03-01 00:00:00", req.CurrentYear)
		timeStrLastYear    = fmt.Sprintf("%d-03-01 00:00:00", req.CurrentYear-1)
	)
	paipanJieqiScoreCurrentYear, err := slf.coronaCli.GetJieqiScore(ctx, &corona.GetJieqiScoreRequest{
		Birthtime:   req.Birthtime,
		Gender:      req.Gender,
		CurrentTime: timeStrCurrentYear,
	})
	if err != nil {
		return nil, err
	}
	paipanJieqiScoreLastYear, err := slf.coronaCli.GetJieqiScore(ctx, &corona.GetJieqiScoreRequest{
		Birthtime:   req.Birthtime,
		Gender:      req.Gender,
		CurrentTime: timeStrLastYear,
	})
	if err != nil {
		return nil, err
	}
	type MonthObj struct {
		Jieqi   string    // 节气
		Score   int       // 分数
		TimeStr string    // 时间字符串
		Time    time.Time // 时间
		Ganzhi  string    // 干支
		LnScore int       // 流年分数
		DyScore int       // 大运分数
	}
	var months []MonthObj
	splitN := strings.SplitN(paipanJieqiScoreLastYear.PaiyueJq[12-1], ": ", 2)
	parse, err := time.Parse("2006-01-02 15:04:05", splitN[1])
	if err != nil {
		return nil, err
	}
	months = append(months, MonthObj{
		Jieqi:   splitN[0],
		Score:   paipanJieqiScoreLastYear.NowMonthFen[12-1],
		TimeStr: splitN[1],
		Time:    parse,
		Ganzhi:  paipanJieqiScoreLastYear.MonthGanzhiList[12-1],
	})
	for i, jieqi := range paipanJieqiScoreCurrentYear.PaiyueJq {
		splitN := strings.SplitN(jieqi, ": ", 2)
		parse, err := time.Parse("2006-01-02 15:04:05", splitN[1])
		if err != nil {
			return nil, err
		}
		months = append(months, MonthObj{
			Jieqi:   splitN[0],
			Score:   paipanJieqiScoreCurrentYear.NowMonthFen[i],
			TimeStr: splitN[1],
			Time:    parse,
			Ganzhi:  paipanJieqiScoreCurrentYear.MonthGanzhiList[i],
		})
	}
	paipanDayunLiulianScore, err := slf.coronaCli.GetDayunLiunianScore(ctx, &corona.GetDayunLiunianScoreRequest{
		Birthtime: req.Birthtime,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, err
	}
	var (
		lnIndex1 = months[0].Time.Year() - paipanDayun.DayunQishi // 流年索引1（1月取上一年）
		lnIndex2 = months[1].Time.Year() - paipanDayun.DayunQishi // 流年索引2（2月及以后取当前年）
		lnScore1 = paipanDayunLiulianScore.Lnian[lnIndex1]
		lnScore2 = paipanDayunLiulianScore.Lnian[lnIndex2]
		dyScore1 = paipanDayunLiulianScore.Dyun[lnIndex1/5]
		dyScore2 = paipanDayunLiulianScore.Dyun[lnIndex2/5]
	)
	for i, obj := range months {
		if i == 12 {
			continue
		}
		if i == 0 {
			obj.LnScore = lnScore1
			obj.DyScore = dyScore1
		} else {
			obj.LnScore = lnScore2
			obj.DyScore = dyScore2
		}
	}
	yunshi.Dayun = &v1.YunshiScoreDayun{
		Ganzhi: ganzhiName4Dayun,
		Score:  float32(dyScore2),
	}
	var (
		lnGanzhi       = shizhuCurrentYear.Nianzhu
		lnOverallScore = float32(dyScore2)*req.Coefficient + float32(lnScore2)*(1-req.Coefficient)
		lnFinalScore   = lnOverallScore*0.5 + 50
	)
	yunshi.Liunian = &v1.YunshiNianScore{
		InitialScore: float32(lnScore2),
		OverallScore: lnOverallScore,
		FinalScore:   lnFinalScore,
		Keyword: func(score float32) string {
			if score >= 90 {
				return "如日中天"
			} else if score >= 80 && score < 90 {
				return "顺风顺水"
			} else if score >= 70 && score < 80 {
				return "平平稳稳"
			} else if score >= 60 && score < 70 {
				return "砥砺前行"
			} else {
				return "步履维艰"
			}
		}(lnFinalScore),
		Ganzhi: lnGanzhi,
	}
	for i, obj := range months {
		if i == 12 { // 第13个月只返回节气（用于前端显示最佳/谨慎月份时间范围）
			yunshi.Liuyue = append(yunshi.Liuyue, &v1.YunshiYueScore{
				Jieqi:     obj.Jieqi,
				JieqiTime: obj.TimeStr,
			})
			continue
		}
		var (
			lyOverallScore = (float32(obj.DyScore)*req.Coefficient+float32(obj.LnScore)*(1-req.Coefficient))*req.Coefficient + float32(obj.Score)*(1-req.Coefficient)
			lyFinalScore   = lyOverallScore*0.5 + 50
		)
		yunshi.Liuyue = append(yunshi.Liuyue, &v1.YunshiYueScore{
			Jieqi:        obj.Jieqi,
			Ganzhi:       obj.Ganzhi,
			JieqiTime:    obj.TimeStr,
			InitialScore: float32(obj.Score),
			OverallScore: lyOverallScore,
			FinalScore:   lyFinalScore,
		})
	}

	// 论财-建议
	// 本命年
	shizhuBirthYear, err := slf.coronaCli.Shizhu(ctx, &corona.GetShizhuRequest{Birthtime: req.Birthtime})
	if err != nil {
		return nil, err
	}
	mzNianGanzhi := shizhuBirthYear.Nianzhu
	mzNianGan := string([]rune(mzNianGanzhi)[0])
	mzShiGanzhi := shizhuBirthYear.Shizhu
	splitN = strings.SplitN(paipanJieqiScoreCurrentYear.PaiyueJq[0], ": ", 2)
	parse, err = time.Parse("2006-01-02 15:04:05", splitN[1])
	if err != nil {
		return nil, err
	}
	if string([]rune(mzNianGanzhi)[1]) == string([]rune(lnGanzhi)[1]) {
		result.Suggestions = append(result.Suggestions, "今年值本命年，凡事须小心。")
	}
	// 流年地支喜忌
	paipanShizhuBirthBaziAndShengxiao := []string{
		shizhuBirthYear.Nianzhu,
		shizhuBirthYear.Yuezhu,
		shizhuBirthYear.Rizhu,
		shizhuBirthYear.Shizhu,
		shizhuBirthYear.Zodiac,
	}
	mzYueGanzhi := paipanShizhuBirthBaziAndShengxiao[1]
	mzYueGan := string([]rune(mzYueGanzhi)[0])
	mzYueZhi := string([]rune(mzYueGanzhi)[1])
	mzRiGanzhi := paipanShizhuBirthBaziAndShengxiao[2]
	mzRiGan := string([]rune(mzRiGanzhi)[0])
	mzRiZhi := string([]rune(mzRiGanzhi)[1])
	lnGan := string([]rune(lnGanzhi)[0])
	lnZhi := string([]rune(lnGanzhi)[1])
	lnShishen, err := slf.yunshiRepo.GetShishenByRiyuan(ctx, mzRiGan, lnZhi)
	if err != nil {
		return nil, err
	}
	shishen := lnShishen.Shishen
	sls, err := slf.yunshiRepo.GetShishenLiunianSuggestion(ctx, shishen, req.Gender, result.Score.Liunian.OverallScore > 50)
	if err != nil {
		return nil, err
	}
	if sls != nil {
		result.Suggestions = append(result.Suggestions, sls.Suggestion)
	}
	// 流年神煞
	nayinList, err := slf.yunshiRepo.Nayins(ctx)
	if err != nil {
		return nil, err
	}
	//shenshaRules, err := slf.yunshiRepo.Shenshas(ctx)
	//if err != nil {
	//	return nil, err
	//}
	nayinMap := make(map[string]*model.Nayin)
	for _, item := range nayinList {
		nayinMap[item.Zhu] = item
	}

	shenshaResult, err := slf.coronaCli.Shensha(ctx, &corona.GetShenshaRequest{
		Bazi: []string{
			paipanShizhuBirthBaziAndShengxiao[0],
			paipanShizhuBirthBaziAndShengxiao[1],
			paipanShizhuBirthBaziAndShengxiao[2],
			paipanShizhuBirthBaziAndShengxiao[3],
			ganzhiName4Dayun,
			lnGanzhi,
		},
		Gender: req.Gender,
	})
	if err != nil {
		return nil, err
	}
	nianShenshaSet := make(map[string]bool)
	for _, item := range shenshaResult.GetByIndex(0) {
		nianShenshaSet[item] = true
	}
	yueShenshaSet := make(map[string]bool)
	for _, item := range shenshaResult.GetByIndex(1) {
		yueShenshaSet[item] = true
	}
	riShenshaSet := make(map[string]bool)
	for _, item := range shenshaResult.GetByIndex(2) {
		riShenshaSet[item] = true
	}
	shiShenshaSet := make(map[string]bool)
	for _, item := range shenshaResult.GetByIndex(3) {
		shiShenshaSet[item] = true
	}
	dyShenshaSet := make(map[string]bool)
	for _, item := range shenshaResult.GetByIndex(4) {
		dyShenshaSet[item] = true
	}
	lnShenshasSet := make(map[string]bool)
	for _, item := range shenshaResult.GetByIndex(5) {
		lnShenshasSet[item] = true
	}
	if lnShenshasSet["驿马"] && !lnShenshasSet["禄神"] {
		result.Suggestions = append(result.Suggestions, "今年奔走较为频繁。")
	}
	if lnShenshasSet["驿马"] && lnShenshasSet["禄神"] {
		result.Suggestions = append(result.Suggestions, "今年升迁有望，但不可掉以轻心，把握机会。")
	}
	if lnShenshasSet["华盖"] {
		result.Suggestions = append(result.Suggestions, "今年需要谨慎婚姻，学术研究方面或有成就。")
	}
	if lnShenshasSet["桃花"] {
		result.Suggestions = append(result.Suggestions, "今年可能有艳遇，多有恋爱之事，已婚人士请谨慎。")
	}
	if lnShenshasSet["灾煞"] {
		result.Suggestions = append(result.Suggestions, "今年可能有不好的事情发生，建议谨慎行事。")
	}
	if lnShenshasSet["羊刃"] {
		result.Suggestions = append(result.Suggestions, "今年可能有凶灾或破损耗财的事情发生，建议谨慎行事。")
	}
	if lnShenshasSet["空亡"] {
		result.Suggestions = append(result.Suggestions, "今年发现特殊buff，增益效果减少50%，减益效果减少50%。")
	}
	// 流年大运十神
	dyGan := string([]rune(ganzhiName4Dayun)[0])
	dyZhi := string([]rune(ganzhiName4Dayun)[1])
	riTiangan := string([]rune(paipanShizhuBirthBaziAndShengxiao[2])[0])
	dyShishen, err := slf.yunshiRepo.GetShishenByRiyuan(ctx, riTiangan, dyZhi)
	if err != nil {
		return nil, err
	}
	jiuzhuData, err := slf.coronaCli.LifeCyclesMonth(ctx, &corona.LifeCyclesMonthRequest{
		Birthday: req.Birthtime,
		Now:      fmt.Sprintf("%d-03-01 00:00:00", req.CurrentYear),
		Gender:   lo.Ternary(req.Gender == "男", 1, 2),
		Location: []string{},
	})
	if err != nil {
		return nil, err
	}
	sizhuHasCai := jiuzhuData.ZhuXing[0] == "正财" || jiuzhuData.ZhuXing[0] == "偏财" ||
		jiuzhuData.ZhuXing[1] == "正财" || jiuzhuData.ZhuXing[1] == "偏财" ||
		jiuzhuData.ZhuXing[2] == "正财" || jiuzhuData.ZhuXing[2] == "偏财" ||
		jiuzhuData.ZhuXing[3] == "正财" || jiuzhuData.ZhuXing[3] == "偏财" ||
		jiuzhuData.BenqiShiShen[0] == "正财" || jiuzhuData.BenqiShiShen[0] == "偏财" ||
		jiuzhuData.BenqiShiShen[1] == "正财" || jiuzhuData.BenqiShiShen[1] == "偏财" ||
		jiuzhuData.BenqiShiShen[2] == "正财" || jiuzhuData.BenqiShiShen[2] == "偏财" ||
		jiuzhuData.BenqiShiShen[3] == "正财" || jiuzhuData.BenqiShiShen[3] == "偏财" ||
		jiuzhuData.ZhongqiShiShen[0] == "正财" || jiuzhuData.ZhongqiShiShen[0] == "偏财" ||
		jiuzhuData.ZhongqiShiShen[1] == "正财" || jiuzhuData.ZhongqiShiShen[1] == "偏财" ||
		jiuzhuData.ZhongqiShiShen[2] == "正财" || jiuzhuData.ZhongqiShiShen[2] == "偏财" ||
		jiuzhuData.ZhongqiShiShen[3] == "正财" || jiuzhuData.ZhongqiShiShen[3] == "偏财" ||
		jiuzhuData.YuqiShiShen[0] == "正财" || jiuzhuData.YuqiShiShen[0] == "偏财" ||
		jiuzhuData.YuqiShiShen[1] == "正财" || jiuzhuData.YuqiShiShen[1] == "偏财" ||
		jiuzhuData.YuqiShiShen[2] == "正财" || jiuzhuData.YuqiShiShen[2] == "偏财" ||
		jiuzhuData.YuqiShiShen[3] == "正财" || jiuzhuData.YuqiShiShen[3] == "偏财"
	if (lnShishen.Shishen == "正财" || lnShishen.Shishen == "偏财" || dyShishen.Shishen == "正财" || dyShishen.Shishen == "偏财") &&
		(nianShenshaSet["羊刃"] || yueShenshaSet["羊刃"] || riShenshaSet["羊刃"] || shiShenshaSet["羊刃"]) &&
		(dyShenshaSet["羊刃"] || lnShenshasSet["羊刃"]) {
		if sizhuHasCai {
			result.Suggestions = append(result.Suggestions, "今年可能发生财物耗散。")
		} else {
			result.Suggestions = append(result.Suggestions, "今年可能发生轻微的财务耗散。")
		}
	}
	sizhuHasZhengguan := jiuzhuData.ZhuXing[0] == "正官" || jiuzhuData.ZhuXing[1] == "正官" || jiuzhuData.ZhuXing[2] == "正官" || jiuzhuData.ZhuXing[3] == "正官" ||
		jiuzhuData.BenqiShiShen[0] == "正官" || jiuzhuData.BenqiShiShen[1] == "正官" || jiuzhuData.BenqiShiShen[2] == "正官" || jiuzhuData.BenqiShiShen[3] == "正官" ||
		jiuzhuData.ZhongqiShiShen[0] == "正官" || jiuzhuData.ZhongqiShiShen[1] == "正官" || jiuzhuData.ZhongqiShiShen[2] == "正官" || jiuzhuData.ZhongqiShiShen[3] == "正官" ||
		jiuzhuData.YuqiShiShen[0] == "正官" || jiuzhuData.YuqiShiShen[1] == "正官" || jiuzhuData.YuqiShiShen[2] == "正官" || jiuzhuData.YuqiShiShen[3] == "正官"
	if req.Gender == "女" && sizhuHasCai && sizhuHasZhengguan {
		if lnShishen.Shishen == "伤官" && dyShishen.Shishen == "劫财" {
			result.Suggestions = append(result.Suggestions, "今年你的婚姻或者你老公的事业财运，存在些许波折。")
		}
		if lnShishen.Shishen == "劫财" && dyShishen.Shishen == "伤官" {
			result.Suggestions = append(result.Suggestions, "今年你的婚姻或者你老公的事业财运，存在些许波折。")
		}
	}
	if req.Gender == "女" && func() bool {
		num := 0
		for i := 0; i < 4; i++ {
			if jiuzhuData.ZhuXing[i] == "比劫" {
				num++
			}
			if jiuzhuData.BenqiShiShen[i] == "比劫" {
				num++
			}
			if jiuzhuData.ZhongqiShiShen[i] == "比劫" {
				num++
			}
			if jiuzhuData.YuqiShiShen[i] == "比劫" {
				num++
			}
		}
		return num >= 4
	}() {
		if lnShishen.Shishen == "官杀" || dyShishen.Shishen == "官杀" {
			result.Suggestions = append(result.Suggestions, "今年你的婚姻或者你老公的事业财运，存在些许波折。")
		}
	}
	riShishen, err := slf.yunshiRepo.GetShishenByRiyuan(ctx, riTiangan, jiuzhuData.Dizhi[2])
	if err != nil {
		return nil, err
	}
	if riShishen.Shishen == "正财" || riShishen.Shishen == "偏财" {
		if (dyShishen.Shishen == "正财" || dyShishen.Shishen == "偏财") ||
			(lnShishen.Shishen == "正财" || lnShishen.Shishen == "偏财") {
			result.Suggestions = append(result.Suggestions, "今年走财运，容易发财。")
		}
	}
	paipanWuxing, err := slf.coronaCli.GetSizhuWuxingXiyong(ctx, &corona.GetSizhuWuxingXiyongRequest{
		Birthtime: req.Birthtime,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, err
	}
	wuxingName := slf.tianganMapWuxing4Shishen[riTiangan][0]
	wuxingIndex := 0
	for i, name := range strings.Split(paipanWuxing.SaveLiliangWuXingMingzi, ",") {
		if wuxingName == name {
			wuxingIndex = i
			break
		}
	}
	yinxiaoNengliang := paipanWuxing.SaveLiliangNum[wuxingIndex]
	if yinxiaoNengliang >= 150 {
		changsheng, err := slf.yunshiRepo.GetChangshengByName(ctx, "帝旺")
		if err != nil {
			return nil, err
		}
		if changsheng.Mapping[riTiangan] == lnZhi || changsheng.Mapping[riTiangan] == dyZhi {
			result.Suggestions = append(result.Suggestions, "今年登科有望，读书考试即为顺利，但也要好好学习，切勿掉以轻心。")
		}
	}
	if (lnShishen.Shishen == "食神" || lnShishen.Shishen == "伤官") &&
		(dyShishen.Shishen == "食神" || dyShishen.Shishen == "伤官") {
		result.Suggestions = append(result.Suggestions, "今年务必要注重安全，小心因粗心或意外造成的事情。")
	}
	mzNianzhi := string([]rune(mzNianGanzhi)[1])
	mzShizhi := string([]rune(mzShiGanzhi)[1])
	dzxc, err := slf.yunshiRepo.GetDizhiXiangchong(ctx, mzNianzhi, mzShizhi)
	if err != nil {
		return nil, err
	}
	if dzxc != nil && (dyZhi == mzShizhi || lnZhi == mzShizhi) {
		result.Suggestions = append(result.Suggestions, "建议今年多多关心父母身体与安全。")
	}
	lnGan1, err := slf.yunshiRepo.GetTiangan(ctx, lnGan)
	if err != nil {
		return nil, err
	}
	mzNianGan1, err := slf.yunshiRepo.GetTiangan(ctx, mzNianGan)
	if err != nil {
		return nil, err
	}
	wxxk, err := slf.yunshiRepo.GetWuxingXiangke(ctx, lnGan1.Wuxing, mzNianGan1.Wuxing)
	if err != nil {
		return nil, err
	}
	if wxxk != nil {
		mzNianBenqi := jiuzhuData.Benqi[0]
		changsheng, err := slf.yunshiRepo.GetChangshengByName(ctx, "墓")
		if err != nil {
			return nil, err
		}
		if changsheng.Mapping[mzNianBenqi] == lnZhi {
			result.Suggestions = append(result.Suggestions, "建议今年多多关心父母，尤其是父亲的身体与安全。")
		}
	}
	var (
		dzxcIdx = 0
		isDzxc  = false
	)
	for i := 0; i < 4; i++ {
		dizhiMz := jiuzhuData.Dizhi[i]
		dzxc1, err := slf.yunshiRepo.GetDizhiXiangchong(ctx, lnZhi, dizhiMz)
		if err != nil {
			return nil, err
		}
		dzxc2, err := slf.yunshiRepo.GetDizhiXiangchong(ctx, dyZhi, dizhiMz)
		if err != nil {
			return nil, err
		}
		if dzxc1 != nil || dzxc2 != nil {
			dzxcIdx = i
			isDzxc = true
			break
		}
	}
	if isDzxc && func() bool {
		hasThOrYr := false
		for _, shensha := range jiuzhuData.ShenShaJiShen[dzxcIdx] {
			if shensha == "桃花" || shensha == "羊刃" {
				hasThOrYr = true
				break
			}
		}
		return hasThOrYr
	}() {
		result.Suggestions = append(result.Suggestions, "今年运冲桃花，注意不要因色犯刑。")
	}
	if (lnShishen.Shishen == "正财" || lnShishen.Shishen == "偏财") && (dyShishen.Shishen == "正财" || dyShishen.Shishen == "偏财") {
		if func() bool {
			hasYm := false
			for i := 0; i < 4; i++ {
				for _, shensha := range jiuzhuData.ShenShaJiShen[i] {
					if shensha == "驿马" {
						hasYm = true
						break
					}
				}
			}
			return hasYm
		}() {
			result.Suggestions = append(result.Suggestions, "今年有机会大发其财，请好好把握。")
		}
	}
	if req.CurrentYear-birthtimeSun.Year() > 60 && func() bool {
		hasYm := false
		for i := 4; i < 6; i++ {
			for _, shensha := range jiuzhuData.ShenShaJiShen[i] {
				if shensha == "驿马" {
					hasYm = true
					break
				}
			}
		}
		return hasYm
	}() {
		result.Suggestions = append(result.Suggestions, "今年易发生气虚，注意腰腿方面的毛病。")
	}
	if (lnGanzhi == "戊戌" || lnGanzhi == "庚辰" || lnGanzhi == "庚戌" || lnGanzhi == "壬辰") &&
		(mzRiGanzhi == "戊戌" || mzRiGanzhi == "庚辰" || mzRiGanzhi == "庚戌" || mzRiGanzhi == "壬辰") {
		result.Suggestions = append(result.Suggestions, "公职为官之人，今年有提升方面的喜事。（青天大老爷心里多念念百姓）")
	}
	wuhe, err := slf.yunshiRepo.GetTianganWuhe(ctx, mzYueGan, lnGan)
	if err != nil {
		return nil, err
	}
	liuhe, err := slf.yunshiRepo.GetDizhiLiuhe(ctx, mzYueZhi, lnZhi)
	if err != nil {
		return nil, err
	}
	if wuhe != nil && liuhe != nil {
		result.Suggestions = append(result.Suggestions, "今年天合地合，家有喜事。")
	}
	tiangan, err := slf.yunshiRepo.GetTiangan(ctx, mzRiGan)
	if err != nil {
		return nil, err
	}
	wuxing, err := slf.yunshiRepo.GetWuxingAboutXiangkeByName(ctx, tiangan.Wuxing)
	dizhiList := []string{dyZhi, mzNianzhi, mzYueZhi, mzRiZhi, mzShizhi}
	dizhiSet := make(map[string]struct{})
	for _, dz := range dizhiList {
		dizhiSet[dz] = struct{}{}
	}
	dizhiList = []string{}
	for s := range dizhiSet {
		dizhiList = append(dizhiList, s)
	}
	array := algo.CombinationsFromArray(dizhiList, 3)
	mzYueZhiWuxing, err := slf.yunshiRepo.GetDizhi(ctx, mzYueZhi)
	if err != nil {
		return nil, err
	}
	mzRiGanWuxing, err := slf.yunshiRepo.GetTiangan(ctx, mzRiGan)
	if err != nil {
		return nil, err
	}
	riyuan := paipanAll.Riyuan
	if func() bool {
		isFound := false
		for _, com := range array {
			sanhe, err := slf.yunshiRepo.GetDizhiSanhe(ctx, com[0], com[1], com[2])
			if err != nil {
				return false
			}
			if sanhe != nil && wuxing.Xiangke == sanhe.Hehua {
				isFound = true
				break
			}
		}
		return isFound
	}() {
		if riyuan == "平和" {
			xs1, err := slf.yunshiRepo.GetWuxingXiangsheng(ctx, mzYueZhiWuxing.Wuxing, mzRiGanWuxing.Wuxing)
			if err != nil {
				return nil, err
			}
			xs2, err := slf.yunshiRepo.GetWuxingXiangsheng(ctx, mzRiGanWuxing.Wuxing, mzYueZhiWuxing.Wuxing)
			if err != nil {
				return nil, err
			}
			xk1, err := slf.yunshiRepo.GetWuxingXiangke(ctx, mzYueZhiWuxing.Wuxing, mzRiGanWuxing.Wuxing)
			if err != nil {
				return nil, err
			}
			xk2, err := slf.yunshiRepo.GetWuxingXiangke(ctx, mzRiGanWuxing.Wuxing, mzYueZhiWuxing.Wuxing)
			if err != nil {
				return nil, err
			}
			if xs1 != nil || mzYueZhiWuxing.Wuxing == mzRiGanWuxing.Wuxing {
				riyuan = "身强"
			} else if xs2 != nil || (xk1 != nil || xk2 != nil) {
				riyuan = "身弱"
			}
		}
		if riyuan == "身强" || riyuan == "从弱" {
			result.Suggestions = append(result.Suggestions, "今年的大运财运极好。")
		}
		if riyuan == "身弱" || riyuan == "从强" {
			result.Suggestions = append(result.Suggestions, "今年的大运钱财花销损耗较大。")
		}
	}
	wuhe, err = slf.yunshiRepo.GetTianganWuhe(ctx, dyGan, mzRiGan)
	if err != nil {
		return nil, err
	}
	if wuhe != nil && (riyuan == "身强" || riyuan == "从弱") {
		xk, err := slf.yunshiRepo.GetWuxingXiangke(ctx, mzRiGanWuxing.Wuxing, wuhe.Tiangan1)
		if err != nil {
			return nil, err
		}
		if xk != nil {
			result.Suggestions = append(result.Suggestions, "今年的流年财运较好。")
		}
	}
	if func() bool {
		return dyShishen.Shishen == "正财" || dyShishen.Shishen == "偏财"
	}() && (riyuan == "身弱" || riyuan == "从强") {
		result.Suggestions = append(result.Suggestions, "财运方面，易为钱财奔波劳累。")
	}
	// 大运用神到位（大运地支五行为喜用）
	xyjcx := strings.SplitN(paipanWuxing.Xiyongjichou, ",", 5)
	dyZhiWuxing, err := slf.yunshiRepo.GetDizhi(ctx, dyZhi)
	if err != nil {
		return nil, err
	}
	if dyZhiWuxing.Wuxing == xyjcx[0] || dyZhiWuxing.Wuxing == xyjcx[1] {
		result.Suggestions = append(result.Suggestions, "今年大运到位，今年总体平安、兴旺、发达。")
	}
	// 大运最后一年
	if (req.CurrentYear >= paipanDayun.DayunQishi) && ((req.CurrentYear-paipanDayun.DayunQishi)%10 == 9) {
		result.Suggestions = append(result.Suggestions, "今年是大运将交未交的最后一年，往往有人生重大之变动，建议谨慎度过。")
	}
	// 相关事宜
	switch lnShishen.Shishen {
	case "比肩", "劫财":
		result.Suggestions = append(result.Suggestions, "今年主要发生和朋友，同事，兄弟姐妹有关的事。")
	case "食神", "伤官":
		result.Suggestions = append(result.Suggestions, "今年主要发生和女命的子女、晚辈、下属相关的，或者作品，演说，言论，演出，跳舞，展示，投资，策划之类事情。")
	case "正财", "偏财":
		if req.Gender == "男" {
			result.Suggestions = append(result.Suggestions, "今年主要发生和同妻子，父亲，财运，身体健康工作以及婚姻，感情方面之事。")
		} else {
			result.Suggestions = append(result.Suggestions, "今年主要发生和同父亲，财运，身体健康、工作方面之事。")
		}
	case "正官", "七杀":
		if req.Gender == "男" {
			result.Suggestions = append(result.Suggestions, "今年主要发生和同父亲，工作，职务，职业，名誉，官司，病伤灾等方面管你之事。")
		} else {
			result.Suggestions = append(result.Suggestions, "今年主要发生和同老公，工作，职务，职业，名誉，官司，病伤灾等方面管你之事。")
		}
	case "正印", "偏印":
		result.Suggestions = append(result.Suggestions, "今年主要发生和母亲，学习，工作，单位，名誉，票据，住房，疾病，财运有关之事。")
	default:
	}

	// 创建排盘记录
	ret, _ := slf.irs.Query(req.IP)
	result.ID, err = slf.paipanRecordRepo.CreatePaipanRecord(ctx, &model.PaipanRecord{
		UserID: func() string {
			if req.User != nil {
				return req.User.UserID
			} else {
				return ""
			}
		}(),
		Name:           req.Name,
		Gender:         lo.Ternary(req.Gender == "男", 1, 2),
		Birthtime:      birthtime,
		BirthtimeSun:   birthtimeSun,
		BirthtimeLunar: result.Mingzhu.BirthtimeLunar,
		Bazi:           result.Mingzhu.Bazi,
		Birthplace:     req.Birthplace,
		UserAgent:      req.UserAgent,
		IP:             ret.IP,
		Region:         ret.Region(),
		AppID:          4, // 万年历
		AppPlatformID:  1, // 未知（默认）
		SaveTime:       time.Now(),
		Type:           1,
	})
	if err != nil {
		return nil, err
	}
	return result, nil
}

// BirthtimeSun 获取真太阳时间
func (slf *yunshiService) realSunTime(ctx context.Context, birthtime time.Time, location []string) (time.Time, error) {
	if len(location) == 0 {
		return birthtime, nil
	}
	join := strings.Join(location, "")
	if len(location) == 3 {
		join = "中国" + join
	}
	offset, err := slf.yunshiRepo.GetOffset4TimeByLocation(ctx, join)
	if err != nil {
		return time.Time{}, err
	}
	return birthtime.Add(time.Duration(offset) * time.Minute), nil
}
