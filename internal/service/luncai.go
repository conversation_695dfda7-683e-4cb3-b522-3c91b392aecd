package service

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"strings"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/array"
	"zodiacus/pkg/strs"
	"zodiacus/third_party/corona"
)

type LuncaiService interface {
	Luncai(ctx context.Context, req *v1.LuncaiRequest) (*v1.LuncaiResponseData, error)
	LuncaiReplay(ctx context.Context, req *v1.LuncaiReplayRequest) (*v1.LuncaiReplayResponseData, error)
}

func NewLuncaiService(
	service *Service,
	luncaiRepo repository.LuncaiRepository,
	paipanRepo repository.UserPaipanRecordRepository,
	orderRepo repository.UserOrderRepository,
	vipRepo repository.VIPRepository,
) LuncaiService {
	return &luncaiService{
		Service:          service,
		luncaiRepo:       luncaiRepo,
		paipanRecordRepo: paipanRepo,
		orderRepo:        orderRepo,
		vipRepo:          vipRepo,
	}
}

type luncaiService struct {
	*Service
	luncaiRepo       repository.LuncaiRepository
	orderRepo        repository.UserOrderRepository
	paipanRecordRepo repository.UserPaipanRecordRepository
	vipRepo          repository.VIPRepository
}

func (slf *luncaiService) LuncaiReplay(ctx context.Context, req *v1.LuncaiReplayRequest) (*v1.LuncaiReplayResponseData, error) {
	record, err := slf.paipanRecordRepo.FetchPaipanRecordByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if record == nil {
		return nil, nil
	}
	return slf.Luncai(ctx, &v1.LuncaiRequest{
		Birthtime:  record.Birthtime.Format("2006-01-02 15:04:05"),
		Gender:     lo.Ternary(record.Gender == 1, "男", "女"),
		Name:       record.Name,
		Birthplace: record.Birthplace,
		IsReplay:   true,
	})
}

func (slf *luncaiService) isVIP(ctx context.Context, userID, application string) bool {
	duration, err := slf.vipRepo.GetVIPMemberInfo(ctx, application, userID)
	if err != nil {
		slf.logger.WithContext(ctx).Error("get vip info error", zap.Error(err))
		return false
	}
	return duration != nil && duration.ExpireTime.After(time.Now())
}

func (slf *luncaiService) Luncai(ctx context.Context, req *v1.LuncaiRequest) (*v1.LuncaiResponseData, error) {
	if req.Birthplace == nil {
		req.Birthplace = []string{}
	}
	birthtime, err := time.Parse("2006-01-02 15:04:05", req.Birthtime)
	if err != nil {
		return nil, errors.Wrap(err, "time.Parse")
	}
	birthtimeSun, err := slf.realSunTime(ctx, birthtime, req.Birthplace)
	if err != nil {
		return nil, errors.Wrap(err, "realSunTime")
	}
	realSunTimeStr := birthtimeSun.Format("2006-01-02 15:04:05")
	//currentTime := time.Now().Format("2006-01-02 15:04:05")
	paipanAll, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: realSunTimeStr,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.PaipanAll")
	}

	// =================权限检查=================
	isShow := func() bool {
		// 判定条件：是否示例
		if req.IsExample {
			return true
		}
		// 判定条件一：是否重放（单词购买后根据先前的luncai_id查看结果）
		if req.IsReplay {
			return true
		}
		if req.User != nil { // 判定条件三：已登录用户，该八字是否已有支付成功订单
			orders, err := slf.orderRepo.FetchUserOrderByLuncaiBazi(ctx, req.User.UserID, paipanAll.Sizhu)
			if err != nil {
				return false
			}
			// 判定条件二：VIP用户
			if slf.isVIP(ctx, req.User.UserID, req.Application) {
				return true
			}
			if len(orders) > 0 {
				return true
			}
		} else { // 判定条件四：未登录用户，该设备ID是否已有支付成功订单
			orders, err := slf.orderRepo.FetchAnonymousOrderByBazi(ctx, req.DeviceID, paipanAll.Sizhu)
			if err != nil {
				return false
			}
			if len(orders) > 0 {
				return true
			}
		}
		return true
	}()
	// =================权限检查=================
	result := &v1.LuncaiResponseData{
		IsShow: isShow,
	}
	// 1.命主信息
	result.Mingzhu = v1.LuncaiMinzhu{
		Name:           req.Name,
		Birthtime:      realSunTimeStr,
		BirthtimeLunar: strs.Digit2ZhUpper(paipanAll.Nongli),
		Birthplace:     req.Birthplace,
		Gender:         req.Gender,
		Bazi:           paipanAll.Sizhu,
		Wuxing:         strings.SplitN(paipanAll.Xiyongjichou, ",", 5),
		Zodiac:         paipanAll.Shengxiao,
		Zhuxing:        paipanAll.TianganBenqiSsjxList,
	}
	// 2.命理概述
	paipanBaziMap, err := slf.coronaCli.PaipanBaziMap(ctx, &corona.GetPaipanBaziMapRequest{
		Birthday: realSunTimeStr,
		Gender:   lo.Ternary(req.Gender == "男", 1, 2),
		IsLunar:  false,
		Location: req.Birthplace,
		Name:     req.Name,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.HoroscopeBaziMap")
	}
	paipanDetail, err := slf.coronaCli.PaipanDetail(ctx, &corona.GetPaipanDetailRequest{
		Birthday: realSunTimeStr,
		Gender:   lo.Ternary(req.Gender == "男", 1, 2),
		IsLunar:  false,
		Location: req.Birthplace,
		Name:     req.Name,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.HoroscopeInformation")
	}
	result.Mingli = v1.LuncaiMingli{
		TongYi:          paipanBaziMap.Data.TongYi,
		TongYiPercent:   paipanBaziMap.Data.TongYiPercent,
		YingYang:        paipanBaziMap.Data.YingYang,
		YingYangPercent: paipanBaziMap.Data.YingYangPercent,
		MapTransfer:     &paipanBaziMap.Data.MapTransfer,
		LiuTongNum:      paipanBaziMap.Data.LiuTongNum,
		ZuaiNum:         paipanBaziMap.Data.ZuaiNum,
		Pingfen:         paipanBaziMap.Data.Pingfen,
		Wangshuai:       paipanAll.Riyuan,
		Gejucankao:      paipanDetail.Data.GejuCankao,
		Tiaohou:         paipanDetail.Data.TiaoHouYongShen,
		Wuxing:          strings.SplitN(paipanAll.Xiyongjichou, ",", 5),
	}
	// 3.五行喜用看职业：命理信息已包含五行用喜，可根据五行职业枚举自行判断。
	// 4.能量与求财方式
	// 5.发展建议：根据喜用五行从枚举中查询。
	result.Nengliang = paipanBaziMap.Data.ShishenMapPower
	// 更多职业建议
	var (
		shishenSet        = make(map[string]bool) // 命局
		shishenNum        = make(map[string]int)  // 命局
		zhuxingShishenSet = make(map[string]bool) // 命局
		zhuxingShishenNum = make(map[string]int)  // 命局
		fuxingShishenSet  = make(map[string]bool) // 命局
		fuxingShishenNum  = make(map[string]int)  // 命局
		shenshaSet        = make(map[string]bool) // 神煞
	)
	for _, list := range paipanAll.Shensha4 {
		for _, s := range list {
			shenshaSet[s] = true
		}
	}
	for i := 0; i < 4; i++ { // 命局：年、月、日、时
		shishenSet[paipanAll.BenqiShishen[i]] = true
		shishenNum[paipanAll.BenqiShishen[i]]++
		zhuxingShishenSet[paipanAll.BenqiShishen[i]] = true
		zhuxingShishenNum[paipanAll.BenqiShishen[i]]++
		shishenSet[paipanAll.ZhongqiShishen[i]] = true
		for _, name := range paipanAll.BenqiShishen {
			shishenSet[name] = true
			shishenNum[name]++
			fuxingShishenSet[name] = true
			fuxingShishenNum[name]++
		}
		shishenSet[paipanAll.ZhongqiShishen[i]] = true
		shishenNum[paipanAll.ZhongqiShishen[i]]++
		fuxingShishenSet[paipanAll.ZhongqiShishen[i]] = true
		fuxingShishenNum[paipanAll.ZhongqiShishen[i]]++
		shishenSet[paipanAll.YuqiShishen[i]] = true
		shishenNum[paipanAll.YuqiShishen[i]]++
		fuxingShishenSet[paipanAll.YuqiShishen[i]] = true
		fuxingShishenNum[paipanAll.YuqiShishen[i]]++
	}
	//- 食伤泄秀
	riyuan := paipanAll.Riyuan         // 日元
	mzYueZhiName := paipanAll.Dizhi[1] // 月支
	mzYueZhiShishen := paipanAll.BenqiShishen[1]

	// 职业建议
	result.CareerSuggestion = slf.Helper.LuncaiOccupation(ctx, paipanAll).MoreSuggestion

	// 财富所属
	var (
		mzTianganZhengcaiNum, mzTianganPiancaiNum int
		mzDizhiZhengcaiNum, mzDizhiPiancaiNum     int
	)
	{
		tbl := &v1.LuncaiBelongTable{}
		for i := 0; i < 4; i++ {
			switch i {
			case 0:
				tbl.Gongwei = append(tbl.Gongwei, []string{"祖辈宫"})
				tbl.Sizhu = append(tbl.Sizhu, "年柱")
				tbl.LifeStage = append(tbl.LifeStage, "少年")
				tbl.Nianling = append(tbl.Nianling, "1-18岁")
			case 1:
				tbl.Gongwei = append(tbl.Gongwei, []string{"事业宫", "父母宫", "兄弟宫"})
				tbl.Sizhu = append(tbl.Sizhu, "月柱")
				tbl.LifeStage = append(tbl.LifeStage, "青年")
				tbl.Nianling = append(tbl.Nianling, "19-36岁")
			case 2:
				tbl.Gongwei = append(tbl.Gongwei, []string{"夫妻宫"})
				tbl.Sizhu = append(tbl.Sizhu, "日柱")
				tbl.LifeStage = append(tbl.LifeStage, "中年")
				tbl.Nianling = append(tbl.Nianling, "37-54岁")
			case 3:
				tbl.Gongwei = append(tbl.Gongwei, []string{"子女宫"})
				tbl.Sizhu = append(tbl.Sizhu, "时柱")

				tbl.LifeStage = append(tbl.LifeStage, "晚年")
				tbl.Nianling = append(tbl.Nianling, "55岁及以上")
			}
			tbl.Tiangan = append(tbl.Tiangan, paipanAll.Tiangan[i])
			tbl.Dizhi = append(tbl.Dizhi, paipanAll.Dizhi[i])
			// 天干财位计算：
			tgShishen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, paipanAll.Tiangan[2], paipanAll.Tiangan[i])
			if err != nil {
				return nil, err
			}
			tbl.TianganShishen = append(tbl.TianganShishen, tgShishen.Shishen)
			if (tgShishen.Shishen == "正财" || tgShishen.Shishen == "偏财") && (i != 2) { // 日柱天干不参与计算
				tbl.TianganCaiwei = append(tbl.TianganCaiwei, tgShishen.Shishen)
			} else {
				tbl.TianganCaiwei = append(tbl.TianganCaiwei, "")
			}
			// 地支财位计算：
			dzShishen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, paipanAll.Tiangan[2], paipanAll.Dizhi[i])
			if err != nil {
				return nil, err
			}
			tbl.DizhiShishen = append(tbl.DizhiShishen, dzShishen.Shishen)
			if paipanAll.Benqi[i] != "" {
				dzBenqiShishen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, paipanAll.Tiangan[2], paipanAll.Benqi[i])
				if err != nil {
					return nil, err
				}
				tbl.BenqiShishen = append(tbl.BenqiShishen, dzBenqiShishen.Shishen)
			} else {
				tbl.BenqiShishen = append(tbl.BenqiShishen, "")
			}
			if paipanAll.Zhongqi[i] != "" {
				dzZhongqiShishen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, paipanAll.Tiangan[2], paipanAll.Zhongqi[i])
				if err != nil {
					return nil, err
				}
				tbl.ZhongqiShishen = append(tbl.ZhongqiShishen, dzZhongqiShishen.Shishen)
			} else {
				tbl.ZhongqiShishen = append(tbl.ZhongqiShishen, "")
			}
			if paipanAll.Yuqi[i] != "" {
				dzYuqiShishen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, paipanAll.Tiangan[2], paipanAll.Yuqi[i])
				if err != nil {
					return nil, err
				}
				tbl.YuqiShishen = append(tbl.YuqiShishen, dzYuqiShishen.Shishen)
			} else {
				tbl.YuqiShishen = append(tbl.YuqiShishen, "")
			}
			if dzShishen.Shishen == "正财" || dzShishen.Shishen == "偏财" {
				tbl.DizhiCaiwei = append(tbl.DizhiCaiwei, dzShishen.Shishen)
			} else {
				tbl.DizhiCaiwei = append(tbl.DizhiCaiwei, "")
			}
		}
		dtl := &v1.LuncaiBelongDetail{
			Cangcai: &v1.LuncaiBelongDetailCangcai{
				TianganCaiNum: func() int {
					var num int
					for i, s := range tbl.TianganShishen {
						if (s == "正财" || s == "偏财") && (i != 2) {
							num++
						}
						if s == "正财" {
							mzTianganZhengcaiNum++
						}
						if s == "偏财" {
							mzTianganPiancaiNum++
						}
					}
					return num
				}(),
				DizhiCaiNum: func() int {
					var num int
					for i := 0; i < 4; i++ {
						if tbl.BenqiShishen[i] == "正财" || tbl.BenqiShishen[i] == "偏财" {
							num++
						}
						if tbl.BenqiShishen[i] == "正财" {
							mzDizhiZhengcaiNum++
						}
						if tbl.BenqiShishen[i] == "偏财" {
							mzDizhiPiancaiNum++
						}
						if tbl.ZhongqiShishen[i] == "正财" || tbl.ZhongqiShishen[i] == "偏财" {
							num++
						}
						if tbl.ZhongqiShishen[i] == "正财" {
							mzDizhiZhengcaiNum++
						}
						if tbl.ZhongqiShishen[i] == "偏财" {
							mzDizhiPiancaiNum++
						}
						if tbl.YuqiShishen[i] == "正财" || tbl.YuqiShishen[i] == "偏财" {
							num++
						}
						if tbl.YuqiShishen[i] == "正财" {
							mzDizhiZhengcaiNum++
						}
						if tbl.YuqiShishen[i] == "偏财" {
							mzDizhiPiancaiNum++
						}
					}
					return num
				}(),
				Deling: func() bool {
					yzShishen := tbl.DizhiShishen[1]
					return yzShishen == "正印" || yzShishen == "偏印" || yzShishen == "比肩" || yzShishen == "劫财"
				}(),
				Shiling: func() bool {
					yzShishen := tbl.DizhiShishen[1]
					return yzShishen != "正印" && yzShishen != "偏印" && yzShishen != "比肩" && yzShishen != "劫财"
				}(),
				AncangRuku: func() bool {
					for i := 0; i < 4; i++ {
						if (paipanAll.Tiangan[i] == "辰" || paipanAll.Tiangan[i] == "戌" || paipanAll.Tiangan[i] == "丑" || paipanAll.Tiangan[i] == "未") ||
							(paipanAll.Dizhi[i] == "辰" || paipanAll.Dizhi[i] == "戌" || paipanAll.Dizhi[i] == "丑" || paipanAll.Dizhi[i] == "未") {
							if (tbl.DizhiShishen[i] == "正财" || tbl.DizhiShishen[i] == "偏财") ||
								(tbl.BenqiShishen[i] == "正财" || tbl.BenqiShishen[i] == "偏财") ||
								(tbl.ZhongqiShishen[i] == "正财" || tbl.ZhongqiShishen[i] == "偏财") ||
								(tbl.YuqiShishen[i] == "正财" || tbl.YuqiShishen[i] == "偏财") {
								return true
							}
						}
					}
					return false
				}(),
			},
			CaixingXiji: func() string {
				splitN := strings.SplitN(paipanAll.Xiyongjichoushishen, ",", 5)
				if (splitN[0] == "财星" || splitN[1] == "财星") ||
					((splitN[4] == "财星") && (riyuan == "身强" || riyuan == "从弱")) {
					return "财星为喜用神，大利求财，公私可求，从事商业经营可得财。"
				}
				if (splitN[2] == "财星" || splitN[3] == "财星") ||
					((splitN[4] == "财星") && (riyuan == "身弱" || riyuan == "从强")) {
					return "财星为仇忌神，求财不易，须多费苦心经营。"
				}
				return ""
			}(),
			Opportunity: func() string {
				var (
					ssg   = corona.NewShishenGetter(paipanAll.GetShishenBaziTupleNewTgList[:4], paipanAll.TianganBenqiSsListLiuAll[:4], paipanAll.TianganZhongqiSsListLiuAll[:4], paipanAll.TianganYuqiSsListLiuAll[:4])
					ids   []int
					tgArr []string
				)
				for i := 0; i < 4; i++ {
					if i != 2 && array.Has([]string{"正财", "偏财"}, ssg.Tg[i]) {
						ids = append(ids, i)
						switch i {
						case 0:
							tgArr = append(tgArr, "年干")
						case 1:
							tgArr = append(tgArr, "月干")
						case 2:
							tgArr = append(tgArr, "日干")
						case 3:
							tgArr = append(tgArr, "时干")
						}
					}
					if array.Has([]string{"正财", "偏财"}, ssg.Bq[i]) {
						ids = append(ids, i)
						switch i {
						case 0:
							tgArr = append(tgArr, "年支本气藏干")
						case 1:
							tgArr = append(tgArr, "月支本气藏干")
						case 2:
							tgArr = append(tgArr, "日支本气藏干")
						case 3:
							tgArr = append(tgArr, "时支本气藏干")
						}
					}
					if array.Has([]string{"正财", "偏财"}, ssg.Zq[i]) {
						ids = append(ids, i)
						switch i {
						case 0:
							tgArr = append(tgArr, "年支中气藏干")
						case 1:
							tgArr = append(tgArr, "月支中气藏干")
						case 2:
							tgArr = append(tgArr, "日支中气藏干")
						case 3:
							tgArr = append(tgArr, "时支中气藏干")
						}
					}
					if array.Has([]string{"正财", "偏财"}, ssg.Yq[i]) {
						ids = append(ids, i)
						switch i {
						case 0:
							tgArr = append(tgArr, "年支余气藏干")
						case 1:
							tgArr = append(tgArr, "月支余气藏干")
						case 2:
							tgArr = append(tgArr, "日支余气藏干")
						case 3:
							tgArr = append(tgArr, "时支余气藏干")
						}
					}
				}
				ids = array.Unique(ids)
				if len(ids) == 0 {
					return "四柱无财星"
				}
				var (
					detail = fmt.Sprintf("财星位于%s", strings.Join(tgArr, "、"))
				)
				if array.Has(ids, 0) { // 年柱有财才
					if array.Has(ids, 1, 2) { // 月柱、日柱都有财才
						detail += "，代表您的财富来源于祖辈或国家体制内，或在您的青中年时期拼搏收获"
					} else if array.Has(ids, 1) { // 月柱有财才
						detail += "，代表您的财富来源于祖辈或国家体制内，或在您的青年时期拼搏收获"
					} else if array.Has(ids, 2) { // 日柱有财才
						detail += "，代表您的财富来源于祖辈或国家体制内，或在您的青年时期拼搏收获"
					} else {
						detail += "，代表您的财富来源于祖辈或国家体制内"
					}
				} else {
					if array.Has(ids, 1, 2) { // 月柱、日柱都有财才
						detail += "，代表您的财富或在您的青中年时期拼搏收获"
					} else if array.Has(ids, 1) { // 月柱有财才
						detail += "，代表您的财富或在您的青年时期拼搏收获"
					} else if array.Has(ids, 2) { // 日柱有财才
						detail += "，代表您的财富或在您的青年时期拼搏收获"
					}
				}
				if array.Has(ids, 3) {
					detail += "，晚年享有财富"
				}
				detail += "。"
				return detail
			}(),
		}
		result.Belong = &v1.LuncaiBelong{
			Table:  tbl,
			Detail: dtl,
		}
	}
	// 事业宫
	{
		sanhe, err := slf.luncaiRepo.GetDizhiSanheByOne(ctx, mzYueZhiName)
		if err != nil {
			return nil, err
		}
		var sanheDizhi1, sanheDizhi2 string
		if sanhe.Dizhi1 == mzYueZhiName {
			sanheDizhi1 = sanhe.Dizhi2
			sanheDizhi2 = sanhe.Dizhi3
		} else if sanhe.Dizhi2 == mzYueZhiName {
			sanheDizhi1 = sanhe.Dizhi1
			sanheDizhi2 = sanhe.Dizhi3
		} else {
			sanheDizhi1 = sanhe.Dizhi1
			sanheDizhi2 = sanhe.Dizhi2
		}
		liuhe, err := slf.luncaiRepo.GetDizhiLiuheByOne(ctx, mzYueZhiName)
		if err != nil {
			return nil, err
		}
		var liuheDizhi string
		if liuhe.Dizhi1 == mzYueZhiName {
			liuheDizhi = liuhe.Dizhi2
		} else {
			liuheDizhi = liuhe.Dizhi1
		}
		mzYuezhi, err := slf.luncaiRepo.GetDizhi(ctx, mzYueZhiName)
		if err != nil {
			return nil, err
		}
		result.CareerPalace = &v1.LuncaiCareerPalace{
			Middle:  mzYueZhiName,
			Left:    sanheDizhi1,
			Right:   sanheDizhi2,
			Down:    liuheDizhi,
			Shishen: mzYueZhiShishen,
			Yueling: v1.LuncaiCareerPalaceYueling{
				Xiyong: mzYuezhi.Dizhi + mzYuezhi.Wuxing,
				Dizhi: []string{
					sanheDizhi1, sanheDizhi2, liuheDizhi,
				},
			},
		}
	}
	// 财源与求财意向
	{
		totalZhengzaiNum, totalPiancaiNum := mzTianganZhengcaiNum+mzDizhiZhengcaiNum, mzTianganPiancaiNum+mzDizhiPiancaiNum
		// 财源
		if totalZhengzaiNum > 0 && totalPiancaiNum > 0 {
			result.Caiyuan = append(result.Caiyuan, "您的财富来源多样，除稳定的收入外，有副业收入的机会，偶尔会有意外之财；建议您把握好机会，同时提升自己能力与修养，维系好关键关系，加强个人财富获取的核心竞争力。")
		} else if totalZhengzaiNum > 0 && totalPiancaiNum == 0 {
			result.Caiyuan = append(result.Caiyuan, "您的财富来源多样性一般，主要是稳定的收入，很少获得意外之财。")
		} else if totalZhengzaiNum == 0 && totalPiancaiNum > 0 {
			result.Caiyuan = append(result.Caiyuan, "您的财富来源多样性一般，财富来源较为不稳定，易获得意外之财。")
		} else {
			result.Caiyuan = append(result.Caiyuan, "您的财富情况较为特殊，需要结合其他方法推算财源的多样性。如需详细了解，建议咨询资深专业人员。")
		}
		var percent float64
		for _, item := range result.Nengliang {
			if item.ShiShenArr[0] == "正财" || item.ShiShenArr[0] == "偏财" {
				percent = item.PowerBfbArr[0] + item.PowerBfbArr[1]
				break
			}
		}
		if percent < 0.05 {
			result.Caiyuan = append(result.Caiyuan, fmt.Sprintf("您的财星能量占比为%.2f%%，求财意向不强。", percent))
		} else if percent >= 0.05 && percent <= 0.1 {
			result.Caiyuan = append(result.Caiyuan, fmt.Sprintf("您的财星能量占比为%.2f%%，求财意向一般。", percent))
		} else {
			result.Caiyuan = append(result.Caiyuan, fmt.Sprintf("您的财星能量占比为%.2f%%，求财意向较强。", percent))
		}
	}
	// 风险与偏好
	tbl := result.Belong.Table
	result.Mingli.Shishen = [][]string{
		{tbl.TianganShishen[0], tbl.DizhiShishen[0]},
		{tbl.TianganShishen[1], tbl.DizhiShishen[1]},
		{"日主", tbl.DizhiShishen[2]},
		{tbl.TianganShishen[3], tbl.DizhiShishen[3]},
	}
	{
		result.Risk = &v1.LuncaiRisk{}
		result.Risk.Shishen = result.Mingli.Shishen
		numSet := make(map[string]int)
		for _, arr := range result.Risk.Shishen {
			for _, s := range arr {
				if s != "日主" {
					numSet[s]++
				}
			}
		}
		redNum := numSet["七杀"] + numSet["伤官"] + numSet["劫财"] + numSet["偏财"]
		blackNum := numSet["正官"] + numSet["正印"] + numSet["食神"] + numSet["正财"]
		//grayNum := set["比肩"] + set["偏印"]
		if redNum == 0 {
			result.Risk.Detail = append(result.Risk.Detail, "谨慎型：不喜欢冒险，在保本的前提下获取收益，适合稳定职业，理财上选择国债、定期大额存单、货币基金等理财产品。")
		} else if blackNum > redNum {
			result.Risk.Detail = append(result.Risk.Detail, "稳健型：较害怕风险，但是又希望保本的基础上有一定的收益，适合谋取稳定职业，如，或者买债券、货币基金、存款类理财产品、银行中短期理财产品等。")
		} else if blackNum == redNum {
			result.Risk.Detail = append(result.Risk.Detail, "平衡型：能接受一定的风险，会综合考虑风险和收益，适合谋取相对稳定职业，或者尝试货币基金、基金投资、股票、外汇、银行固定预期收益理财等组合方式投资。")
		} else if redNum > blackNum {
			result.Risk.Detail = append(result.Risk.Detail, "激进型：倾向于有风险高收益的理财投资，适合创业，理财偏向于股票型基金、私募基金等投资方式。")
		}
		powerSet := make(map[string]float64)
		for _, item := range result.Nengliang {
			for j, s := range item.ShiShenArr {
				powerSet[s] += item.PowerBfbArr[j]
			}
		}
		redPower := powerSet["七杀"] + powerSet["伤官"] + powerSet["劫财"] + powerSet["偏财"]
		blackPower := powerSet["正官"] + powerSet["正印"] + powerSet["食神"] + powerSet["正财"]
		if redPower == blackPower {
			result.Risk.Detail = append(result.Risk.Detail, "您的动星与静星能量较为平衡，总体来说既具有，缜密稳重、和顺的一面，表现出对体面工作的追求和稳定性格的偏好；同时又具备生性好动、反应灵敏、积极进取的特质，不安于现状，敢于冒险。具体会受流年大运的影响，即在不同时间，会有不同的偏向。")
		} else if redPower > blackPower {
			result.Risk.Detail = append(result.Risk.Detail, "您的动星较多，总体来说，偏于生性好动，反应灵敏，偏于积极进取，不安于现状，热情、激情喜形于色，爱僧分明，有一种挑战心理，敢于冒险，比较开放，灵活机动，具有开拓精神。不论什么工作、体面与否，只要能赚钱都可以干，追求物质上的享受。一生多从事流动性的，具有主动性大的行业。需要时，能主动求人；被人求他也痛快。社会活动能力强，有社交能力。在企业人事安排上，开拓市场就用动星多的人，动有鲁莽性，醋也，动有武，不拘小节，偏于外向。")
		} else {
			result.Risk.Detail = append(result.Risk.Detail, "您的静星较多，总体来说，缜密稳重，工作事业在乎体面，不会上街叫卖。好吃素，性格和顺，一生工作稳定性较强，不喜变化，有从事公职之像，如从事私营往往也是体面性，稳定性的工作，比较安稳，流动性不强。宁肯少挣钱，也不干多挣钱不体面的工作，不善于搞生意，善于做有益于社会的事，正星的人忧患意识强。在企业人事安排上，搞管理就用静星多的人，心细好静。")
		}
	}
	// 担财
	{

		var (
			ability, recommendation string
			isNormalGeju            = func() bool {
				return map[string]bool{
					"正官格":                 true,
					"七杀格":                 true,
					"正印格":                 true,
					"偏印（枭神）格":           true,
					"偏印格":                 true,
					"枭神格":                 true,
					"食神格":                 true,
					"伤官格":                 true,
					"偏财格":                 true,
					"正财格":                 true,
					"比肩格(建禄格)":         true,
					"比肩格":                 true,
					"建禄格":                 true,
					"劫财格(羊刃格或阳刃格)": true,
					"劫财格":                 true,
					"羊刃格":                 true,
					"阳刃格":                 true,
				}[paipanAll.Geju]
			}()
		)
		if (riyuan == "身弱" || riyuan == "偏弱") && isNormalGeju {
			ability = "较弱"
			recommendation = "建议您在实现事业与财富追求的过程中，注重健康与心态，提升个人能力与气场，合理把握身边的平台、人脉等资源；合理分配已获取的财务，如配偶子女等，同时可考虑存款转化为产业、房产或者无形的资产;在面对机会时，可以通过听取多方建议，提升出击的把握和信心。"
		} else if riyuan == "身强" || riyuan == "偏强" {
			ability = "较强"
			recommendation = "建议您在实现事业与财富追求的过程中，加强个人心态建设及能力提升，合理把握可用资源；面对机遇时，综合考虑、当机立断地迅抓住机遇，让事业更上一层楼。此外也要克服自大、武断等心理，避免因人际因素而受到损失。"
		} else if riyuan == "从强" || riyuan == "从弱" {
			ability = "极强"
			recommendation = "建议您在实现事业与财富追求的过程中，加强个人心态建设与能力提升，同时避免因财富造成其他人际关系、健康方面的问题；遇到合适的时机，综合考虑，当机立断地迅抓住机遇，让事业更上一层楼。此外也要避免刚愎自用、武断等心理，避免因人际因素而受到损失。"
		} else if riyuan == "平和" {
			ability = "适中"
			recommendation = "建议您在实现事业与财富追求过程中，追求财富的同事需要注意个人身体健康，适当学习了解相关理财知识，主要以稳定、正规的渠道（如工资、租金等）获取财富,可在承受范围呢尝试低风险投资。此外，不同时期类似的机遇风险，也有所不同，需要结合时宜具体分析，切勿投机取巧，追求暴富而冒险，以防财来财去。"
		} else if !isNormalGeju {
			ability = "极强"
			recommendation = "建议您在实现事业与财富追求的过程中，加强个人心态建设与能力提升，同时避免因财富造成其他人际关系、健康方面的问题；遇到合适的时机，综合考虑，当机立断地迅抓住机遇，让事业更上一层楼。此外也要避免刚愎自用、武断等心理，避免因人际因素而受到损失。"
		}
		result.Dancai = &v1.LuncaiDancai{
			Dangyi:         paipanBaziMap.Data.TongYi,
			DangyiPercent:  paipanBaziMap.Data.TongYiPercent,
			Wangshuai:      riyuan,
			GejuCankao:     paipanAll.Geju,
			Ability:        ability,
			Recommendation: recommendation,
		}
	}
	// 神煞看财运
	{
		result.Shensha = slf.Helper.LuncaiShenshaCaiyun(ctx, paipanAll)
	}
	// 流年运势
	{
		dyStart, dyEnd := paipanAll.DayunQishi, paipanAll.DayunJiezhi
		dyLiunian := strings.Split(paipanAll.StrDayunLiuYear, ",")
		paipanLiunian, err := slf.coronaCli.GetDayunLiunianScore(ctx, &corona.GetDayunLiunianScoreRequest{
			Birthtime: realSunTimeStr,
			Gender:    req.Gender,
		})
		if err != nil {
			return nil, errors.Wrap(err, "paipan.GetDayunLiulianScore")
		}
		dyScoreList := func() []int {
			end := len(paipanLiunian.Zscore)
			for end > 0 && paipanLiunian.Zscore[end-1] == 0 {
				end--
			}
			arr := paipanLiunian.Zscore[:end]
			return arr[len(arr)-120:]
		}()
		result.Dayunliunian = &v1.LuncaiDayunliunian{
			StartYear:  dyStart,
			EndYear:    dyEnd,
			ScoreList:  dyScoreList,
			DayunList:  paipanAll.GetShierDayun,
			GanzhiList: dyLiunian,
			YearList: func() []int {
				var arr []int
				for i := dyStart; i <= dyEnd; i++ {
					arr = append(arr, i)
				}
				return arr
			}(),
		}
	}
	// 创建排盘记录
	if !req.IsReplay && !req.IsExample && !req.IgnoreRecord {
		ret, _ := slf.irs.Query(req.IP)
		if result.ID, err = slf.paipanRecordRepo.CreatePaipanRecord(ctx, &model.PaipanRecord{
			UserID: func() string {
				if req.User != nil {
					return req.User.UserID
				} else {
					return ""
				}
			}(),
			Name:           req.Name,
			Gender:         lo.Ternary(req.Gender == "男", 1, 2),
			Birthtime:      birthtime,
			BirthtimeSun:   birthtimeSun,
			BirthtimeLunar: result.Mingzhu.BirthtimeLunar,
			Bazi:           result.Mingzhu.Bazi,
			Birthplace:     req.Birthplace,
			UserAgent:      req.UserAgent,
			IP:             ret.IP,
			Region:         ret.Region(),
			AppID:          5, // 万年历
			AppPlatformID:  1, // 未知（默认）
			Type:           1,
			SaveTime:       time.Now(),
		}); err != nil {
			return nil, err
		}
	}
	// 根据权限返回数据
	if !isShow {
		result.Nengliang = nil
		result.CareerSuggestion = nil
		result.Belong = nil
		result.CareerPalace = nil
		result.Caiyuan = nil
		result.Risk = nil
		result.Dancai = nil
		result.Shensha = nil
		result.Dayunliunian = nil
	}
	return result, nil
}

// BirthtimeSun 获取真太阳时间
func (slf *luncaiService) realSunTime(ctx context.Context, birthtime time.Time, location []string) (time.Time, error) {
	if len(location) == 0 {
		return birthtime, nil
	}
	join := strings.Join(location, "")
	if len(location) == 3 {
		join = "中国" + join
	}
	offset, err := slf.luncaiRepo.GetOffset4TimeByLocation(ctx, join)
	if err != nil {
		return time.Time{}, err
	}
	return birthtime.Add(time.Duration(offset) * time.Minute), nil
}
