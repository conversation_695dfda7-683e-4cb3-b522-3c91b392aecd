package service

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"regexp"
	"strconv"
	"strings"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/constants"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/internal/types"
	"zodiacus/pkg/array"
	"zodiacus/pkg/strs"
	"zodiacus/third_party/corona"
)

type DateService interface {
	Ji<PERSON>yun(ctx context.Context, req *v1.GetJiaoyunRequest) (*v1.GetJiaoyunResponseData, error)
	Day(ctx context.Context, req *v1.CalendarDayRequest) (*v1.CalendarDayResponseData, error)
	Day4VIP(ctx context.Context, req *v1.CalendarDayRequest) (*v1.CalendarDayResponseData, error)
	Month(ctx context.Context, req *v1.CalendarMonthRequest) (*v1.CalendarMonthResponseData, error)
	Month4VIP(ctx context.Context, req *v1.CalendarMonthRequest) (*v1.CalendarMonthResponseData, error)
	DayDoWhat(ctx context.Context, req *v1.CalendarDayDoWhatRequest) (*v1.CalendarDayDoWhatResponseData, error)
}

func NewDateService(
	service *Service,
	calendarRepo repository.DateRepository,
	enumRepo repository.EnumsRepository,
	mingliRepo repository.UserMingliRepository,
	mingliGroupRepo repository.UserMingliGroupRepository,
) DateService {
	return &dateService{
		calendarRepo:    calendarRepo,
		enumRepo:        enumRepo,
		mingliRepo:      mingliRepo,
		mingliGroupRepo: mingliGroupRepo,
		Service:         service,
		huangdao:        map[string]bool{"青龙": true, "明堂": true, "金匮": true, "天德": true, "玉堂": true, "司命": true},
		heidao:          map[string]bool{"天刑": true, "朱雀": true, "白虎": true, "天牢": true, "玄武": true, "勾陈": true},
		tianganXiangke: map[string]bool{
			"甲戊": true,
			"乙己": true,
			"丙庚": true,
			"丁辛": true,
			"戊壬": true,
			"己癸": true,
			"庚甲": true,
			"辛乙": true,
			"壬丙": true,
			"癸丁": true,
		},
		tianganWuheMap: map[string]bool{
			"甲己": true,
			"乙庚": true,
			"丙辛": true,
			"丁壬": true,
			"戊癸": true,
		},
		tianganXiangchongMap: map[string]bool{
			"甲庚": true,
			"乙辛": true,
			"丙壬": true,
			"丁癸": true,
		},
		dizhiXiangChong: map[string]bool{
			"子午": true,
			"丑未": true,
			"寅申": true,
			"卯酉": true,
			"辰戌": true,
			"巳亥": true,
		},
		yi2what: map[string]string{
			"乘船":       "",
			"乘船渡水":     "",
			"买车交车":     "买车、交车",
			"交易":       "购物",
			"仓库":       "",
			"伐木":       "",
			"伐术":       "",
			"修仓库":      "收拾衣柜杂物",
			"修置产室":     "",
			"修置产室补垣塞穴": "",
			"修置垣墙":     "",
			"修路":       "",
			"修造":       "",
			"修造动土":     "",
			"入宅":       "搬新家",
			"入职":       "入职",
			"出仓库":      "",
			"出货":       "出货",
			"动土":       "",
			"发货放款":     "",
			"发货放款修置产室": "",
			"启攒":       "",
			"坏垣":       "",
			"大事勿用":     "不适宜进行重要活动或者大的变动",
			"安床":       "铺床、更换床单",
			"安硙":       "",
			"安葬":       "",
			"安装机械":     "电脑、设备安装",
			"开业":       "",
			"开井渠":      "",
			"开仓库":      "",
			"开渠":       "",
			"开渠穿井":     "",
			"成人礼":      "",
			"打井":       "",
			"打扫":       "打扫",
			"打扫房屋":     "打扫房屋",
			"捕鱼":       "钓鱼",
			"探亲访友":     "探亲访友",
			"提案":       "提案、计划或建议",
			"搬家":       "搬家",
			"收养":       "领养宠物",
			"旅游出行":     "旅游出行",
			"日值四废":     "不宜进行重要的事情",
			"日值四离":     "不适合进行婚礼",
			"日值四绝":     "不宜进行重要的事情",
			"杨公忌日":     "",
			"栽种":       "养花种菜",
			"求医":       "看病",
			"求医疗病":     "看病",
			"求嗣":       "求子、创造人类",
			"求职应聘":     "求职应聘",
			"沐浴":       "沐浴、足疗",
			"添丁":       "添加新的家庭成员（宝宝、宠物）",
			"添家电":      "添家电",
			"渡水":       "过河、过海",
			"牧养":       "饲养、养育动物",
			"牧畜":       "饲养、养育动物",
			"理发":       "理发",
			"疗目":       "治疗眼镜、配镜",
			"相亲":       "相亲",
			"破屋":       "拆迁",
			"破屋坏垣":     "装修砸墙",
			"祈祷":       "祈福",
			"祈福":       "祈福",
			"祭祀":       "祭祀",
			"竖柱上梁":     "",
			"筑堤防":      "",
			"纳奋":       "置办床上用品",
			"纳畜":       "购买或领养动物、养鱼",
			"纳蓄":       "囤货",
			"纳财":       "存钱",
			"结婚":       "",
			"美甲浴足":     "美甲浴足",
			"补围墙":      "",
			"补围墙修墙":    "",
			"补围墙墓穴":    "",
			"补坦塞穴":     "",
			"补垣塞穴":     "",
			"装修":       "装修、装饰",
			"视察":       "视察工作",
			"订合约":      "签订合约、合同等法律文件",
			"订婚":       "",
			"诸事不宜":     "不适合进行重要的事务或活动",
			"返家":       "回家",
			"逛街购衣":     "逛街购衣",
			"酿造":       "",
			"酿造开业":     "",
			"针灸":       "针灸养身",
			"针灸筑堤防":    "",
			"阴宅破土":     "",
			"除虫":       "除虫",
			"领证":       "领证",
		},
		tiangan: map[string]*types.Tiangan{
			"甲": {Name: "甲", Yinyang: "阳", Wuxing: "木"},
			"乙": {Name: "乙", Yinyang: "阴", Wuxing: "木"},
			"丙": {Name: "丙", Yinyang: "阳", Wuxing: "火"},
			"丁": {Name: "丁", Yinyang: "阴", Wuxing: "火"},
			"戊": {Name: "戊", Yinyang: "阳", Wuxing: "土"},
			"己": {Name: "己", Yinyang: "阴", Wuxing: "土"},
			"庚": {Name: "庚", Yinyang: "阳", Wuxing: "金"},
			"辛": {Name: "辛", Yinyang: "阴", Wuxing: "金"},
			"壬": {Name: "壬", Yinyang: "阳", Wuxing: "水"},
			"癸": {Name: "癸", Yinyang: "阴", Wuxing: "水"},
		},
		dizhi: map[string]*types.Dizhi{
			"子": {Name: "子", Yinyang: "阳", Wuxing: "水"},
			"丑": {Name: "丑", Yinyang: "阴", Wuxing: "土"},
			"寅": {Name: "寅", Yinyang: "阳", Wuxing: "木"},
			"卯": {Name: "卯", Yinyang: "阴", Wuxing: "木"},
			"辰": {Name: "辰", Yinyang: "阳", Wuxing: "土"},
			"巳": {Name: "巳", Yinyang: "阴", Wuxing: "火"},
			"午": {Name: "午", Yinyang: "阳", Wuxing: "火"},
			"未": {Name: "未", Yinyang: "阴", Wuxing: "土"},
			"申": {Name: "申", Yinyang: "阳", Wuxing: "金"},
			"酉": {Name: "酉", Yinyang: "阴", Wuxing: "金"},
			"戌": {Name: "戌", Yinyang: "阳", Wuxing: "土"},
			"亥": {Name: "亥", Yinyang: "阴", Wuxing: "水"},
		},
		wuxingXiangsheng: map[string]string{
			"木": "火",
			"火": "土",
			"土": "金",
			"金": "水",
			"水": "木",
		},
		wuxingXiangke: map[string]string{
			"木": "土",
			"火": "金",
			"土": "水",
			"金": "木",
			"水": "火",
		},
		/*
			日天干	财神方位（玉匣记）	个人偏财时钟方位	个人偏财地理方位	福神	喜神（桃花）	阳贵	文昌
			甲	东北	4、10	东偏南、西偏北	正北	东北	西南	东南
			乙	东北	7、 1	南偏西、北偏东	西南	西北	西南	南
			丙	正西	8	西南	西北	西南	正西	西南
			丁	正西	9	正西	东南	正南	西北	西
			戊	正北	12	正北	东北	东南	东北	西南
			己	正北	11	西北	正北	东北	正北	西
			庚	正东	2	东偏北	西南	西北	东北	西北
			辛	正东	3	正东	西北	西南	东北	北
			壬	正南	5	南偏东	东南	正南	正东	东北
			癸	正南	6	正南	东北	东南	东南	东
		*/
		caifuxishouxi: map[string]*types.CailushouxiLocation{
			"甲": {Cai: "东北", Fu: "正北", Xi: "东北", Yanggui: "西南", Wenchang: "东南", PianCai: []string{"东偏南", "西偏北"}, PianCaiClock: []int{4, 10}},
			"乙": {Cai: "东北", Fu: "西南", Xi: "西北", Yanggui: "西南", Wenchang: "南", PianCai: []string{"南偏西", "北偏东"}, PianCaiClock: []int{7, 1}},
			"丙": {Cai: "正西", Fu: "西北", Xi: "西南", Yanggui: "正西", Wenchang: "西南", PianCai: []string{"西南"}, PianCaiClock: []int{8}},
			"丁": {Cai: "正西", Fu: "东南", Xi: "正南", Yanggui: "西北", Wenchang: "西", PianCai: []string{"正西"}, PianCaiClock: []int{9}},
			"戊": {Cai: "正北", Fu: "东北", Xi: "东南", Yanggui: "东北", Wenchang: "西南", PianCai: []string{"正北"}, PianCaiClock: []int{12}},
			"己": {Cai: "正北", Fu: "正北", Xi: "东北", Yanggui: "正北", Wenchang: "西", PianCai: []string{"西北"}, PianCaiClock: []int{11}},
			"庚": {Cai: "正东", Fu: "西南", Xi: "西北", Yanggui: "东北", Wenchang: "西北", PianCai: []string{"东偏北"}, PianCaiClock: []int{2}},
			"辛": {Cai: "正东", Fu: "西北", Xi: "西南", Yanggui: "东北", Wenchang: "北", PianCai: []string{"正东"}, PianCaiClock: []int{3}},
			"壬": {Cai: "正南", Fu: "东南", Xi: "正南", Yanggui: "正东", Wenchang: "东北", PianCai: []string{"南偏东"}, PianCaiClock: []int{5}},
			"癸": {Cai: "正南", Fu: "东北", Xi: "东南", Yanggui: "东南", Wenchang: "东", PianCai: []string{"正南"}, PianCaiClock: []int{6}},
		},
		cangganPower: map[string]map[string]int{
			"子": {"癸": 100},
			"丑": {"己": 60, "癸": 30, "辛": 10},
			"寅": {"甲": 60, "丙": 30, "戊": 10},
			"卯": {"乙": 100},
			"辰": {"戊": 60, "乙": 30, "癸": 10},
			"巳": {"丙": 60, "庚": 30, "戊": 10},
			"午": {"丁": 70, "己": 30},
			"未": {"己": 60, "丁": 30, "乙": 10},
			"申": {"庚": 60, "壬": 30, "戊": 10},
			"酉": {"辛": 100},
			"戌": {"戊": 60, "辛": 30, "丁": 10},
			"亥": {"壬": 70, "甲": 30},
		},
		wuxingRelation: map[string]*types.WuxingRelationship{
			"木": {
				Bijie:    "木",
				Shishang: "火",
				Caicai:   "土",
				Guansha:  "金",
				Yinxiao:  "水",
			},
			"火": {
				Bijie:    "火",
				Shishang: "土",
				Caicai:   "金",
				Guansha:  "水",
				Yinxiao:  "木",
			},
			"土": {
				Bijie:    "土",
				Shishang: "金",
				Caicai:   "水",
				Guansha:  "木",
				Yinxiao:  "火",
			},
			"金": {
				Bijie:    "金",
				Shishang: "水",
				Caicai:   "木",
				Guansha:  "火",
				Yinxiao:  "土",
			},
			"水": {
				Bijie:    "水",
				Shishang: "木",
				Caicai:   "火",
				Guansha:  "土",
				Yinxiao:  "金",
			},
		},
		dizhiWuxingCoefficient: map[string]map[string]float32{
			"子": {"木": 1.414, "火": 0.500, "土": 0.707, "金": 1.000, "水": 2.000},
			"丑": {"木": 0.898, "火": 0.821, "土": 1.512, "金": 1.348, "水": 1.041},
			"寅": {"木": 1.571, "火": 1.548, "土": 0.924, "金": 0.716, "水": 0.862},
			"卯": {"木": 2.000, "火": 1.414, "土": 0.500, "金": 0.707, "水": 1.000},
			"辰": {"木": 1.166, "火": 1.074, "土": 1.421, "金": 1.161, "水": 0.800},
			"巳": {"木": 0.862, "火": 1.571, "土": 1.548, "金": 0.924, "水": 0.716},
			"午": {"木": 0.912, "火": 1.700, "土": 1.590, "金": 0.774, "水": 0.645},
			"未": {"木": 0.924, "火": 1.341, "土": 1.674, "金": 1.069, "水": 0.612},
			"申": {"木": 0.795, "火": 0.674, "土": 1.012, "金": 1.641, "水": 1.498},
			"酉": {"木": 0.500, "火": 0.707, "土": 1.000, "金": 2.000, "水": 1.414},
			"戌": {"木": 0.674, "火": 1.012, "土": 1.641, "金": 1.498, "水": 0.795},
			"亥": {"木": 1.590, "火": 0.774, "土": 0.645, "金": 0.912, "水": 1.700},
		},
	}
}

type dateService struct {
	calendarRepo           repository.DateRepository
	mingliRepo             repository.UserMingliRepository
	mingliGroupRepo        repository.UserMingliGroupRepository
	enumRepo               repository.EnumsRepository
	huangdao               map[string]bool
	heidao                 map[string]bool
	tianganXiangke         map[string]bool
	tianganWuheMap         map[string]bool
	tianganXiangchongMap   map[string]bool
	dizhiXiangChong        map[string]bool
	yi2what                map[string]string
	tiangan                map[string]*types.Tiangan
	dizhi                  map[string]*types.Dizhi
	wuxingXiangsheng       map[string]string
	wuxingXiangke          map[string]string
	caifuxishouxi          map[string]*types.CailushouxiLocation
	cangganPower           map[string]map[string]int
	dizhiWuxingCoefficient map[string]map[string]float32
	wuxingRelation         map[string]*types.WuxingRelationship
	*Service
}

func (slf *dateService) Jiaoyun(ctx context.Context, req *v1.GetJiaoyunRequest) (*v1.GetJiaoyunResponseData, error) {
	if req.Birthplace == nil {
		req.Birthplace = []string{}
	}
	birthtime, err := time.Parse("2006-01-02 15:04:05", req.Birthtime)
	if err != nil {
		return nil, errors.Wrap(err, "time.Parse")
	}
	birthtimeSun, err := slf.realSunTime(ctx, birthtime, req.Birthplace)
	if err != nil {
		return nil, err
	}
	realSunTimeStr := birthtimeSun.Format("2006-01-02 15:04:05")
	resp, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: realSunTimeStr,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, err
	}
	return &v1.GetJiaoyunResponseData{JiaoyunTime: resp.RetJyDate}, nil
}

func (slf *dateService) DayDoWhat(ctx context.Context, req *v1.CalendarDayDoWhatRequest) (*v1.CalendarDayDoWhatResponseData, error) {
	var (
		err    error
		mingli *model.UserMingli
	)
	if req.MingliID != nil {
		mingli, err = slf.mingliRepo.GetMingliByID(ctx, req.User.UserID, *req.MingliID)
	} else {
		// 查询默认分组
		group, err := slf.mingliGroupRepo.GetDefaultGroup(ctx, req.User.UserID, req.AppID)
		if err != nil {
			return nil, err
		}
		if group == nil {
			return nil, v1.ErrNotFound
		}
		// 查询默认命例
		mingli, err = slf.mingliRepo.GetDefaultMingli(ctx, req.User.UserID, req.AppID, group.ID)
		if err != nil {
			return nil, err
		}
	}
	if mingli == nil {
		return nil, v1.ErrUserMingliNotFound
	}

	// 当前时间
	datetime, err := time.Parse(time.DateTime, req.Time)
	if err != nil {
		return nil, err
	}
	day, err := slf.calendarRepo.GetOneDay(ctx, datetime.Format("2006-01-02"))
	if err != nil {
		return nil, err
	}

	// 读取缓存
	{
		var (
			res v1.CalendarDayDoWhatResponseData
			key = fmt.Sprintf("zodiacus:calendar:dowhat:%s_%s_%s", mingli.Birthtime.Format(time.RFC3339), lo.Ternary(mingli.Gender == 1, "男", "女"), datetime.Format("2006-01-02"))
		)
		cache, err := slf.calendarRepo.GetDayDoWhatCache(ctx, key, &res)
		if err == nil && cache {
			return &res, nil
		}
		if err != nil {
			slf.logger.WithContext(ctx).Error("GetDayDoWhatCache", zap.Error(err))
		}
	}

	// 是否交运
	respAll, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: mingli.Birthtime.Format("2006-01-02 15:04:05"),
		Gender:    lo.Ternary(mingli.Gender == 1, "男", "女"),
	})
	if err != nil {
		return nil, err
	}
	jyTimeStr := respAll.RetJyDate
	jyTime, err := time.Parse("2006-01-02 15:04:05", jyTimeStr)
	if err != nil {
		return nil, err
	}
	hasJy := time.Now().After(jyTime)

	var (
		res = v1.CalendarDayDoWhatResponseData{
			JiaoyunTime: jyTimeStr,
			HasJiaoyun:  hasJy,
			LunarDay:    day.LunarYear,
		}
	)
	jiuzhu, err := slf.coronaCli.LifeCyclesMonth(ctx, &corona.LifeCyclesMonthRequest{
		Birthday: mingli.Birthtime.Format("2006-01-02 15:04:05"),
		Location: []string{},
		Gender:   mingli.Gender,
		Now:      time.Now().Format("2006-01-02 15:04:05"),
	})
	if err != nil {
		return nil, err
	}
	// 查询大运、流年、流月、流日
	var (
		nianGan, nianZhi       = jiuzhu.Tiangan[0], jiuzhu.Dizhi[0]
		yueGan, yueZhi         = jiuzhu.Tiangan[1], jiuzhu.Dizhi[1]
		riGan, riZhi           = jiuzhu.Tiangan[2], jiuzhu.Dizhi[2]
		shiGan, shiZhi         = jiuzhu.Tiangan[3], jiuzhu.Dizhi[3]
		dayunGan, dayunZhi     = jiuzhu.Tiangan[4], jiuzhu.Dizhi[4]
		liunianGan, liunianZhi = jiuzhu.Tiangan[5], jiuzhu.Dizhi[5]
		liuyueGan, liuyueZhi   = jiuzhu.Tiangan[6], jiuzhu.Dizhi[6]
		liuriGan, liuriZhi     = jiuzhu.Tiangan[7], jiuzhu.Dizhi[7]
	)

	// 1.1 每日综合运势分数
	{
		scoreList, err := slf.coronaCli.GetSolarMonthScore(ctx, &corona.GetSolarMonthScoreRequest{
			Birthtime:   mingli.Birthtime.Format("2006-01-02 15:04:05"),
			Gender:      lo.Ternary(mingli.Gender == 1, "男", "女"),
			CurrentTime: req.Time,
		})
		if err != nil {
			return nil, err
		}
		dayScore := scoreList.DayFenSolar[datetime.Day()-1]
		// 今日运势分=每日综合得分*0.5+50
		res.FateScore = float64(dayScore)*0.5 + 50
	}

	// 1.2 每日专项分数计算
	{
		// 1.2.1 每日能量值计算
		fenye, err := slf.coronaCli.GetRenyuanFenye(ctx, &corona.GetRenyuanFenyeRequest{Datetime: req.Time})
		if err != nil {
			return nil, err
		}
		// ===========================================
		yjTianganPower := make(map[string]float32) // 原局天干能量
		for _, s := range []string{nianGan, yueGan, riGan, shiGan} {
			yjTianganPower[s] += 100
		}
		yjCangganPower := make(map[string]float32) // 原局藏干能量
		for _, s := range []string{nianZhi, yueZhi, riZhi, shiZhi} {
			for s2, i := range slf.cangganPower[s] {
				yjCangganPower[s2] += float32(i)
			}
		}
		tianganPower := map[string]float32{
			"甲": 0,
			"乙": 0,
			"丙": 0,
			"丁": 0,
			"戊": 0,
			"己": 0,
			"庚": 0,
			"辛": 0,
			"壬": 0,
			"癸": 0,
		}
		for s := range tianganPower {
			tianganPower[s] = yjTianganPower[s] + yjCangganPower[s]
		}
		lnDizhiWuxing := slf.dizhi[liunianZhi].Wuxing
		coefficient := slf.dizhiWuxingCoefficient[liunianZhi]
		relation := slf.wuxingRelation[lnDizhiWuxing]
		for s := range tianganPower {
			wuxing := slf.tiangan[s].Wuxing
			switch wuxing {
			case relation.Bijie:
				tianganPower[s] *= 2
			case relation.Shishang:
				tianganPower[s] *= 1.414
			case relation.Caicai:
				tianganPower[s] *= 0.5
			case relation.Guansha:
				tianganPower[s] *= 0.707
			case relation.Yinxiao:
				tianganPower[s] *= 1
			default:
			}
			tianganPower[s] *= coefficient[wuxing]
		}
		siling := strings.Split(fenye.Fenye, "")[0]
		wuxingMapPower := map[string]float32{
			"木": 0,
			"火": 0,
			"土": 0,
			"金": 0,
			"水": 0,
		}
		res.TenTianganPower = make(map[string]float32)
		res.TenGodPower = make(map[string]float32)
		for tiangan, power := range tianganPower {
			if tiangan == siling {
				power += 20
			}
			res.TenTianganPower[tiangan] = power
			wuxingMapPower[slf.tiangan[tiangan].Wuxing] += power
			ssName := ""
			for _, item := range respAll.ShishenPower {
				for i, s := range item.TianganArr {
					if s == tiangan {
						ssName = item.ShiShenArr[i]
						break
					}
				}
			}
			res.TenGodPower[ssName] = power
		}
		// 1.2.2 每日财运分数
		var (
			liuriCaicaiPower   = float64(res.TenGodPower["正财"] + res.TenGodPower["偏财"])
			liuriShishangPower = float64(res.TenGodPower["食神"] + res.TenGodPower["伤官"])
			liuriBijiePower    = float64(res.TenGodPower["比肩"] + res.TenGodPower["劫财"])
			liuriGuanshaPower  = float64(res.TenGodPower["正官"] + res.TenGodPower["七杀"])
			liuriYinxiaoPower  = float64(res.TenGodPower["正印"] + res.TenGodPower["偏印"])
			liuriTotalPower    = liuriCaicaiPower + liuriShishangPower + liuriBijiePower + liuriGuanshaPower + liuriYinxiaoPower
		)
		if hasJy {
			res.PropertyScore = (((liuriCaicaiPower*0.6+liuriShishangPower*0.3+liuriBijiePower*0.1-liuriGuanshaPower*0.1-liuriYinxiaoPower*0.3)/
				(liuriCaicaiPower*0.6+liuriShishangPower*0.3+liuriBijiePower*0.1+liuriGuanshaPower*0.1+liuriYinxiaoPower*0.3))*50+50)*0.35 + res.FateScore*0.65
		}
		// 1.2.3 每日桃花分
		var (
			indicator1 float64
			indicator2 float64
			indicator3 float64
			indicator4 float64
		)
		{
			shishenArr := array.Merge(jiuzhu.ZhuXing[4:], jiuzhu.BenqiShiShen[4:], jiuzhu.ZhongqiShiShen[4:], jiuzhu.YuqiShiShen[4:])
			indicator1 = lo.Ternary(mingli.Gender == 1,
				max(liuriCaicaiPower/liuriTotalPower, float64(array.Count(shishenArr, "正财")+array.Count(shishenArr, "偏财"))/8),
				max(liuriGuanshaPower/liuriTotalPower, float64(array.Count(shishenArr, "正官")+array.Count(shishenArr, "七杀"))/8))
		}
		{
			var (
				score float64
			)
			if slf.tianganWuheMap[riGan+dayunGan] || slf.tianganWuheMap[dayunGan+riGan] {
				score += 10
			}
			if slf.tianganWuheMap[riGan+liunianGan] || slf.tianganWuheMap[liunianGan+riGan] {
				score += 10
			}
			if slf.tianganWuheMap[riGan+liuyueGan] || slf.tianganWuheMap[liuyueGan+riGan] {
				score += 10
			}
			if slf.tianganWuheMap[riGan+liuriGan] || slf.tianganWuheMap[liuriGan+riGan] {
				score += 10
			}
			if slf.tianganXiangchongMap[riGan+dayunGan] || slf.tianganXiangchongMap[dayunGan+riGan] {
				score -= 5
			}
			if slf.tianganXiangchongMap[riGan+liunianGan] || slf.tianganXiangchongMap[liunianGan+riGan] {
				score -= 5
			}
			if slf.tianganXiangchongMap[riGan+liuyueGan] || slf.tianganXiangchongMap[liuyueGan+riGan] {
				score -= 5
			}
			if slf.tianganXiangchongMap[riGan+liuriGan] || slf.tianganXiangchongMap[liuriGan+riGan] {
				score -= 5
			}
			indicator2 = score / 40
		}
		{
			// 六合、三合半合、三合拱合、三合暗合、三刑、六冲、六害
			var (
				score float64
			)
			liuheMap := map[string]bool{
				"子丑": true,
				"寅亥": true,
				"卯戌": true,
				"辰酉": true,
				"巳申": true,
				"午未": true,
			}
			sanhebanheMap := map[string]bool{
				"申子": true,
				"寅午": true,
				"巳酉": true,
				"亥卯": true,
				"子辰": true,
				"午戌": true,
				"酉丑": true,
				"卯未": true,
			}
			sanhegongheMap := map[string]bool{
				"申辰": true,
				"寅戌": true,
				"巳丑": true,
				"亥未": true,
			}
			sanheanheMap := map[string]bool{
				"申辰": true,
				"亥未": true,
				"寅戌": true,
				"巳丑": true,
			}
			sanxing := map[string]bool{
				"寅巳申": true,
				"丑戌未": true,
			}
			liuchong := map[string]bool{
				"子午": true,
				"丑未": true,
				"寅申": true,
				"卯酉": true,
				"辰戌": true,
				"巳亥": true,
			}
			liuhai := map[string]bool{
				"子未": true,
				"丑午": true,
				"寅巳": true,
				"卯辰": true,
				"申亥": true,
				"酉戌": true,
			}
			if liuheMap[riZhi+dayunZhi] || liuheMap[dayunZhi+riZhi] {
				score += 10
			}
			if liuheMap[riZhi+liunianZhi] || liuheMap[liunianZhi+riZhi] {
				score += 10
			}
			if liuheMap[riZhi+liuyueZhi] || liuheMap[liuyueZhi+riZhi] {
				score += 10
			}
			if liuheMap[riZhi+liuriZhi] || liuheMap[liuriZhi+riZhi] {
				score += 10
			}
			if sanhebanheMap[riZhi+dayunZhi] || sanhebanheMap[dayunZhi+riZhi] {
				score += 7
			}
			if sanhebanheMap[riZhi+liunianZhi] || sanhebanheMap[liunianZhi+riZhi] {
				score += 7
			}
			if sanhebanheMap[riZhi+liuyueZhi] || sanhebanheMap[liuyueZhi+riZhi] {
				score += 7
			}
			if sanhebanheMap[riZhi+liuriZhi] || sanhebanheMap[liuriZhi+riZhi] {
				score += 7
			}
			if sanhegongheMap[riZhi+dayunZhi] || sanhegongheMap[dayunZhi+riZhi] {
				score += 3
			}
			if sanhegongheMap[riZhi+liunianZhi] || sanhegongheMap[liunianZhi+riZhi] {
				score += 3
			}
			if sanhegongheMap[riZhi+liuyueZhi] || sanhegongheMap[liuyueZhi+riZhi] {
				score += 3
			}
			if sanhegongheMap[riZhi+liuriZhi] || sanhegongheMap[liuriZhi+riZhi] {
				score += 3
			}
			if sanheanheMap[riZhi+dayunZhi] || sanheanheMap[dayunZhi+riZhi] {
				score += 2
			}
			if sanheanheMap[riZhi+liunianZhi] || sanheanheMap[liunianZhi+riZhi] {
				score += 2
			}
			if sanheanheMap[riZhi+liuyueZhi] || sanheanheMap[liuyueZhi+riZhi] {
				score += 2
			}
			if sanheanheMap[riZhi+liuriZhi] || sanheanheMap[liuriZhi+riZhi] {
				score += 2
			}
			var sanxingFn = func(zhi1, zhi2 string) bool {
				for s := range sanxing {
					if strings.Contains(s, zhi1) && strings.Contains(s, zhi2) {
						return true
					}
				}
				return false
			}
			if sanxingFn(riZhi, dayunZhi) {
				score -= 10
			}
			if sanxingFn(riZhi, liunianZhi) {
				score -= 10
			}
			if sanxingFn(riZhi, liuyueZhi) {
				score -= 10
			}
			if sanxingFn(riZhi, liuriZhi) {
				score -= 10
			}
			if liuchong[riZhi+dayunZhi] || liuchong[dayunZhi+riZhi] {
				score -= 7
			}
			if liuchong[riZhi+liunianZhi] || liuchong[liunianZhi+riZhi] {
				score -= 7
			}
			if liuchong[riZhi+liuyueZhi] || liuchong[liuyueZhi+riZhi] {
				score -= 7
			}
			if liuchong[riZhi+liuriZhi] || liuchong[liuriZhi+riZhi] {
				score -= 7
			}
			if liuhai[riZhi+dayunZhi] || liuhai[dayunZhi+riZhi] {
				score -= 2
			}
			if liuhai[riZhi+liunianZhi] || liuhai[liunianZhi+riZhi] {
				score -= 2
			}
			if liuhai[riZhi+liuyueZhi] || liuhai[liuyueZhi+riZhi] {
				score -= 2
			}
			if liuhai[riZhi+liuriZhi] || liuhai[liuriZhi+riZhi] {
				score -= 2
			}
			indicator3 = score / 40
		}
		{
			var list []string
			list = append(list, jiuzhu.ShenShaJiShen[constants.PaipanIndexDayun]...)
			list = append(list, jiuzhu.ShenShaJiShen[constants.PaipanIndexLiuNian]...)
			list = append(list, jiuzhu.ShenShaJiShen[constants.PaipanIndexLiuYue]...)
			list = append(list, jiuzhu.ShenShaJiShen[constants.PaipanIndexLiuRi]...)
			var count int
			for _, s := range list {
				if s == "桃花" || s == "红鸢" || s == "红艳" || s == "天喜" {
					count++
				}
			}
			indicator4 = float64(count) / 10
			if indicator4 > 1 {
				indicator4 = 1
			}
		}
		res.LoveScore = 20*indicator1 + 10*indicator2 + 10*indicator3 + (40-20*indicator1-10*indicator2-10*indicator3)*indicator4
		if res.LoveScore > 40 {
			res.LoveScore = 40
		}
		res.LoveScore += res.FateScore * 0.6
		// 1.2.4 每日健康分
		// 补不足指数
		var indicator5 float64
		wuxingMapPower4Mingju := make(map[string]float32)
		powerList4Mingju := strings.Split(respAll.SaveLiliangNum, ",")
		for i, item := range strings.Split(respAll.SaveLiliangWuXingMingzi, ",") {
			power, _ := strconv.Atoi(powerList4Mingju[i])
			wuxingMapPower4Mingju[item] = float32(power)
		}
		countSet := make(map[string]float32)
		for k, v := range wuxingMapPower4Mingju {
			if v < 50 {
				countSet[k] = v
			}
		}
		var (
			count1 = len(countSet)
			count2 int
		)
		for k, v := range countSet {
			if v+wuxingMapPower[k] >= 90 {
				count2 += 1
			}
		}
		if count1 == 0 {
			indicator5 = 1
		} else {
			indicator5 = float64(count2) / float64(count1)
		}
		// 五行平衡指数
		var indicator6 float64
		var mjTotalPower float32
		for _, i := range wuxingMapPower4Mingju {
			mjTotalPower += i
		}
		for _, i := range wuxingMapPower4Mingju {
			percent := float64(i) / float64(mjTotalPower)
			if percent < 0.15 {
				percent -= 0.15
				indicator6 += percent * percent
			} else if percent > 0.25 {
				percent -= 0.25
				indicator6 += percent * percent
			}
		}
		// 流日平衡指数F
		var (
			indicator7 float64
			temp7      float64
		)
		wuxingMapPower4Mixed := make(map[string]float32)
		for s, i := range wuxingMapPower4Mingju {
			wuxingMapPower4Mixed[s] = i + wuxingMapPower[s]
		}
		var mixedTotalPower float32
		for _, i := range wuxingMapPower4Mixed {
			mixedTotalPower += i
		}
		for _, i := range wuxingMapPower4Mixed {
			percent := float64(i) / float64(mixedTotalPower)
			if percent < 0.15 {
				percent -= 0.15
				temp7 += percent * percent
			} else if percent > 0.25 {
				percent -= 0.25
				temp7 += percent * percent
			}
		}
		if temp7 == 0 && indicator6 == 0 {
			indicator7 = 1
		} else if temp7 != 0 && indicator6 == 0 {
			indicator7 = -temp7
		} else {
			indicator7 = (indicator6 - temp7) / indicator6
			if indicator7 < -1 {
				indicator7 = -1
			}
		}
		res.HealthScore = (indicator5*20 + indicator7*10 + 10) + res.FateScore*0.6
		if slf.tianganXiangke[dayunGan+riGan] || slf.tianganXiangke[riGan+dayunGan] {
			res.HealthScore -= 10
		}
		if slf.tianganXiangke[liunianGan+riGan] || slf.tianganXiangke[riGan+liunianGan] {
			res.HealthScore -= 10
		}
		if slf.tianganXiangke[liuyueGan+riGan] || slf.tianganXiangke[riGan+liuyueGan] {
			res.HealthScore -= 10
		}
		if slf.tianganXiangke[liuriGan+riGan] || slf.tianganXiangke[riGan+liuriGan] {
			res.HealthScore -= 10
		}
		if slf.dizhiXiangChong[dayunZhi+riZhi] || slf.dizhiXiangChong[riZhi+dayunZhi] {
			res.HealthScore -= 10
		}
		if slf.dizhiXiangChong[liunianZhi+riZhi] || slf.dizhiXiangChong[riZhi+liunianZhi] {
			res.HealthScore -= 10
		}
		if slf.dizhiXiangChong[liuyueZhi+riZhi] || slf.dizhiXiangChong[riZhi+liuyueZhi] {
			res.HealthScore -= 10
		}
		if slf.dizhiXiangChong[liuriZhi+riZhi] || slf.dizhiXiangChong[riZhi+liuriZhi] {
			res.HealthScore -= 10
		}
		if res.HealthScore < 40 {
			res.HealthWarning = true
		}
	}
	// 1.3 每日建议
	{
		res.Zhishen = day.Zhishen
	}
	// 1.4 每日宜忌
	{

		yi := strings.Split(day.Yi, " ")
		yiWhatSet := make(map[string]bool)
		for _, item := range yi {
			what := slf.yi2what[item]
			if what != "" {
				yiWhatSet[what] = true
			}
		}
		for what := range yiWhatSet {
			res.YiWhat = append(res.YiWhat, what)
		}
		ji := strings.Split(day.Ji, " ")
		jiWhatSet := make(map[string]bool)
		for _, item := range ji {
			what := slf.yi2what[item]
			if what != "" {
				jiWhatSet[what] = true
			}
		}
		for what := range jiWhatSet {
			res.JiWhat = append(res.JiWhat, what)
		}
	}
	// 1.5 每日穿衣
	{
		liuriDizhiWuxing := slf.dizhi[liuriZhi].Wuxing
		good := []string{slf.wuxingXiangsheng[liuriDizhiWuxing], func() string {
			for s1, s2 := range slf.wuxingXiangke {
				if s2 == liuriDizhiWuxing {
					return s1
				}
			}
			return ""
		}()}
		chou, ji := mingli.Wuxing[2], mingli.Wuxing[3]
		res.Dressing = func() []string {
			var list []string
			for _, s := range good {
				if s == chou || s == ji {
					continue
				} else {
					list = append(list, s)
				}
			}
			return list
		}()
	}
	// 1.6 每日幸运数组
	{
		res.LuckyNum = func() int {
			return func() []int {
				switch func() string {
					ss := slf.wuxingXiangsheng[slf.dizhi[liuriZhi].Wuxing]
					if ss == mingli.Wuxing[0] || ss == mingli.Wuxing[1] {
						return ss
					}
					if slf.dizhi[liuriZhi].Wuxing == mingli.Wuxing[0] || slf.dizhi[liuriZhi].Wuxing == mingli.Wuxing[1] {
						return slf.dizhi[liuriZhi].Wuxing
					}
					return slf.wuxingXiangke[slf.dizhi[liuriZhi].Wuxing]
				}() {
				case "木":
					return []int{3, 8}
				case "火":
					return []int{2, 7}
				case "土":
					return []int{5, 0}
				case "金":
					return []int{4, 9}
				case "水":
					return []int{1, 6}
				default:
					return []int{1, 6}
				}
			}()[datetime.Day()%2]
		}()
	}
	// 1.7 财神方位、桃花方位、偏财位
	{
		l1 := slf.caifuxishouxi[liuriGan]
		l2 := slf.caifuxishouxi[riGan]
		res.PropertyLocation = array.Unique([]string{l1.Cai, l2.Cai})
		res.LoveLocation = array.Unique([]string{l1.Xi, l2.Xi})
		res.PianPropertyLocation = array.Unique(array.Merge(l1.PianCai, l2.PianCai))
		res.PianPropertyClock = array.Unique(array.Merge(l1.PianCaiClock, l2.PianCaiClock))
	}
	// 1.8 每日贵人
	{
		res.Guiren = func() string {
			var all []string
			all = append(all, jiuzhu.ShenShaJiShen[constants.PaipanIndexLiuRi]...)
			all = append(all, jiuzhu.ShenShaJiShen[constants.PaipanIndexLiuYue]...)
			all = append(all, jiuzhu.ShenShaJiShen[constants.PaipanIndexLiuNian]...)
			all = append(all, jiuzhu.ShenShaJiShen[constants.PaipanIndexDayun]...)
			all = append(all, jiuzhu.ShenShaJiShen[constants.PaipanIndexShiZhu]...)
			all = append(all, jiuzhu.ShenShaJiShen[constants.PaipanIndexRiZhu]...)
			all = append(all, jiuzhu.ShenShaJiShen[constants.PaipanIndexYueZhu]...)
			all = append(all, jiuzhu.ShenShaJiShen[constants.PaipanIndexNianZhu]...)
			for _, s := range all {
				switch s {
				case "天乙贵人", "月德贵人", "天德合", "月德合", "福星贵人", "太极贵人", "词馆", "文昌贵人":
					return s
				}
			}
			return ""
		}()
	}

	// 设置缓存
	{
		var (
			key = fmt.Sprintf("zodiacus:calendar:dowhat:%s_%s_%s", mingli.Birthtime.Format(time.RFC3339), lo.Ternary(mingli.Gender == 1, "男", "女"), datetime.Format("2006-01-02"))
		)
		if err = slf.calendarRepo.SetDayDoWhatCache(ctx, key, &res, time.Hour*24); err != nil {
			slf.logger.WithContext(ctx).Error("SetDayDoWhatCache", zap.Error(err))
		}
	}

	return &res, nil
}

func (slf *dateService) Month4VIP(ctx context.Context, req *v1.CalendarMonthRequest) (*v1.CalendarMonthResponseData, error) {
	var (
		err    error
		mingli *model.UserMingli
	)
	if req.User != nil {
		// 查询默认分组
		group, err := slf.mingliGroupRepo.GetDefaultGroup(ctx, req.User.UserID, req.AppID)
		if err != nil {
			return nil, err
		}
		if group == nil {
			return nil, v1.ErrNotFound
		}
		// 查询默认命例
		mingli, err = slf.mingliRepo.GetDefaultMingli(ctx, req.User.UserID, req.AppID, group.ID)
		if err != nil {
			return nil, err
		}
	}
	data, err := slf.Month(ctx, &v1.CalendarMonthRequest{
		Month: req.Month,
	})
	if err != nil {
		return nil, err
	}
	var res v1.CalendarMonthResponseData
	if mingli != nil {
		bazi2D := mingli.Bazi2D()
		rigan := bazi2D[2][0]
		ryss, err := slf.enumRepo.GetAllRiyuanShishen(ctx)
		if err != nil {
			return nil, err
		}
		ryssMap := make(map[string]string)
		for _, item := range ryss {
			ryssMap[item.Riyuan1+item.Riyuan2] = item.Shishen
		}
		nayinList, err := slf.enumRepo.GetAllNayin(ctx)
		if err != nil {
			return nil, err
		}
		nayinMap := make(map[string]*model.Nayin)
		for _, item := range nayinList {
			nayinMap[item.Zhu] = item
		}
		for _, week := range *data {
			arr := make([]*v1.CalendarEachDayOfMonth, 0, len(week))
			for _, day := range week {
				vipDay := day
				arr = append(arr, vipDay)
				dayBazi := []rune(day.Bazi3)
				key1 := rigan + string(dayBazi[0])
				key2 := rigan + string(dayBazi[1])
				vipDay.VipShishen = [2]string{ryssMap[key1], ryssMap[key2]}
				date, err := time.Parse("2006-01-02", day.Date)
				if err != nil {
					return nil, err
				}
				yun := mingli.GetDayunOrXiaoyun(date.Year())
				if yun == "" {
					continue
				}
				shensha, err := slf.coronaCli.Shensha(ctx, &corona.GetShenshaRequest{
					Bazi: []string{
						mingli.Bazi[0],
						mingli.Bazi[1],
						mingli.Bazi[2],
						mingli.Bazi[3],
						yun,
						day.Bazi1,
						day.Bazi2,
						day.Bazi3,
					},
					Gender: lo.Ternary(mingli.Gender == 1, "男", "女"),
				})
				if err != nil {
					return nil, err
				}
				vipDay.LiuriShensha = shensha.GetByIndex(constants.PaipanIndexLiuRi)
			}
			res = append(res, arr)
		}
	}
	return &res, nil
}

func (slf *dateService) Day4VIP(ctx context.Context, req *v1.CalendarDayRequest) (*v1.CalendarDayResponseData, error) {
	return slf.Day(ctx, req)
}

func (slf *dateService) Day(ctx context.Context, req *v1.CalendarDayRequest) (*v1.CalendarDayResponseData, error) {
	// TODO：补充VIP逻辑
	var (
		err    error
		mingli *model.UserMingli
	)
	if req.User != nil {
		// 查询默认分组
		group, err := slf.mingliGroupRepo.GetDefaultGroup(ctx, req.User.UserID, req.AppID)
		if err != nil {
			return nil, err
		}
		if group == nil {
			return nil, v1.ErrNotFound
		}
		// 查询默认命例
		mingli, err = slf.mingliRepo.GetDefaultMingli(ctx, req.User.UserID, req.AppID, group.ID)
		if err != nil {
			return nil, err
		}
	}
	if mingli != nil {
		// do something...
	}
	if req.Date == "" {
		req.Date = time.Now().Format("2006-01-02")
	}
	item, err := slf.calendarRepo.GetOneDay(ctx, req.Date)
	if err != nil {
		return nil, err
	}
	if item == nil {
		return nil, nil
	}
	day, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, err
	}
	yellowYears := day.Year() + 2697
	res := &v1.CalendarDayResponseData{
		Date:                item.Date,
		LunarDate:           fmt.Sprintf("%s年%s", item.LunarYear, item.LunarDate),
		Zodiac:              strings.TrimSuffix(item.Shengxiao, "年"),
		Weekday:             item.Xingqi,
		Constellation:       item.Constellation,
		Festival:            strings.Split(item.Jieri, " "),
		Yi:                  strings.Split(item.Yi, " "),
		Ji:                  strings.Split(item.Ji, " "),
		Bazi1:               item.Bazi1,
		Bazi2:               item.Bazi2,
		Bazi2Next:           item.Bazi2Next,
		Bazi3:               item.Bazi3,
		Zeri:                item.Zeri,
		PengzubaijiOverview: item.PengzubaijiOverview,
		YellowYears:         yellowYears,
		YellowYearsZh:       strs.Digit2ZhNumber(fmt.Sprintf("黄帝纪年%d年", yellowYears)),
		Wuxing:              item.Wuxing,
		Hou:                 item.Hou,
		CaiLocation:         item.CaiLocation,
		FuLocation:          item.FuLocation,
		XiLocation:          item.XiLocation,
		LuLocation:          item.LuLocation,
		Shierjianri:         item.Shierjianri,
		Jishen:              strings.Split(item.Jishen, " "),
		Xiongshen:           strings.Split(item.Xiongshen, " "),
		Taishen:             strings.Split(item.Jinritaishen, " ")[0],
		TaishenLocation:     strings.Split(item.Jinritaishen, " ")[1],
		Xingxiu:             item.Xingxiu,
		Jieqi:               item.Jieqi,
		JieqiDate:           item.Date,
	}
	if !item.JieqiTime.IsZero() {
		res.JieqiTime = item.JieqiTime.Format("2006-01-02 15:04:05")
	}
	split := strings.Split(item.PengzubaijiDetail, " ")
	if len(split) == 4 {
		res.Pengzubaiji = append(res.Pengzubaiji, fmt.Sprintf("%s %s", split[0], split[1]))
		res.Pengzubaiji = append(res.Pengzubaiji, fmt.Sprintf("%s %s", split[2], split[3]))
	}
	if _, ok := slf.huangdao[item.Zhishen]; ok {
		res.Huangdao = item.Zhishen
	} else {
		res.Heidao = item.Zhishen
	}

	if item.Jieqi == "" {
		lastJieqi, err := slf.calendarRepo.GetLastJieqi(ctx, req.Date)
		if err != nil {
			return nil, err
		}
		if lastJieqi != nil {
			res.Jieqi = lastJieqi.Jieqi
			res.JieqiDate = lastJieqi.Date
		}
	}
	pattern := `(\d{2}:\d{2}-\d{2}:\d{2})\S{2}冲(\S)煞(\S)`
	re := regexp.MustCompile(pattern)
	matches := re.FindStringSubmatch(item.Time1)
	if len(matches) > 0 {
		res.Times = append(res.Times, &v1.CalendarShichen{
			Time:        matches[1],
			Chong:       matches[2],
			Sha:         matches[3],
			Bazi:        item.Time1Bazi,
			Jixiong:     item.Time1Jixiong,
			CaiLocation: item.Time1CaiLocation,
			FuLocation:  item.Time1FuLocation,
			XiLocation:  item.Time1XiLocation,
			LuLocation:  item.Time1LuLocation,
			Yi:          item.Time1Yi,
			Ji:          item.Time1Ji,
		})
	}
	matches = re.FindStringSubmatch(item.Time2)
	if len(matches) > 0 {
		res.Times = append(res.Times, &v1.CalendarShichen{
			Time:        matches[1],
			Chong:       matches[2],
			Sha:         matches[3],
			Bazi:        item.Time2Bazi,
			Jixiong:     item.Time2Jixiong,
			CaiLocation: item.Time2CaiLocation,
			FuLocation:  item.Time2FuLocation,
			XiLocation:  item.Time2XiLocation,
			LuLocation:  item.Time2LuLocation,
			Yi:          item.Time2Yi,
			Ji:          item.Time2Ji,
		})
	}
	matches = re.FindStringSubmatch(item.Time3)
	if len(matches) > 0 {
		res.Times = append(res.Times, &v1.CalendarShichen{
			Time:        matches[1],
			Chong:       matches[2],
			Sha:         matches[3],
			Bazi:        item.Time3Bazi,
			Jixiong:     item.Time3Jixiong,
			CaiLocation: item.Time3CaiLocation,
			FuLocation:  item.Time3FuLocation,
			XiLocation:  item.Time3XiLocation,
			LuLocation:  item.Time3LuLocation,
			Yi:          item.Time3Yi,
			Ji:          item.Time3Ji,
		})
	}
	matches = re.FindStringSubmatch(item.Time4)
	if len(matches) > 0 {
		res.Times = append(res.Times, &v1.CalendarShichen{
			Time:        matches[1],
			Chong:       matches[2],
			Sha:         matches[3],
			Bazi:        item.Time4Bazi,
			Jixiong:     item.Time4Jixiong,
			CaiLocation: item.Time4CaiLocation,
			FuLocation:  item.Time4FuLocation,
			XiLocation:  item.Time4XiLocation,
			LuLocation:  item.Time4LuLocation,
			Yi:          item.Time4Yi,
			Ji:          item.Time4Ji,
		})
	}
	matches = re.FindStringSubmatch(item.Time5)
	if len(matches) > 0 {
		res.Times = append(res.Times, &v1.CalendarShichen{
			Time:        matches[1],
			Chong:       matches[2],
			Sha:         matches[3],
			Bazi:        item.Time5Bazi,
			Jixiong:     item.Time5Jixiong,
			CaiLocation: item.Time5CaiLocation,
			FuLocation:  item.Time5FuLocation,
			XiLocation:  item.Time5XiLocation,
			LuLocation:  item.Time5LuLocation,
			Yi:          item.Time5Yi,
			Ji:          item.Time5Ji,
		})
	}
	matches = re.FindStringSubmatch(item.Time6)
	if len(matches) > 0 {
		res.Times = append(res.Times, &v1.CalendarShichen{
			Time:        matches[1],
			Chong:       matches[2],
			Sha:         matches[3],
			Bazi:        item.Time6Bazi,
			Jixiong:     item.Time6Jixiong,
			CaiLocation: item.Time6CaiLocation,
			FuLocation:  item.Time6FuLocation,
			XiLocation:  item.Time6XiLocation,
			LuLocation:  item.Time6LuLocation,
			Yi:          item.Time6Yi,
			Ji:          item.Time6Ji,
		})
	}
	matches = re.FindStringSubmatch(item.Time7)
	if len(matches) > 0 {
		res.Times = append(res.Times, &v1.CalendarShichen{
			Time:        matches[1],
			Chong:       matches[2],
			Sha:         matches[3],
			Bazi:        item.Time7Bazi,
			Jixiong:     item.Time7Jixiong,
			CaiLocation: item.Time7CaiLocation,
			FuLocation:  item.Time7FuLocation,
			XiLocation:  item.Time7XiLocation,
			LuLocation:  item.Time7LuLocation,
			Yi:          item.Time7Yi,
			Ji:          item.Time7Ji,
		})
	}
	matches = re.FindStringSubmatch(item.Time8)
	if len(matches) > 0 {
		res.Times = append(res.Times, &v1.CalendarShichen{
			Time:        matches[1],
			Chong:       matches[2],
			Sha:         matches[3],
			Bazi:        item.Time8Bazi,
			Jixiong:     item.Time8Jixiong,
			CaiLocation: item.Time8CaiLocation,
			FuLocation:  item.Time8FuLocation,
			XiLocation:  item.Time8XiLocation,
			LuLocation:  item.Time8LuLocation,
			Yi:          item.Time8Yi,
			Ji:          item.Time8Ji,
		})
	}
	matches = re.FindStringSubmatch(item.Time9)
	if len(matches) > 0 {
		res.Times = append(res.Times, &v1.CalendarShichen{
			Time:        matches[1],
			Chong:       matches[2],
			Sha:         matches[3],
			Bazi:        item.Time9Bazi,
			Jixiong:     item.Time9Jixiong,
			CaiLocation: item.Time9CaiLocation,
			FuLocation:  item.Time9FuLocation,
			XiLocation:  item.Time9XiLocation,
			LuLocation:  item.Time9LuLocation,
			Yi:          item.Time9Yi,
			Ji:          item.Time9Ji,
		})
	}
	matches = re.FindStringSubmatch(item.Time10)
	if len(matches) > 0 {
		res.Times = append(res.Times, &v1.CalendarShichen{
			Time:        matches[1],
			Chong:       matches[2],
			Sha:         matches[3],
			Bazi:        item.Time10Bazi,
			Jixiong:     item.Time10Jixiong,
			CaiLocation: item.Time10CaiLocation,
			FuLocation:  item.Time10FuLocation,
			XiLocation:  item.Time10XiLocation,
			LuLocation:  item.Time10LuLocation,
			Yi:          item.Time10Yi,
			Ji:          item.Time10Ji,
		})
	}
	matches = re.FindStringSubmatch(item.Time11)
	if len(matches) > 0 {
		res.Times = append(res.Times, &v1.CalendarShichen{
			Time:        matches[1],
			Chong:       matches[2],
			Sha:         matches[3],
			Bazi:        item.Time11Bazi,
			Jixiong:     item.Time11Jixiong,
			CaiLocation: item.Time11CaiLocation,
			FuLocation:  item.Time11FuLocation,
			XiLocation:  item.Time11XiLocation,
			LuLocation:  item.Time11LuLocation,
			Yi:          item.Time11Yi,
			Ji:          item.Time11Ji,
		})
	}
	matches = re.FindStringSubmatch(item.Time12)
	if len(matches) > 0 {
		res.Times = append(res.Times, &v1.CalendarShichen{
			Time:        matches[1],
			Chong:       matches[2],
			Sha:         matches[3],
			Bazi:        item.Time12Bazi,
			Jixiong:     item.Time12Jixiong,
			CaiLocation: item.Time12CaiLocation,
			FuLocation:  item.Time12FuLocation,
			XiLocation:  item.Time12XiLocation,
			LuLocation:  item.Time12LuLocation,
			Yi:          item.Time12Yi,
			Ji:          item.Time12Ji,
		})
	}
	nextDay, err := slf.calendarRepo.GetNextDay(ctx, req.Date)
	if err != nil {
		return nil, err
	}
	if nextDay != nil {
		matches = re.FindStringSubmatch(nextDay.Time1)
		if len(matches) > 0 {
			res.Times = append(res.Times, &v1.CalendarShichen{
				Time:        matches[1],
				Chong:       matches[2],
				Sha:         matches[3],
				Bazi:        nextDay.Time1Bazi,
				Jixiong:     nextDay.Time1Jixiong,
				CaiLocation: nextDay.Time1CaiLocation,
				FuLocation:  nextDay.Time1FuLocation,
				XiLocation:  nextDay.Time1XiLocation,
				LuLocation:  nextDay.Time1LuLocation,
				Yi:          nextDay.Time1Yi,
				Ji:          nextDay.Time1Ji,
			})
		}
	}
	return res, nil
}

func (slf *dateService) Month(ctx context.Context, req *v1.CalendarMonthRequest) (*v1.CalendarMonthResponseData, error) {
	if req.Month == "" {
		req.Month = time.Now().Format("2006-01")
	}
	t, err := time.Parse("2006-01", req.Month)
	if err != nil {
		return nil, err
	}
	var (
		year     = t.Year()
		month    = t.Month()
		first    = time.Date(year, month, 1, 0, 0, 0, 0, time.Local)
		daysNum  = first.AddDate(0, 1, -1).Day()
		calendar = make([]*v1.CalendarEachDayOfMonth, 42)
		start    = int(first.Weekday())
		end      = start + daysNum - 1
		days     = make(map[string]*v1.CalendarEachDayOfMonth)
	)
	for day := 1; day <= daysNum; day++ {
		current := time.Date(year, month, day, 0, 0, 0, 0, time.Local)
		date := current.Format("2006-01-02")
		calendar[start+day-1] = &v1.CalendarEachDayOfMonth{Date: date, CurrentMonth: true}
	}

	daysInCurrent, err := slf.calendarRepo.GetDaysByMonth(ctx, req.Month)
	if err != nil {
		return nil, err
	}
	for _, day := range daysInCurrent {
		days[day.Date] = day
	}
	if end < 35 {
		calendar = calendar[:35]
	}

	prevMonth := first.AddDate(0, -1, 0)
	prevMonthDays := prevMonth.AddDate(0, 1, -1).Day()
	for i := start - 1; i >= 0; i-- {
		prev := time.Date(prevMonth.Year(), prevMonth.Month(), prevMonthDays-(start-1-i), 0, 0, 0, 0, time.Local)
		date := prev.Format("2006-01-02")
		calendar[i] = &v1.CalendarEachDayOfMonth{Date: date}
	}
	daysInPrev, err := slf.calendarRepo.GetDaysByMonth(ctx, prevMonth.Format("2006-01"))
	if err != nil {
		return nil, err
	}
	for _, day := range daysInPrev {
		days[day.Date] = day
	}

	nextMonth := first.AddDate(0, 1, 0)
	for i := end + 1; i < len(calendar); i++ {
		next := time.Date(nextMonth.Year(), nextMonth.Month(), i-end, 0, 0, 0, 0, time.Local)
		date := next.Format("2006-01-02")
		calendar[i] = &v1.CalendarEachDayOfMonth{Date: date}
	}
	daysInNext, err := slf.calendarRepo.GetDaysByMonth(ctx, nextMonth.Format("2006-01"))
	if err != nil {
		return nil, err
	}
	for _, day := range daysInNext {
		days[day.Date] = day
	}

	for _, item := range calendar {
		day := days[item.Date]
		if day == nil {
			continue
		}
		item.LunarDate = fmt.Sprintf("%s年%s", day.LunarYear, day.LunarDate)
		item.Weekday = day.Weekday
		item.Bazi1 = day.Bazi1
		item.Bazi2 = day.Bazi2
		item.Bazi2Next = day.Bazi2Next
		item.Bazi3 = day.Bazi3
		jieri := strings.Split(day.JieriStr, " ")
		for _, j := range jieri {
			if j != "" {
				item.Jieri = append(item.Jieri, j)
			}
		}
		yi := strings.Split(day.YiStr, " ")
		for _, y := range yi {
			if y != "" {
				item.Yi = append(item.Yi, y)
			}
		}
		ji := strings.Split(day.JiStr, " ")
		for _, j := range ji {
			if j != "" {
				item.Ji = append(item.Ji, j)
			}
		}
		item.Jieqi = day.Jieqi
		if !day.JieqiTime.IsZero() {
			item.JieqiTimeStr = day.JieqiTime.Format("2006-01-02 15:04:05")
		}
		item.HolidayOff = day.HolidayOff
	}
	var res v1.CalendarMonthResponseData
	for i := 0; i < len(calendar); i += 7 {
		week := calendar[i : i+7]
		res = append(res, week)
	}
	return &res, nil
}

// BirthtimeSun 获取真太阳时间
func (slf *dateService) realSunTime(ctx context.Context, birthtime time.Time, location []string) (time.Time, error) {
	if len(location) == 0 {
		return birthtime, nil
	}
	join := strings.Join(location, "")
	if len(location) == 3 {
		join = "中国" + join
	}
	offset, err := slf.calendarRepo.GetOffset4TimeByLocation(ctx, join)
	if err != nil {
		return time.Time{}, err
	}
	return birthtime.Add(time.Duration(offset) * time.Minute), nil
}
