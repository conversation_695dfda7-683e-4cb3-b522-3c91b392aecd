package service

import (
	"context"
	"fmt"
	"github.com/google/uuid"
	"github.com/lithammer/shortuuid/v4"
	"github.com/pkg/errors"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/imgcaptcha"
	"zodiacus/pkg/randx"
	aliyun_sms "zodiacus/third_party/aliyun/sms"
	"zodiacus/third_party/submail"
	"zodiacus/third_party/uniapp"
	wechat_app "zodiacus/third_party/wechat"
	"zodiacus/third_party/wecom"
)

type AuthService interface {
	GetUserInfo(ctx context.Context, req *v1.GetUserInfoRequest) (*v1.GetUserInfoResponseData, error)
	GenImageCode(ctx context.Context, req *v1.GenImageCodeRequest) (*v1.GenImageCodeResponseData, error)
	SMSAuth(ctx context.Context, req *v1.SMSAuthRequest) (string, error)
	LoginByPhone(ctx context.Context, req *v1.LoginByPhoneRequest) (*v1.LoginByPhoneResponseData, error)
	LoginByPhoneQuickly4Uni(ctx context.Context, req *v1.LoginByPhoneQuickly4UniRequest) (*v1.LoginByPhoneQuickly4UniResponseData, error)
	LoginByWechatMiniProgram(ctx context.Context, req *v1.LoginByWechatMiniProgramRequest) (*v1.LoginByWechatMiniProgramResponseData, error)
	WechatMiniProgramJsCode2Session(ctx context.Context, req *v1.WechatMiniProgramJsCode2SessionRequest) error
	WechatJsCode2Session(ctx context.Context, req *v1.WechatJsCode2SessionRequest) (*v1.WechatJsCode2SessionResponseData, error)
	Deactivate(ctx context.Context, req *v1.DeactivateRequest) error
	AdminLogin(ctx context.Context, req *v1.AdminLoginRequest) (*v1.AdminLoginResponseData, error)
}

func NewAuthService(
	service *Service,
	userRepo repository.AppUserRepository,
	adminUserRepo repository.AdminUserRepository,
	authRepo repository.AuthRepository,
	orderRepo repository.UserOrderRepository,
	doraemon *wecom.Application,
	aliyunSms *aliyun_sms.Client,
	submailer *submail.Client,
	uniappcli *uniapp.Client,
	wxoa *wechat_app.OfficialAccount,
	wxmp *wechat_app.Miniprogram,
) AuthService {
	return &authService{
		Service:       service,
		userRepo:      userRepo,
		adminUserRepo: adminUserRepo,
		authRepo:      authRepo,
		orderRepo:     orderRepo,
		doraemon:      doraemon,
		aliyunSms:     aliyunSms,
		submailer:     submailer,
		uniappcli:     uniappcli,
		wxoa:          wxoa,
		wxmp:          wxmp,
	}
}

type authService struct {
	*Service
	userRepo      repository.AppUserRepository
	adminUserRepo repository.AdminUserRepository
	authRepo      repository.AuthRepository
	orderRepo     repository.UserOrderRepository
	doraemon      *wecom.Application
	aliyunSms     *aliyun_sms.Client
	uniappcli     *uniapp.Client
	submailer     *submail.Client
	wxoa          *wechat_app.OfficialAccount
	wxmp          *wechat_app.Miniprogram
}

func (slf *authService) AdminLogin(ctx context.Context, req *v1.AdminLoginRequest) (*v1.AdminLoginResponseData, error) {
	adminUser, err := slf.adminUserRepo.FetchAdminUserByUserName(ctx, req.UserName)
	if err != nil {
		return nil, err
	}
	if adminUser == nil {
		return nil, v1.ErrBadRequest
	}
	if adminUser.Password != req.Password {
		return nil, v1.ErrBadRequest
	}
	return slf.LoginResponseToken4AdminUser(ctx, adminUser)
}

func (slf *authService) Deactivate(ctx context.Context, req *v1.DeactivateRequest) error {
	if err := slf.jwthub.DelAllTokens(ctx, req.User); err != nil {
		return err
	}
	if err := slf.userRepo.DeleteAppUser(ctx, req.User); err != nil {
		return err
	}
	return nil
}

func (slf *authService) WechatJsCode2Session(ctx context.Context, req *v1.WechatJsCode2SessionRequest) (*v1.WechatJsCode2SessionResponseData, error) {
	var res v1.WechatJsCode2SessionResponseData
	switch req.Type {
	case 1: // 小程序
		session, err := slf.wxmp.GetAuth().Code2SessionContext(ctx, req.JsCode)
		if err != nil {
			return nil, err
		}
		if session.ErrCode != 0 {
			return nil, errors.Errorf("获取session失败：code=%d, msg=%s", session.ErrCode, session.ErrMsg)
		}
		res.OpenId = session.OpenID
		res.SessionKey = session.SessionKey
		res.UnionId = session.UnionID
	case 2: // 服务号
		token, err := slf.wxoa.GetOauth().GetUserAccessTokenContext(ctx, req.JsCode)
		if err != nil {
			return nil, err
		}
		if token.ErrCode != 0 {
			return nil, errors.Errorf("获取session失败：code=%d, msg=%s", token.ErrCode, token.ErrMsg)
		}
		res.OpenId = token.OpenID
		res.SessionKey = token.AccessToken
		res.UnionId = token.UnionID
	default:
		return nil, v1.ErrBadRequest.CustomMessage("不支持的应用类型")
	}
	return &res, nil
}

func (slf *authService) WechatMiniProgramJsCode2Session(ctx context.Context, req *v1.WechatMiniProgramJsCode2SessionRequest) error {
	session, err := slf.wxmp.GetAuth().Code2SessionContext(ctx, req.JsCode)
	if err != nil {
		return err
	}
	if session.ErrCode != 0 {
		return errors.Errorf("获取session失败：code=%d, msg=%s", session.ErrCode, session.ErrMsg)
	}
	fmt.Println(session)

	// TODO
	return nil
}

// LoginByWechatMiniProgram 微信小程序登录（获取手机号走登录流程）
func (slf *authService) LoginByWechatMiniProgram(ctx context.Context, req *v1.LoginByWechatMiniProgramRequest) (*v1.LoginByWechatMiniProgramResponseData, error) {
	phoneNumberResp, err := slf.wxmp.GetAuth().GetPhoneNumberContext(ctx, req.Code)
	if err != nil {
		return nil, err
	}
	if phoneNumberResp.ErrCode != 0 {
		return nil, errors.Errorf("获取手机号失败：code=%d, msg=%s", phoneNumberResp.ErrCode, phoneNumberResp.ErrMsg)
	}
	appUser, err := slf.CheckUserByPhoneNumber(ctx, phoneNumberResp.PhoneInfo.PhoneNumber, req.Channel)
	if err != nil {
		return nil, err
	}
	return slf.LoginResponseToken4AppUser(ctx, appUser)
}

// LoginByPhoneQuickly4Uni 手机号快速登录/一键登录（uniapp）
func (slf *authService) LoginByPhoneQuickly4Uni(ctx context.Context, req *v1.LoginByPhoneQuickly4UniRequest) (*v1.LoginByPhoneQuickly4UniResponseData, error) {
	uniResp, err := slf.uniappcli.QuicklyLogin(ctx, &uniapp.QuicklyLoginRequest{
		AccessToken: req.AccessToken,
		OpenId:      req.OpenId,
	})
	if err != nil {
		return nil, err
	}
	if !uniResp.Success {
		return nil, v1.ErrBadRequest.CustomMessage("登录失败")
	}
	appUser, err := slf.CheckUserByPhoneNumber(ctx, uniResp.PhoneNumber, req.Channel)
	if err != nil {
		return nil, err
	}
	return slf.LoginResponseToken4AppUser(ctx, appUser)
}

func (slf *authService) LoginByPhone(ctx context.Context, req *v1.LoginByPhoneRequest) (*v1.LoginByPhoneResponseData, error) {
	vc, err := slf.authRepo.FetchNewestVerificationCode(ctx, "phone", "login", req.Username)
	if err != nil {
		return nil, err
	}
	if vc == nil || vc.Code != req.Password {
		return nil, v1.ErrBadRequest.CustomMessage("短信验证码错误")
	}
	if time.Now().After(vc.ExpireTime) {
		return nil, v1.ErrBadRequest.CustomMessage("短信验证码已过期")
	}
	if vc.IsUsed {
		return nil, v1.ErrBadRequest.CustomMessage("短信验证码已使用")
	}
	if err = slf.authRepo.SetVerificationCodeUsed(ctx, vc.ID); err != nil {
		return nil, err
	}
	appUser, err := slf.CheckUserByPhoneNumber(ctx, req.Username, req.Channel)
	if err != nil {
		return nil, err
	}
	return slf.LoginResponseToken4AppUser(ctx, appUser)
}

// SMSAuth 发送短信验证码
func (slf *authService) SMSAuth(ctx context.Context, req *v1.SMSAuthRequest) (string, error) {
	if !imgcaptcha.VerifyCaptcha(req.ClientSecret, req.CaptchaToken) {
		return "图形验证码错误", nil
	}
	code := randx.RandomCode(4)
	send, err := slf.submailer.SMS().XSend(ctx, &submail.SMSXSendRequest{
		To:      req.Dest,
		Project: "hqdSn3",
		Vars:    map[string]any{"code": code},
	})
	if err != nil {
		return "", err
	}
	fmt.Println(send)
	if _, err = slf.authRepo.InsertVerificationCode(ctx, &model.VerificationCode{
		Code:       code,
		Type:       "phone",
		Scene:      "login",
		Dest:       req.Dest,
		IsUsed:     false,
		ExpireTime: time.Now().Add(time.Minute * 10).Add(time.Second * 20),
	}); err != nil {
		return "", err
	}

	return "", nil
}

// GenImageCode 生成图片验证码
func (slf *authService) GenImageCode(ctx context.Context, req *v1.GenImageCodeRequest) (*v1.GenImageCodeResponseData, error) {
	id, img, err := imgcaptcha.GetCaptcha()
	if err != nil {
		return nil, err
	}
	return &v1.GenImageCodeResponseData{
		CaptchaId:    id,
		CaptchaImage: img,
	}, nil
}

// GetUserInfo 查询用户信息：ID、昵称、会员过期时间、是否付费
func (slf *authService) GetUserInfo(ctx context.Context, req *v1.GetUserInfoRequest) (*v1.GetUserInfoResponseData, error) {
	appUser, err := slf.userRepo.FetchAppUserByUserID(ctx, req.UserID)
	if err != nil {
		return nil, err
	}
	if appUser == nil {
		return nil, v1.ErrUnauthorized
	}
	// 会员信息
	premiumInfo, err := slf.userRepo.FetchPremiumInfoByUserID(ctx, req.UserID, req.Application)
	if err != nil {
		return nil, err
	}
	var expireAt int64
	if premiumInfo != nil {
		expireAt = premiumInfo.ExpireTime.Unix()
	}
	// 是否付费（todo 是否付费标志应合并到会员信息表中）
	paid, err := slf.orderRepo.IsPaidUser(ctx, req.UserID)
	if err != nil {
		return nil, err
	}
	return &v1.GetUserInfoResponseData{
		UserProfileInfo: v1.UserProfileInfo{
			ID:          req.UserID,
			DisplayName: appUser.DisplayName,
		},
		UserPremiumInfo: v1.UserPremiumInfo{
			IsPaid:     paid,
			ExpireTime: expireAt,
		},
	}, nil
}

func (slf *authService) CheckUserByPhoneNumber(ctx context.Context, phone, channel string) (*model.AppUser, error) {
	appUser, err := slf.userRepo.FetchAppUserByPhone(ctx, phone)
	if err != nil {
		return nil, err
	}
	if appUser == nil {
		appUser = &model.AppUser{
			UserID:              uuid.New().String(),
			Name:                phone,
			DisplayName:         slf.MaskPhoneNumber(phone),
			Avatar:              "",
			Phone:               phone,
			SignupApplicationID: 2,
			Channel:             channel,
		}
		if _, err = slf.userRepo.CreateAppUser(ctx, appUser); err != nil {
			return nil, err
		}
	}
	return appUser, nil
}

func (slf *authService) LoginResponseToken4AppUser(ctx context.Context, appUser *model.AppUser) (*v1.LoginResponseData, error) {
	var (
		tokenID   = shortuuid.New()
		userID    = appUser.UserID
		username  = appUser.Name
		expiresAt = time.Now().Add(time.Hour * 24 * 30) // a month.
	)
	token, err := slf.jwthub.GenToken(ctx, tokenID, userID, username, expiresAt)
	if err != nil {
		return nil, err
	}
	return &v1.LoginByPhoneResponseData{
		AccessToken:  token,
		RefreshToken: "",
		ExpiresIn:    expiresAt.Unix(),
	}, nil
}

func (slf *authService) LoginResponseToken4AdminUser(ctx context.Context, adminUser *model.AdminUser) (*v1.LoginResponseData, error) {
	var (
		tokenID   = shortuuid.New()
		userID    = adminUser.UserID
		username  = adminUser.Username
		expiresAt = time.Now().Add(time.Hour * 24 * 30) // a month.
	)
	token, err := slf.jwthub.GenToken(ctx, tokenID, userID, username, expiresAt)
	if err != nil {
		return nil, err
	}
	return &v1.LoginByPhoneResponseData{
		AccessToken:  token,
		RefreshToken: "",
		ExpiresIn:    expiresAt.Unix(),
	}, nil
}
