package service

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"strings"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/randstring"
)

type AppUserInvitationService interface {
	GetRewardUnreadPopups(ctx context.Context, req *v1.GetInviteRewardUnreadPopupsRequest) (*v1.GetInviteRewardUnreadPopupsResponseData, error)
	InviteBindingIP(ctx context.Context, req *v1.InviteBindingIPRequest) error
	GetInviteCode(ctx context.Context, req *v1.GetInviteCodeRequest) (*v1.GetInviteCodeResponseData, error)
	GetInviteReferrals(ctx context.Context, req *v1.GetInviteReferralsRequest) (*v1.GetInviteReferralsResponseData, error)
	GetInviteReferrer(ctx context.Context, req *v1.GetInviteReferrerRequest) (*v1.GetInviteReferrerResponseData, error)
	AcceptInvitation(ctx context.Context, req *v1.AcceptInvitationRequest) (*v1.AcceptInvitationResponseData, error)
}

func NewAppUserInvitationService(
	service *Service,
	invitationRepo repository.AppUserInvitationRepository,
	appUserRepo repository.AppUserRepository,
) AppUserInvitationService {
	return &appUserInvitationService{
		Service:        service,
		invitationRepo: invitationRepo,
		appUserRepo:    appUserRepo,
	}
}

type appUserInvitationService struct {
	*Service
	invitationRepo repository.AppUserInvitationRepository
	appUserRepo    repository.AppUserRepository
}

func (slf *appUserInvitationService) GetRewardUnreadPopups(ctx context.Context, req *v1.GetInviteRewardUnreadPopupsRequest) (*v1.GetInviteRewardUnreadPopupsResponseData, error) {
	records, err := slf.invitationRepo.FetchUnreadInviteRecords(ctx, req.GetUserID())
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, nil
	}
	var res []*v1.GetInviteRewardUnreadPopupsResponseDataItem
	for _, record := range records {
		item := &v1.GetInviteRewardUnreadPopupsResponseDataItem{
			InviteCode: record.InviteCode,
			InviteTime: record.InviteTime,
		}
		if record.InviteeID == req.GetUserID() {
			item.Type = "invitee_reward"
			item.Inviter = slf.MaskPhoneNumber(record.InviterPhone)
			item.GiftVipDuration = record.InviteeVipDuration
		}
		if record.InviterID == req.GetUserID() {
			item.Type = "inviter_reward"
			item.Invitee = slf.MaskPhoneNumber(record.InviteePhone)
			item.GiftVipDuration = record.InviterVipDuration
		}
		res = append(res, item)
	}
	if err = slf.tx.Transaction(ctx, func(ctx context.Context) error {
		if err = slf.invitationRepo.MarkInviteRecordsAsRead4Inviter(ctx, req.GetUserID()); err != nil {
			return err
		}
		if err = slf.invitationRepo.MarkInviteRecordsAsRead4Invitee(ctx, req.GetUserID()); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return nil, err
	}
	return &res, nil
}

// InviteBindingIP 邀请码绑定IP
func (slf *appUserInvitationService) InviteBindingIP(ctx context.Context, req *v1.InviteBindingIPRequest) error {
	code, err := slf.invitationRepo.GetInviteCode(ctx, req.GetChannel())
	if err != nil {
		return err
	}
	if code == nil {
		return v1.ErrBadRequest
	}
	var expireTime = time.Now().Add(time.Hour * 24 * 3)
	if req.Channel == "" || req.IP == "" {
		return nil
	}
	if err := slf.tx.Transaction(ctx, func(ctx context.Context) error {
		binding, err := slf.invitationRepo.FetchInviteBindingIP(ctx, req.Channel, req.IP)
		if err != nil {
			return err
		}
		if binding == nil {
			binding = &model.InviteBindingIP{
				InviteCode: req.Channel,
				IP:         req.IP,
				ExpireTime: expireTime,
			}
			if err = slf.invitationRepo.CreateInviteBindingIP(ctx, binding); err != nil {
				return err
			}
		} else {
			if !binding.IsExpired() {
				return nil
			}
			binding.ExpireTime = expireTime
			if err = slf.invitationRepo.UpdateInviteBindingIP(ctx, binding); err != nil {
				return err
			}
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}

func (slf *appUserInvitationService) AcceptInvitation(ctx context.Context, req *v1.AcceptInvitationRequest) (*v1.AcceptInvitationResponseData, error) {
	user, err := slf.appUserRepo.FetchAppUserByUserID(ctx, req.User.UserID)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, v1.ErrUnauthorized
	}
	data, err := slf.invitationRepo.GetInviteRecordByInvitee(ctx, req.User.UserID)
	if err != nil {
		return nil, err
	}
	if data != nil {
		return nil, v1.ErrInvitationOnlyOnce
	}
	code, err := slf.invitationRepo.GetInviteCode(ctx, req.InviteCode)
	if err != nil {
		return nil, err
	}
	if code == nil {
		return nil, v1.ErrInvitationCodeInvalid
	}
	if code.UserID == req.User.UserID {
		return nil, v1.ErrInvitationSameUser
	}
	inviter, err := slf.appUserRepo.FetchAppUserByUserID(ctx, code.UserID)
	if err != nil {
		return nil, err
	}
	if inviter == nil {
		return nil, v1.ErrInvitationCodeInvalid
	}
	level, err := slf.invitationRepo.FetchInviteLevel(ctx, code.Level)
	if err != nil {
		return nil, err
	}
	if level == nil {
		return nil, v1.ErrInvitationCodeInvalid
	}
	var (
		inviterGift        = time.Hour * time.Duration(level.InviterGiftVipHours)
		inviteeGift        = time.Hour * time.Duration(level.InviteeGiftVipHours)
		inviterGiftSeconds = int64(inviterGift.Seconds())
		inviteeGiftSeconds = int64(inviteeGift.Seconds())
		record             = &model.InviteRecord{
			ID:                 slf.sid.Int64(),
			InviterID:          code.UserID,
			InviterPhone:       inviter.Phone,
			InviterVipDuration: inviterGiftSeconds,
			InviteeID:          req.User.UserID,
			InviteePhone:       user.Phone,
			InviteeVipDuration: inviteeGiftSeconds,
			InviteCode:         code.Code,
			InviteTime:         time.Now(),
		}
	)
	var (
		isNew     = false
		expiredAt time.Time
	)
	// 查询是否会员
	if err = slf.tx.Transaction(ctx, func(ctx context.Context) error {
		role := int64(1) // 排盘会员为1
		if err = slf.invitationRepo.CreateInviteRecord(ctx, record); err != nil {
			return err
		}
		// 被邀请者
		inviteeDuration, err := slf.invitationRepo.GetAppVipDuration(ctx, record.InviteeID, role)
		if err != nil {
			return err
		}
		if inviteeDuration == nil {
			isNew = true
			inviteeDuration = &model.MemberDuration{
				ID:         slf.sid.Int64(),
				UserID:     record.InviteeID,
				Role:       role,
				ExpireTime: time.Now().Add(inviteeGift),
			}
			if err = slf.invitationRepo.CreateAppVipDuration(ctx, inviteeDuration); err != nil {
				return err
			}
		} else {
			isNew = !inviteeDuration.IsValid()
			if inviteeDuration.IsValid() {
				inviteeDuration.ExpireTime = inviteeDuration.ExpireTime.Add(inviteeGift)
			} else {
				inviteeDuration.ExpireTime = time.Now().Add(inviteeGift)
			}
			if err = slf.invitationRepo.UpdateAppVipDuration(ctx, inviteeDuration); err != nil {
				return err
			}
		}
		// 邀请者
		inviterDuration, err := slf.invitationRepo.GetAppVipDuration(ctx, record.InviterID, role)
		if err != nil {
			return err
		}
		if inviterDuration == nil {
			inviterDuration = &model.MemberDuration{
				ID:         slf.sid.Int64(),
				UserID:     record.InviterID,
				Role:       role,
				ExpireTime: time.Now().Add(inviterGift),
			}
			if err = slf.invitationRepo.CreateAppVipDuration(ctx, inviterDuration); err != nil {
				return err
			}
		} else {
			if inviterDuration.IsValid() {
				inviterDuration.ExpireTime = inviterDuration.ExpireTime.Add(inviterGift)
			} else {
				inviterDuration.ExpireTime = time.Now().Add(inviterGift)
			}
			if err = slf.invitationRepo.UpdateAppVipDuration(ctx, inviterDuration); err != nil {
				return err
			}
		}
		// 被邀请者会员过期时间
		expiredAt = inviteeDuration.ExpireTime
		return nil
	}); err != nil {
		return nil, err
	}
	return &v1.AcceptInvitationResponseData{
		IsNewVip:        isNew,
		GiftVipDuration: inviteeGiftSeconds,
		VipExpiredAt:    expiredAt.Format(time.DateTime),
	}, nil
}

// GetInviteCode 查询个人邀请码
func (slf *appUserInvitationService) GetInviteCode(ctx context.Context, req *v1.GetInviteCodeRequest) (*v1.GetInviteCodeResponseData, error) {
	code, err := slf.invitationRepo.GetInviteCodeByUID(ctx, req.User.UserID)
	if err != nil {
		return nil, err
	}
	if code != nil {
		return &v1.GetInviteCodeResponseData{InviteCode: code.Code}, nil
	}
	for {
		code = &model.InviteCode{
			ID:     slf.sid.Int64(),
			UserID: req.User.UserID,
			Code:   slf.GenerateInviteCode(req.User.UserID, randstring.RandomStr(4, randstring.CharsetAlphaNumeric), 4),
		}
		ok, err := slf.invitationRepo.CreateInviteCode(ctx, code)
		if err != nil {
			return nil, err
		}
		if ok {
			break
		}
	}
	return &v1.GetInviteCodeResponseData{InviteCode: code.Code}, nil
}

// GetInviteReferrals 查询邀请记录
func (slf *appUserInvitationService) GetInviteReferrals(ctx context.Context, req *v1.GetInviteReferralsRequest) (*v1.GetInviteReferralsResponseData, error) {
	data, err := slf.invitationRepo.GetInviteRecordsByInviter(ctx, req)
	if err != nil {
		return nil, err
	}
	for _, item := range data.List {
		item.Invitee = slf.MaskPhoneNumber(item.Invitee)
	}
	return data, nil
}

// GetInviteReferrer 查询被邀请记录
func (slf *appUserInvitationService) GetInviteReferrer(ctx context.Context, req *v1.GetInviteReferrerRequest) (*v1.GetInviteReferrerResponseData, error) {
	data, err := slf.invitationRepo.GetInviteRecordByInvitee(ctx, req.User.UserID)
	if err != nil {
		return nil, err
	}
	if data == nil {
		return nil, nil
	}
	return &v1.GetInviteReferrerResponseData{
		Inviter:         slf.MaskPhoneNumber(data.InviterPhone),
		GiftVipDuration: data.InviteeVipDuration,
		InviteAt:        data.InviteTime.Format(time.DateTime),
	}, nil
}

// GenerateInviteCode 生成邀请码
func (slf *appUserInvitationService) GenerateInviteCode(uid, salt string, length int) string {
	mixed := fmt.Sprintf("%s%s", uid, salt)
	hash := sha256.Sum256([]byte(mixed))
	base64Str := base64.StdEncoding.EncodeToString(hash[:])
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	var result strings.Builder
	for i := 0; i < length; i++ {
		index := int(base64Str[i]) % len(charset)
		result.WriteByte(charset[index])
	}
	return result.String()
}
