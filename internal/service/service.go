package service

import (
	"zodiacus/internal/repository"
	"zodiacus/pkg/geoip"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/pkg/oss/aliyun"
	"zodiacus/pkg/sid"
	"zodiacus/third_party/corona"
)

type Service struct {
	logger    *log.Logger
	sid       *sid.Sid
	tx        repository.Transaction
	coronaCli *corona.Client
	*corona.Helper
	irs    *geoip.Client
	oss    *aliyun_oss.Client
	jwthub *jwthub.Jwthub
}

func NewService(
	tm repository.Transaction,
	logger *log.Logger,
	sid *sid.Sid,
	coronaCli *corona.Client,
	irs *geoip.Client,
	oss *aliyun_oss.Client,
	jwthub *jwthub.Jwthub,
) *Service {
	return &Service{
		logger:    logger,
		tx:        tm,
		coronaCli: coronaCli,
		Helper:    corona.NewHelper(),
		sid:       sid,
		irs:       irs,
		oss:       oss,
		jwthub:    jwthub,
	}
}

func (slf *Service) MaskPhoneNumber(phoneNumber string) string {
	n := len(phoneNumber)
	if n >= 10 {
		return phoneNumber[:n-8] + "******" + phoneNumber[n-2:]
	}
	length := n / 2
	right := (n - length + 1) / 2
	left := n - length - right
	mask := make([]byte, length)
	for i := range mask {
		mask[i] = '*'
	}
	return phoneNumber[:left] + string(mask) + phoneNumber[n-right:]
}
