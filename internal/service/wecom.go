package service

import (
	"context"
	"fmt"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/contract"
	contactWayRequest "github.com/ArtisanCloud/PowerWeChat/v3/src/work/externalContact/contactWay/request"
	acquisitionRequest "github.com/ArtisanCloud/PowerWeChat/v3/src/work/externalContact/customerAcquisition"
	conatctTemplateRquest "github.com/ArtisanCloud/PowerWeChat/v3/src/work/externalContact/messageTemplate/request"
	externalContactRequest "github.com/ArtisanCloud/PowerWeChat/v3/src/work/externalContact/request"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/work/server/handlers/models"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"net/http"
	"sort"
	"strconv"
	"strings"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/third_party/wecom"
)

type WecomService interface {
	DoraemonVerify(*http.Request) (*http.Response, error)
	DoraemonNotify(*http.Request) (*http.Response, error)
	Departments(context.Context, *v1.QwDepartmentsRequest) (v1.QwDepartmentsResponseData, error)
	CreateContactWay(context.Context, *v1.QwCreateContactWayRequest) (v1.QwCreateContactWayResponseData, error)
	CreateAcquisitionLink(context.Context, *v1.QwCreateAcquisitionLinkRequest) (v1.QwCreateAcquisitionLinkResponseData, error)
	UpdateContactWay(context.Context, *v1.QwUpdateContactWayRequest) error
	DeleteContactWay(context.Context, *v1.QwDeleteContactWayRequest) error
	PageListContactWay(context.Context, *v1.QwPageListContactWayRequest) (*v1.QwPageListContactWayResponseData, error)
	PageListBill(context.Context, *v1.QwPageListBillRequest) (*v1.QwPageListBillResponseData, error)
	PageListContactFollow(context.Context, *v1.QwPageListContactFollowRequest) (*v1.QwPageListContactFollowResponseData, error)
	ContactFollowUsers(context.Context, *v1.QwContactFollowUsersRequest) (v1.QwContactFollowUsersResponseData, error)
	UpdateContact(ctx context.Context, request *v1.QwUpdateContactRequest) error
}

func NewWecomService(service *Service,
	doraemon *wecom.Application,
	qwRepository repository.QwRepository,
	appUserKefuRepo repository.AppUserKefuRepository,
	appUserRepo repository.AppUserRepository,
	orderRepo repository.UserOrderRepository,
) WecomService {
	return &wecomService{
		Service:      service,
		doraemon:     doraemon,
		qwRepository: qwRepository,
		kefuRepo:     appUserKefuRepo,
		userRepo:     appUserRepo,
		orderRepo:    orderRepo,
	}
}

type wecomService struct {
	*Service
	doraemon     *wecom.Application
	qwRepository repository.QwRepository
	kefuRepo     repository.AppUserKefuRepository
	userRepo     repository.AppUserRepository
	orderRepo    repository.UserOrderRepository
}

func (slf *wecomService) UpdateContact(ctx context.Context, req *v1.QwUpdateContactRequest) error {
	return slf.qwRepository.UpdateContact(ctx, req)
}

func (slf *wecomService) ContactFollowUsers(ctx context.Context, req *v1.QwContactFollowUsersRequest) (v1.QwContactFollowUsersResponseData, error) {
	userIDs, err := slf.qwRepository.FetchContactFollowUserIDs(ctx)
	if err != nil {
		return nil, err
	}
	usersMap := make(map[string]*v1.QwMember)
	departments, err := slf.Departments(ctx, &v1.QwDepartmentsRequest{
		ParentID:       0,
		ContainsMember: true,
	})
	if err != nil {
		return nil, err
	}
	for _, department := range departments {
		var fn func(node *v1.QwDepartmentTreeNode)
		fn = func(node *v1.QwDepartmentTreeNode) {
			for _, member := range node.Members {
				usersMap[member.UserID] = member
			}
			for _, child := range node.Children {
				fn(child)
			}
		}
		fn(department)
	}
	users := make(v1.QwContactFollowUsersResponseData, 0, len(userIDs))
	for _, userID := range userIDs {
		if user, ok := usersMap[userID]; ok {
			users = append(users, user)
		}
	}
	return users, nil
}

func (slf *wecomService) PageListContactFollow(ctx context.Context, req *v1.QwPageListContactFollowRequest) (*v1.QwPageListContactFollowResponseData, error) {
	return slf.qwRepository.PageListContactFollow(ctx, req)
}

func (slf *wecomService) PageListBill(ctx context.Context, req *v1.QwPageListBillRequest) (*v1.QwPageListBillResponseData, error) {
	return slf.qwRepository.PageListBill(ctx, req)
}

func (slf *wecomService) PageListContactWay(ctx context.Context, req *v1.QwPageListContactWayRequest) (*v1.QwPageListContactWayResponseData, error) {
	return slf.qwRepository.PageListContactWay(ctx, req)
}

func (slf *wecomService) DeleteContactWay(ctx context.Context, req *v1.QwDeleteContactWayRequest) error {
	return slf.qwRepository.DeleteContactWay(ctx, req.ID)
}

func (slf *wecomService) UpdateContactWay(ctx context.Context, req *v1.QwUpdateContactWayRequest) error {
	way, err := slf.qwRepository.FetchContactWayByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if way == nil {
		return v1.ErrQwContactWayNotFound
	}
	qwResp1, err := slf.doraemon.ExternalContactContactWay.Get(ctx, way.ConfigID)
	if err != nil {
		return err
	}
	if qwResp1.ErrCode != 0 {
		return errors.Errorf("获取联系我失败：code=%d, msg=%s", qwResp1.ErrCode, qwResp1.ErrMsg)
	}
	qwResp2, err := slf.doraemon.ExternalContactContactWay.Update(ctx, &contactWayRequest.RequestUpdateContactWay{
		ConfigID:      way.ConfigID,
		Remark:        req.Name,
		SkipVerify:    req.SkipVerify,
		Style:         qwResp1.ContactWay.Style,
		State:         way.AddState,
		User:          req.UserIDs,
		Party:         qwResp1.ContactWay.Party,
		ExpiresIn:     qwResp1.ContactWay.ExpiresIn,
		ChatExpiresIn: qwResp1.ContactWay.ChatExpiresIn,
		UnionID:       qwResp1.ContactWay.UnionID,
		Conclusions: func() *contactWayRequest.Conclusions {
			if qwResp1.ContactWay.Conclusions == nil {
				return nil
			}
			var conclusions *contactWayRequest.Conclusions
			if qwResp1.ContactWay.Conclusions.Text != nil {
				conclusions.Text = &conatctTemplateRquest.TextOfMessage{
					Content: qwResp1.ContactWay.Conclusions.Text.Content,
				}
			}
			if qwResp1.ContactWay.Conclusions.Image != nil {
				conclusions.Image = &conatctTemplateRquest.Image{
					PicURL: qwResp1.ContactWay.Conclusions.Image.PicURL,
				}
			}
			if qwResp1.ContactWay.Conclusions.Link != nil {
				conclusions.Link = &conatctTemplateRquest.Link{
					Title:  qwResp1.ContactWay.Conclusions.Link.Title,
					PicURL: qwResp1.ContactWay.Conclusions.Link.PicURL,
					Desc:   qwResp1.ContactWay.Conclusions.Link.Desc,
					URL:    qwResp1.ContactWay.Conclusions.Link.URL,
				}
			}
			if qwResp1.ContactWay.Conclusions.MiniProgram != nil {
				conclusions.MiniProgram = &conatctTemplateRquest.MiniProgram{
					Title:      qwResp1.ContactWay.Conclusions.MiniProgram.Title,
					PicMediaID: qwResp1.ContactWay.Conclusions.MiniProgram.PicMediaID,
					AppID:      qwResp1.ContactWay.Conclusions.MiniProgram.AppID,
					Page:       qwResp1.ContactWay.Conclusions.MiniProgram.Page,
				}
			}
			return conclusions
		}(),
	})
	if err != nil {
		return err
	}
	if qwResp2.ErrCode != 0 {
		return errors.Errorf("更新联系我失败：code=%d, msg=%s", qwResp2.ErrCode, qwResp2.ErrMsg)
	}
	return slf.qwRepository.UpdateContactWay(ctx, &model.QwContactWay{
		ID:         req.ID,
		Name:       req.Name,
		SkipVerify: req.SkipVerify,
		UserIDs:    req.UserIDs,
	})
}

func (slf *wecomService) CreateContactWay(ctx context.Context, req *v1.QwCreateContactWayRequest) (v1.QwCreateContactWayResponseData, error) {
	qwResp, err := slf.doraemon.ExternalContactContactWay.Add(ctx, &contactWayRequest.RequestAddContactWay{
		Type:       2,              // 多人
		Scene:      2,              // 二维码
		Remark:     req.Name,       // 名称
		SkipVerify: req.SkipVerify, // 是否跳过验证
		User:       req.UserIDs,    // 员工ID
		State:      req.AddState,   // 添加参数
	})
	if err != nil {
		return 0, err
	}
	if qwResp.ErrCode != 0 {
		return 0, errors.Errorf("创建联系我失败：code=%d, msg=%s", qwResp.ErrCode, qwResp.ErrMsg)
	}
	return slf.qwRepository.CreateContactWay(ctx, &model.QwContactWay{
		Type:       1,
		AppID:      req.AppID,
		PlatformID: req.PlatformID,
		Name:       req.Name,
		Link:       qwResp.QRCode,
		UserIDs:    req.UserIDs,
		ConfigID:   qwResp.ConfigID,
		SkipVerify: req.SkipVerify,
		AddState:   req.AddState,
	})
}

func (slf *wecomService) CreateAcquisitionLink(ctx context.Context, req *v1.QwCreateAcquisitionLinkRequest) (v1.QwCreateAcquisitionLinkResponseData, error) {
	qwResp, err := slf.doraemon.ExternalContactCustomerAcquisition.CreateLink(ctx, &acquisitionRequest.CreateCustomerAcquisitionLinkReq{
		LinkName: req.Name,
		Range: &acquisitionRequest.Range{
			UserList:       req.UserIDs,
			DepartmentList: nil,
		},
		SkipVerify:     true,
		PriorityOption: nil,
	})
	if err != nil {
		return 0, err
	}
	if qwResp.ErrCode != 0 {
		return 0, errors.Errorf("创建获客链接失败：code=%d, msg=%s", qwResp.ErrCode, qwResp.ErrMsg)
	}
	return slf.qwRepository.CreateContactWay(ctx, &model.QwContactWay{
		Type:       2,
		AppID:      req.AppID,
		PlatformID: req.PlatformID,
		Name:       req.Name,
		Link:       qwResp.Link.Url,
		UserIDs:    req.UserIDs,
		ConfigID:   qwResp.Link.LinkID,
		SkipVerify: req.SkipVerify,
	})
}

func (slf *wecomService) Departments(ctx context.Context, req *v1.QwDepartmentsRequest) (v1.QwDepartmentsResponseData, error) {
	var departments []*v1.QwDepartment
	qwResp1, err := slf.doraemon.Department.SimpleList(ctx, req.ParentID)
	if err != nil {
		return nil, err
	}
	if qwResp1.ErrCode != 0 {
		return nil, errors.Errorf("获取部门列表失败：code=%d, msg=%s", qwResp1.ErrCode, qwResp1.ErrMsg)
	}
	for _, departmentID := range qwResp1.DepartmentIDs {
		qwResp2, err := slf.doraemon.Department.Get(ctx, departmentID.ID)
		if err != nil {
			return nil, err
		}
		if qwResp2.ErrCode != 0 {
			return nil, errors.Errorf("获取部门详情失败：code=%d, msg=%s", qwResp2.ErrCode, qwResp2.ErrMsg)
		}
		departments = append(departments, &v1.QwDepartment{
			ID:       qwResp2.Department.ID,
			Order:    qwResp2.Department.Order,
			ParentID: qwResp2.Department.ParentID,
			Name:     qwResp2.Department.Name,
		})
	}
	data := func() []*v1.QwDepartmentTreeNode {
		var fn func(node *v1.QwDepartmentTreeNode, m map[int][]*v1.QwDepartmentTreeNode) *v1.QwDepartmentTreeNode
		fn = func(node *v1.QwDepartmentTreeNode, m map[int][]*v1.QwDepartmentTreeNode) *v1.QwDepartmentTreeNode {
			node.Children = m[node.ID]
			for _, child := range node.Children {
				fn(child, m)
			}
			return node
		}
		m := make(map[int][]*v1.QwDepartmentTreeNode)
		for _, department := range departments {
			m[department.ParentID] = append(m[department.ParentID], &v1.QwDepartmentTreeNode{
				QwDepartment: department,
			})
		}
		for _, nodes := range m {
			sort.Slice(nodes, func(i, j int) bool {
				return nodes[i].Order < nodes[j].Order
			})
		}
		var roots []*v1.QwDepartmentTreeNode
		for _, node := range m[0] {
			roots = append(roots, fn(node, m))
		}
		return roots
	}()
	if req.ContainsMember {
		var fn func(ctx context.Context, node *v1.QwDepartmentTreeNode) error
		fn = func(ctx context.Context, node *v1.QwDepartmentTreeNode) error {
			qwResp, err := slf.doraemon.User.GetDepartmentUsers(ctx, node.ID, 0)
			if err != nil {
				return err
			}
			if qwResp.ErrCode != 0 {
				return errors.Errorf("获取部门成员失败：code=%d, msg=%s", qwResp.ErrCode, qwResp.ErrMsg)
			}
			for _, user := range qwResp.UserList {
				node.Members = append(node.Members, &v1.QwMember{
					UserID: user.UserID,
					Name:   user.Name,
				})
			}
			if len(node.Children) == 0 {
				return nil
			}
			for _, child := range node.Children {
				if err := fn(ctx, child); err != nil {
					return err
				}
			}
			return nil
		}
		for _, node := range data {
			if err = fn(ctx, node); err != nil {
				return nil, err
			}
		}
	}
	return data, nil
}

func (slf *wecomService) DoraemonVerify(request *http.Request) (*http.Response, error) {
	return slf.doraemon.Server.Serve(request)
}

func (slf *wecomService) DoraemonNotify(req *http.Request) (*http.Response, error) {
	return slf.doraemon.Server.Notify(req, func(event contract.EventInterface) any {
		ctx := req.Context()
		switch event.GetEvent() {
		case models.CALLBACK_EVENT_CHANGE_EXTERNAL_CONTACT:
			if err := slf.handleContactChangeEvent(ctx, event); err != nil {
				slf.logger.Error("企业微信回调事件：外部联系人变更通知处理失败", zap.String("应用", "doraemon"), zap.Any("event", event), zap.Error(err))
				return ""
			}
		case models.CALLBACK_EVENT_MSGAUDIT_NOTIFY:
			slf.logger.Info("企业微信回调事件：会话消息更新通知", zap.String("应用", "doraemon"), zap.Any("event", event))
		default:
		}
		return kernel.SUCCESS_EMPTY_RESPONSE
	})
}

func (slf *wecomService) handleContactChangeEvent(ctx context.Context, event contract.EventInterface) error {
	switch event.GetChangeType() {
	case models.CALLBACK_EVENT_CHANGE_TYPE_ADD_EXTERNAL_CONTACT: // 添加外部联系人事件
		payload := models.EventExternalUserAdd{}
		if err := event.ReadMessage(&payload); err != nil {
			return errors.Wrap(err, "解析添加外部联系人事件数据失败")
		}
		slf.logger.WithContext(ctx).Info("添加外部联系人事件", zap.Any("event", payload))
		resp, err := slf.doraemon.ExternalContact.Get(ctx, payload.ExternalUserID, "")
		if err != nil {
			return errors.Wrap(err, "获取外部联系人失败")
		}
		if resp.ErrCode != 0 {
			return errors.Errorf("获取外部联系人失败：code=%d, msg=%s", resp.ErrCode, resp.ErrMSG)
		}
		slf.logger.WithContext(ctx).Info("外部联系人详情", zap.Any("resp", resp))
		addTime, err := strconv.ParseInt(payload.CreateTime, 10, 64)
		if err != nil {
			return errors.Wrap(err, "解析客户添加时间失败")
		}
		addState := payload.State
		var (
			appUserID     string
			bindingUserID bool
		)
		if strings.Contains(addState, "ADFLOW_") { // ADFLOW_MLBZ_29093_A1
			splitN := strings.Split(addState, "_") // 应用_功能_排盘ID_加粉按钮ID
			switch splitN[1] {
			case "MLBZ":
				paipanID, err := strconv.ParseInt(splitN[2], 10, 64)
				if err != nil {
					return errors.Wrap(err, "解析排盘ID失败")
				}
				msgResp, err := slf.doraemon.ExternalContactMessageTemplate.SendWelcomeMsg(ctx, &conatctTemplateRquest.RequestSendWelcomeMsg{
					WelcomeCode: payload.WelcomeCode,
					Text: &conatctTemplateRquest.TextOfMessage{Content: fmt.Sprintf(
						"恭喜缘主%s，已成功解锁测算报告，请点开链接查看结果。如遇打不开，可发送订单号，等待老师稍后为您查询\n\n老师从事命理行业多年，如需以下服务，请回复对应数字：\n\n回复【1】了解五行缺失、财运财库、健康、事业婚姻等命局解析，定制开财补库方案\n回复【2】深入了解家宅風水、提升家宅运势\n回复【6】领取“8字風水学习运用”4天免费直播课\n回复【8】咨询其他问题，请直接描述问题\n\n因过年期间咨询人数较多，请您先留言耐心等待回复，谢谢", resp.ExternalContact.Name)},
					Attachments: []conatctTemplateRquest.MessageTemplateInterface{
						&conatctTemplateRquest.LinkOfMessage{
							MsgType: "link",
							Link: &conatctTemplateRquest.Link{
								Title: "点击查看测算报告",
								URL:   fmt.Sprintf("https://luck.laibuyi.com/pages/orderPay/luncai?id=%d", paipanID),
							},
						},
					},
				})
				if err != nil {
					return errors.Wrap(err, "发送欢迎消息失败")
				}
				if msgResp.ErrCode != 0 {
					return errors.Errorf("发送欢迎消息失败：code=%d, msg=%s", msgResp.ErrCode, msgResp.ErrMsg)
				}
			}
		} else if strings.HasPrefix(addState, "kefu_") {
			appUserID, err = slf.kefuRepo.FetchUserIdByAddState(ctx, addState)
			if err != nil {
				return err
			}
			if appUserID != "" {
				bindingUserID = true
			}
		}
		var (
			addWay              int
			remark, description string
		)
		for _, item := range resp.FollowUsers {
			if item.UserID == payload.UserID {
				addWay = item.AddWay
				remark = item.Remark
				description = item.Description
				break
			}
		}
		if err = slf.tx.Transaction(ctx, func(ctx context.Context) error {
			if err = slf.qwRepository.UpsertContact(ctx, &model.QwContact{
				UserID:         appUserID,
				ExternalUserID: payload.ExternalUserID,
				UnionID:        resp.ExternalContact.UnionID,
				Name:           resp.ExternalContact.Name,
				Avatar:         resp.ExternalContact.Avatar,
				Gender:         resp.ExternalContact.Gender,
			}); err != nil {
				return err
			}
			follow, err := slf.qwRepository.FetchContactFollowByExternalUserIDAndUserID(ctx, payload.ExternalUserID, payload.UserID)
			if err != nil {
				return err
			}
			followEvent := model.QwContactFollowEvent{
				Type:     1,
				Time:     addTime,
				AddWay:   addWay,
				AddState: payload.State,
			}
			if follow == nil {
				follow = &model.QwContactFollow{
					UserID:         appUserID,
					ExternalUserID: payload.ExternalUserID,
					UnionID:        resp.ExternalContact.UnionID,
					QwUserID:       payload.UserID,
					AddWay:         addWay,
					AddTime:        addTime,
					AddState:       payload.State,
					Remark:         remark,
					Description:    description,
					DelFlag:        0,
					DelTime:        0,
					DelSource:      "",
					Events:         []model.QwContactFollowEvent{followEvent},
				}
			} else {
				follow.AddWay = addWay
				follow.AddTime = addTime
				follow.AddState = payload.State
				follow.Remark = remark
				follow.Description = description
				follow.DelFlag = 0
				follow.DelTime = 0
				follow.DelSource = ""
				follow.DelFlag = 0
				follow.DelTime = 0
				follow.DelSource = ""
				follow.Events = append(follow.Events, followEvent)
			}
			return slf.qwRepository.UpsertContactFollow(ctx, follow)
		}); err != nil {
			return errors.Wrap(err, "企微客户入库失败")
		}
		if bindingUserID {
			if err := func() error {
				var qwRemark = resp.ExternalContact.Name
				if len([]rune(qwRemark)) > 10 {
					qwRemark = string([]rune(qwRemark)[:10])
				}
				qwRemark += "_"
				premiumInfo, err := slf.userRepo.FetchPremiumInfoByUserID(ctx, appUserID, "application_horoscope")
				if err != nil {
					return err
				}
				if premiumInfo == nil {
					qwRemark += "N"
				} else {
					isPaid, err := slf.orderRepo.IsPaidUser(ctx, appUserID)
					if err != nil {
						return err
					}
					if !isPaid {
						qwRemark += "V" // 无付费
					} else {
						qwRemark += "FV" // 付费
					}
					qwRemark += premiumInfo.ExpireTime.Format("060102")
				}
				for _, user := range resp.FollowUsers {
					if _, err = slf.doraemon.ExternalContact.Remark(ctx, &externalContactRequest.RequestExternalContactRemark{
						UserID:         user.UserID,
						ExternalUserID: payload.ExternalUserID,
						Remark:         qwRemark,
						Description:    appUserID,
					}); err != nil {
						slf.logger.WithContext(ctx).Error("设置企业微信备注失败", zap.Error(err))
					}
				}
				return nil
			}(); err != nil {
				slf.logger.WithContext(ctx).Error("设置企业微信备注失败", zap.Error(err))
			}
		}
		return nil
	case models.CALLBACK_EVENT_CHANGE_TYPE_DEL_EXTERNAL_CONTACT: // 删除外部联系人事件
		payload := models.EventExternalUserDel{}
		if err := event.ReadMessage(&payload); err != nil {
			return errors.Wrap(err, "解析删除外部联系人事件数据失败")
		}
		delTime, err := strconv.ParseInt(payload.CreateTime, 10, 64)
		if err != nil {
			return errors.Wrap(err, "解析被客户删除时间失败")
		}
		delFlag := lo.Ternary(payload.Source == "DELETE_BY_TRANSFER", 3, 2)
		if err = slf.tx.Transaction(ctx, func(ctx context.Context) error {
			follow, err := slf.qwRepository.FetchContactFollowByExternalUserIDAndUserID(ctx, payload.ExternalUserID, payload.UserID)
			if err != nil {
				return err
			}
			if follow == nil {
				return nil
			}
			followEvent := model.QwContactFollowEvent{
				Type:      2,
				Time:      delTime,
				DelFlag:   delFlag,
				DelSource: payload.Source,
			}
			follow.DelFlag = delFlag
			follow.DelTime = delTime
			follow.DelSource = payload.Source
			follow.Events = append(follow.Events, followEvent)
			return slf.qwRepository.UpsertContactFollow(ctx, follow)
		}); err != nil {
			return err
		}
		return nil
	case models.CALLBACK_EVENT_CHANGE_TYPE_DEL_FOLLOW_USER: // 删除跟进人员事件（被客户删除）
		payload := models.EventExternalUserDelFollowUser{}
		if err := event.ReadMessage(&payload); err != nil {
			return errors.Wrap(err, "解析删除跟进人员事件数据失败")
		}
		delTime, err := strconv.ParseInt(payload.CreateTime, 10, 64)
		if err != nil {
			return errors.Wrap(err, "解析被客户删除时间失败")
		}
		delFlag := 1
		return slf.tx.Transaction(ctx, func(ctx context.Context) error {
			follow, err := slf.qwRepository.FetchContactFollowByExternalUserIDAndUserID(ctx, payload.ExternalUserID, payload.UserID)
			if err != nil {
				return err
			}
			if follow == nil {
				return nil
			}
			followEvent := model.QwContactFollowEvent{
				Type:    2,
				Time:    delTime,
				DelFlag: delFlag,
			}
			follow.DelFlag = delFlag
			follow.DelTime = delTime
			follow.Events = append(follow.Events, followEvent)
			return slf.qwRepository.UpsertContactFollow(ctx, follow)
		})
	case models.CALLBACK_EVENT_CHANGE_TYPE_EDIT_EXTERNAL_CONTACT: // 编辑外部联系人事件
		payload := models.EventExternalUserEdit{}
		if err := event.ReadMessage(&payload); err != nil {
			return errors.Wrap(err, "解析编辑外部联系人事件数据失败")
		}
		resp, err := slf.doraemon.ExternalContact.Get(ctx, payload.ExternalUserID, "")
		if err != nil {
			return errors.Wrap(err, "获取外部联系人失败")
		}
		if resp == nil {
			return nil
		}
		if resp.ErrCode != 0 {
			return errors.Errorf("获取外部联系人失败：code=%d, msg=%s", resp.ErrCode, resp.ErrMSG)
		}
		var (
			remark, description string
		)
		for _, user := range resp.FollowUsers {
			if user.UserID == payload.UserID {
				remark = user.Remark
				description = user.Description
				break
			}
		}
		return slf.tx.Transaction(ctx, func(ctx context.Context) error {
			qc, err := slf.qwRepository.FetchContactByExternalUserID(ctx, payload.ExternalUserID)
			if err != nil {
				return err
			}
			if qc == nil {
				return nil
			}
			qc.Name = resp.ExternalContact.Name
			qc.Avatar = resp.ExternalContact.Avatar
			qc.UnionID = resp.ExternalContact.UnionID
			qc.Gender = resp.ExternalContact.Gender
			if err = slf.qwRepository.UpsertContact(ctx, qc); err != nil {
				return err
			}
			follow, err := slf.qwRepository.FetchContactFollowByExternalUserIDAndUserID(ctx, payload.ExternalUserID, payload.UserID)
			if err != nil {
				return err
			}
			if follow == nil {
				return nil
			}
			follow.Remark = remark
			follow.Description = description
			return slf.qwRepository.UpsertContactFollow(ctx, follow)
		})
	default:
		return nil
	}
}
