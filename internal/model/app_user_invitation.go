package model

import (
	"github.com/uptrace/bun"
	"time"
)

/*
CREATE TABLE `invite_code` (
    `id` BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `user_id` VARCHAR(255) NOT NULL,
    `code` VARCHAR(255) NOT NULL,
    created_at datetime     default CURRENT_TIMESTAMP     not null,
    updated_at datetime     default CURRENT_TIMESTAMP     not null,
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE `invite_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `inviter_id` VARCHAR(255) NOT NULL,
    `inviter_phone` VARCHAR(255) NOT NULL,
    `inviter_vip_duration` BIGINT DEFAULT 0,
    `invitee_id` VARCHAR(255) NOT NULL,
    `invitee_phone` VARCHAR(255) NOT NULL,
    `invitee_vip_duration` BIGINT DEFAULT 0,
    `invite_code` VARCHAR(255) NOT NULL,
    `invite_time` DATETIME(6),
    created_at datetime     default CURRENT_TIMESTAMP     not null,
    updated_at datetime     default CURRENT_TIMESTAMP     not null,
  UNIQUE KEY `idx_invite_relation` (`inviter_id`, `invitee_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
*/

type InviteCode struct {
	bun.BaseModel `bun:"table:invite_code,alias:ic"`
	ID            int64  `bun:"id,pk,autoincrement"`
	UserID        string `bun:"user_id,notnull"`
	Code          string `bun:"code,notnull,unique:idx_code"` // 邀请码
	Level         int    `json:"level"`                       // 邀请码等级
	BaseFields4Time
}

/*
create table `invite_level` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `level` int NOT NULL,
    `inviter_gift_vip_hours` bigint NOT NULL,
    `invitee_gift_vip_hours` bigint NOT NULL,
    created_at datetime     default CURRENT_TIMESTAMP     not null,
    updated_at datetime     default CURRENT_TIMESTAMP     not null,
    PRIMARY KEY (`id`),
	INDEX level (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
*/

type InviteLevel struct {
	bun.BaseModel       `bun:"table:invite_level,alias:il"`
	ID                  int64 `bun:"id,pk,autoincrement"`
	Level               int   `bun:"level,notnull,unique:idx_level"` // 邀请码等级
	InviterGiftVipHours int64 `bun:"inviter_gift_vip_hours,notnull"` // 邀请人VIP时长（单位：小时）
	InviteeGiftVipHours int   `bun:"invitee_gift_vip_hours,notnull"` // 被邀请人VIP时长（单位：小时）
	BaseFields4Time
}

type InviteRecord struct {
	bun.BaseModel      `bun:"table:invite_record,alias:ir"`
	ID                 int64     `bun:"id,pk,autoincrement"`
	InviterID          string    `bun:"inviter_id,notnull,unique:idx_invite_relation"` // 邀请人ID
	InviterPhone       string    `bun:"inviter_phone,notnull"`                         // 邀请人手机号（仅用于展示）
	InviterVipDuration int64     `bun:"inviter_vip_duration"`                          // 邀请人VIP时长
	InviterRead        bool      `bun:"inviter_read,notnull,default:false"`            // 邀请人是否已读
	InviteeID          string    `bun:"invitee_id,notnull,unique:idx_invite_relation"` // 被邀请人ID
	InviteePhone       string    `bun:"invitee_phone,notnull"`                         // 被邀请人手机号（仅用于展示）
	InviteeVipDuration int64     `bun:"invitee_vip_duration"`                          // 被邀请人VIP时长
	InviteeRead        bool      `bun:"invitee_read,notnull,default:false"`            // 被邀请人是否已读
	InviteCode         string    `bun:"invite_code,notnull"`                           // 邀请码
	InviteTime         time.Time `bun:"invite_time"`                                   // 邀请时间
	BaseFields4Time
}

/*
create table `invite_binding_ip` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `invite_code` varchar(255) NOT NULL,
    `ip` varchar(255) NOT NULL,
    `expire_time` datetime NOT NULL,
    created_at datetime     default CURRENT_TIMESTAMP     not null,
    updated_at datetime     default CURRENT_TIMESTAMP     not null,
    PRIMARY KEY (`id`),
	INDEX invite_code (`invite_code`),
	INDEX ip (`ip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
*/

type InviteBindingIP struct {
	bun.BaseModel `bun:"table:invite_binding_ip,alias:ibi"`
	ID            int64     `bun:"id,pk,autoincrement"` // 主键ID
	InviteCode    string    `bun:"invite_code,notnull"` // 邀请码
	IP            string    `bun:"ip,notnull"`          // IP
	ExpireTime    time.Time `bun:"expire_time"`         // 过期时间（三天时间）
	BaseFields4Time
}

func (slf *InviteBindingIP) IsExpired() bool {
	return slf.ExpireTime.Before(time.Now())
}
