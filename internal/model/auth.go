package model

import (
	"github.com/uptrace/bun"
	"time"
)

type VerificationCode struct {
	bun.BaseModel `bun:"verification_code,alias:vc"`
	ID            int64     `bun:"id,pk"`                                      // ID
	Code          string    `bun:"code"`                                       // 验证码
	Type          string    `bun:"type"`                                       // 类型：phone、email
	Scene         string    `bun:"scene"`                                      // 场景：login
	Dest          string    `bun:"dest"`                                       // 目标：手机号码、邮箱地址
	IsUsed        bool      `bun:"is_used"`                                    // 是否已使用
	ExpireTime    time.Time `bun:"expire_time"`                                // 过期时间
	DeletedAt     time.Time `bun:",soft_delete,default:'0001-01-01 00:00:00'"` // 删除时间
	BaseFields4Time
}

/*
create table `verification_code` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code` varchar(255) NOT NULL COMMENT '验证码',
    `type` varchar(255) NOT NULL COMMENT '类型：phone、email',
    `scene` varchar(255) NOT NULL COMMENT '场景：login',
    `dest` varchar(255) NOT NULL COMMENT '目标：手机号码、邮箱地址',
    `is_used` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已使用',
    `expire_time` datetime NOT NULL COMMENT '过期时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '删除时间',
    PRIMARY KEY (`id`),
	INDEX code (`code`),
	INDEX type (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='验证码';
*/
