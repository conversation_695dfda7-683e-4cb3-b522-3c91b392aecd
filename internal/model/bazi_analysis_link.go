package model

import "github.com/uptrace/bun"

/*
create table `bazi_analysis_link` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `bazi` json DEFAULT NULL COMMENT '八字: 姓名、性别、出生时间（公历）、出生地',
    `phone` varchar(255) DEFAULT NULL COMMENT '手机号',
    `add_link` varchar(255) DEFAULT NULL COMMENT '添加链接',
    `add_state` varchar(255) DEFAULT NULL COMMENT '添加状态',
    `order_id` bigint(20) DEFAULT NULL COMMENT '订单id',
    `ip` varchar(255) DEFAULT NULL COMMENT 'ip',
    `ua` varchar(500) DEFAULT NULL COMMENT 'ua',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `add_state` (`add_state`)
) ENGINE=InnoDB CHARSET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '八字测算链接';
*/

// BaziAnalysisLink 八字测算链接
type BaziAnalysisLink struct {
	bun.BaseModel `bun:"bazi_analysis_link,alias:bal"`
	ID            int64                `bun:"id,pk" json:"id"`
	Bazi          BaziAnalysisLinkBazi `bun:"bazi" json:"bazi"`
	Phone         string               `bun:"phone" json:"phone"`
	AddLink       string               `bun:"add_link" json:"add_link"`
	AddState      string               `bun:"add_state" json:"add_state"`
	OrderID       int64                `bun:"order_id" json:"order_id"`
	IP            string               `bun:"ip" json:"ip"`
	UA            string               `bun:"ua" json:"ua"`
	BaseFields4Time
}
type BaziAnalysisLinkBazi struct {
	Name       string   `json:"name"`
	Gender     int      `json:"gender"`
	Birthtime  string   `json:"birthtime"`
	Birthplace []string `json:"birthplace"`
}
