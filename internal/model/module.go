package model

import "github.com/uptrace/bun"

// Module 模块
type Module struct {
	bun.BaseModel `bun:"table:module,alias:m"`
	ID            int64  `bun:"id,pk,autoincrement"`
	Name          string `bun:",notnull,unique:idx_module_code_deleted_at"`
	Sort          int    `bun:",notnull,default:1"`
	IsEnabled     bool   `bun:",notnull,default:true"`
	DeletedAt     string `bun:",soft_delete,unique:idx_module_code_deleted_at,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}
