package model

import (
	"github.com/uptrace/bun"
	"time"
)

type Feedback struct {
	bun.BaseModel `bun:"feedback,alias:fb" json:"-"`

	ID          int64    `bun:"id,pk,autoincrement" json:"id,omitempty"`
	UserID      string   `bun:"user_id" json:"userId,omitempty"`
	Application string   `bun:"application" json:"application,omitempty"`
	Content     string   `bun:"content" json:"content,omitempty"`
	ImageKey    []string `bun:"image_key" json:"imageKey,omitempty"`
	ImageKeyUrl []string `bun:"-" json:"imageKeyUrl,omitempty"`
	//ReplyID     int64    `bun:"reply_id" json:"replyId,omitempty"`

	ReplyContent  string    `bun:"reply_content" json:"replyContent,omitempty"`
	ReplyImageKey []string  `bun:"reply_image_key" json:"replyImageKey,omitempty"`
	ReplyImageUrl []string  `bun:"-" json:"replyImageUrl,omitempty"`
	ReplyTime     time.Time `bun:"reply_time" json:"replyTime,omitempty"`

	BaseFields4Time
}

type FeedbackList struct {
	Feedback     `json:"feedback"`
	ReplyContent []Feedback `json:"replyContent"`
}
