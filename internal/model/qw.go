package model

import (
	"github.com/uptrace/bun"
	"time"
)

/*
CREATE TABLE qw_contact_way (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,  -- 主键ID
    app_id BIGINT NOT NULL,                -- 应用ID
    platform_id BIGINT NOT NULL,           -- 平台ID
    name VARCHAR(255) NOT NULL,            -- 企微联系我的配置名称
    link TEXT,                             -- 企微联系我的链接
    user_ids JSON,                         -- 企微联系我的使用成员（数组，使用 JSON 存储）
    config_id VARCHAR(255),                -- 企微联系我的配置ID
    skip_verify BOOLEAN DEFAULT FALSE,     -- 企微联系我是否跳过验证
    add_state VARCHAR(255),                -- 企微联系我的添加参数
    deleted_at DATETIME NOT NULL DEFAULT '0001-01-01 00:00:00', -- 软删除标记
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    UNIQUE KEY idx_app_platform_add_state_deleted_at (app_id, platform_id, add_state, deleted_at)
) engine=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

*/

// QwContactWay 企微联系我
type QwContactWay struct {
	bun.BaseModel `bun:"qw_contact_way,alias:qcw"`
	ID            int64     `bun:"id,pk,autoincrement"` // 主键ID
	Type          int       `bun:"type"`                // 类型：1-联系我、2-获客链接
	AppID         int64     `bun:"app_id"`              // 应用ID
	PlatformID    int64     `bun:"platform_id"`         // 平台ID
	Name          string    `bun:"name"`                // 企微联系我的配置名称
	Link          string    `bun:"link"`                // 企微联系我的链接
	UserIDs       []string  `bun:"user_ids"`            // 企微联系我的使用成员
	ConfigID      string    `bun:"config_id"`           // 企微联系我的配置ID
	SkipVerify    bool      `bun:"skip_verify"`         // 企微联系我是否跳过验证
	AddState      string    `bun:"add_state"`           // 企微联系我的添加参数
	DeletedAt     time.Time `bun:",soft_delete,unique:idx_app_platform_add_state_deleted_at,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}

/*
CREATE TABLE qw_bill (
	id BIGINT PRIMARY KEY AUTO_INCREMENT,  -- 主键ID
  	transaction_id VARCHAR(255) NOT NULL,  -- 交易单号
	bill_type tinyint NOT NULL,             -- 交易类型：0-收款记录 1-退款记录
	trade_state tinyint NOT NULL,           -- 交易状态（退款记录不返回该字段）：1-已完成 3-已完成有退款
	pay_time bigint NOT NULL,               -- 支付时间
	out_trade_no VARCHAR(255) NOT NULL,     -- 商户单号（退款记录返回对应收款记录的商户单号）
	out_refund_no VARCHAR(255),             -- 商户退款单号（仅退款记录返回该字段）
	external_userid VARCHAR(255) NOT NULL,  -- 付款人的userid
	total_fee bigint NOT NULL,               -- 收款总金额（单位：分）
	payee_userid VARCHAR(255) NOT NULL,     -- 收款成员/退款成员的userid
	payment_type tinyint NOT NULL,           -- 收款方式：0-聊天中收款 1-收款码收款 2-直播间收款 3-产品图册收款 4-转账 5-小程序
	mch_id VARCHAR(255) NOT NULL,            -- 收款商户号id
	remark VARCHAR(255),                     -- 收款/退款备注
	commodity_list JSON,                     -- 商品信息详情列表（仅收款记录返回）
	total_refund_fee bigint,                 -- 退款总金额（单位：分）
	refund_list JSON,                        -- 退款单据详情列表（仅收款记录返回）
	contact_info JSON,                       -- 联系人信息（如创建收款项目时设置为不需要联系地址，则该字段为空，退款记录不返回该字段）
	miniprogram_info JSON,                   -- 小程序信息（收款方式为小程序时返回该字段）
	deleted_at DATETIME NOT NULL DEFAULT '0001-01-01 00:00:00', -- 软删除标记
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
) engine=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
*/

type (
	QwBillCommodity struct {
		Description string `json:"description"` // 商品描述
		Amount      int    `json:"amount"`      // 商品数量
	}
	QwBillRefund struct {
		OutRefundNo   string `json:"out_refund_no"`  // 退款单号
		RefundUserid  string `json:"refund_userid"`  // 退款发起人ID
		RefundComment string `json:"refund_comment"` // 退款备注
		RefundReqtime int    `json:"refund_reqtime"` // 退款发起时间
		RefundStatus  int    `json:"refund_status"`  // 退款状态：0-已申请退款 1-退款处理中 2-退款成功 3-退款关闭 4-退款异常 5-审批中 6-审批失败 7-审批取消
		RefundFee     int    `json:"refund_fee"`     // 退款金额（单位：分）
	}
	QwBillContact struct {
		Name    string `json:"name"`    // 联系人姓名
		Phone   string `json:"phone"`   // 联系人电话
		Address string `json:"address"` // 联系人地址
	}
	QwBillMiniProgram struct {
		AppID string `json:"appid"` // 小程序appid
		Name  string `json:"name"`  // 小程序名称
	}
	QwBill struct {
		bun.BaseModel   `bun:"qw_bill,alias:qb"`
		ID              int64             `bun:"id,pk,autoincrement" json:"id"`            // 主键ID
		TransactionID   string            `bun:"transaction_id" json:"transaction_id"`     // 交易单号
		BillType        int               `bun:"bill_type" json:"bill_type"`               // 交易类型：0-收款记录 1-退款记录
		TradeState      int               `bun:"trade_state" json:"trade_state"`           // 交易状态（退款记录不返回该字段）：1-已完成 3-已完成有退款
		PayTime         int               `bun:"pay_time" json:"pay_time"`                 // 支付时间
		OutTradeNo      string            `bun:"out_trade_no" json:"out_trade_no"`         // 商户单号（退款记录返回对应收款记录的商户单号）
		OutRefundNo     string            `bun:"out_refund_no" json:"out_refund_no"`       // 商户退款单号（仅退款记录返回该字段）
		ExternalUserID  string            `bun:"external_userid" json:"external_userid"`   // 付款人的userid
		TotalFee        int               `bun:"total_fee" json:"total_fee"`               // 收款总金额（单位：分）
		PayeeUserID     string            `bun:"payee_userid" json:"payee_userid"`         // 收款成员/退款成员的userid
		PaymentType     int               `bun:"payment_type" json:"payment_type"`         // 收款方式：0-聊天中收款 1-收款码收款 2-直播间收款 3-产品图册收款 4-转账 5-小程序
		MchID           string            `bun:"mch_id" json:"mch_id"`                     // 收款商户号id
		Remark          string            `bun:"remark" json:"remark"`                     // 收款/退款备注
		CommodityList   []QwBillCommodity `bun:"commodity_list" json:"commodity_list"`     // 商品信息详情列表（仅收款记录返回）
		TotalRefundFee  int               `bun:"total_refund_fee" json:"total_refund_fee"` // 退款总金额（单位：分）
		RefundList      []QwBillRefund    `bun:"refund_list" json:"refund_list"`           // 退款单据详情列表（仅收款记录返回）
		ContactInfo     QwBillContact     `bun:"contact_info" json:"contact_info"`         // 联系人信息（如创建收款项目时设置为不需要联系地址，则该字段为空，退款记录不返回该字段）
		MiniProgramInfo QwBillMiniProgram `bun:"miniprogram_info" json:"miniprogram_info"` // 小程序信息（收款方式为小程序时返回该字段）
	}
)

type (
	/*
		CREATE TABLE qw_contact (
		    id BIGINT PRIMARY KEY AUTO_INCREMENT,  -- 主键ID
		    external_userid VARCHAR(255) NOT NULL, -- 外部联系人的userid
		    union_id VARCHAR(255) NOT NULL,        -- 外部联系人的unionid
		    name VARCHAR(255) NOT NULL,            -- 外部联系人的名称
		    avatar VARCHAR(255),                   -- 外部联系人的头像
		    gender tinyint,                        -- 外部联系人的性别
		    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
		    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
		    deleted_at DATETIME NOT NULL DEFAULT '0001-01-01 00:00:00', -- 软删除标记,
		    unique key idx_external_userid_deleted_at (external_userid, deleted_at)
		) engine=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
	*/
	QwContact struct {
		bun.BaseModel  `bun:"qw_contact,alias:qc"`
		ID             int64     `bun:"id,pk,autoincrement" json:"id"`          // 主键ID
		UserID         string    `bun:"user_id" json:"user_id"`                 // 用户ID
		ExternalUserID string    `bun:"external_userid" json:"external_userid"` // 外部联系人的userid
		UnionID        string    `bun:"union_id" json:"union_id"`               // 外部联系人的unionid
		Name           string    `bun:"name" json:"name"`                       // 外部联系人的名称
		Avatar         string    `bun:"avatar" json:"avatar"`                   // 外部联系人的头像
		Gender         int       `bun:"gender" json:"gender"`                   // 外部联系人的性别
		Phone          string    `bun:"phone" json:"phone"`                     // 外部联系人的手机号
		RealName       string    `bun:"real_name" json:"real_name"`             // 外部联系人的真实姓名
		DeletedAt      time.Time `bun:",soft_delete,unique:idx_external_userid_deleted_at,default:'0001-01-01 00:00:00'"`
		BaseFields4Time
	}
	/*
		CREATE TABLE qw_contact_follow (
		    id BIGINT PRIMARY KEY AUTO_INCREMENT,  -- 主键ID
		    external_userid VARCHAR(255) NOT NULL, -- 外部联系人的userid
		    union_id VARCHAR(255) NOT NULL,        -- 外部联系人的unionid
		    user_id VARCHAR(255) NOT NULL,         -- 企微员工的userid
		    add_way tinyint NOT NULL,              -- 添加方式
		    add_time int NOT NULL,                 -- 添加时间
		    add_state VARCHAR(255) NOT NULL,       -- 添加参数
		    remark VARCHAR(255),                   -- 备注
		    description VARCHAR(512),              -- 描述
		    del_flag tinyint,                      -- 删除标记：0-未删除 1-被删除 2-主动删除 3-自动删除（接替）
		    del_time int,                          -- 删除时间
		    del_source VARCHAR(255),               -- 删除来源
		    events json,                           -- 事件列表：添加、删除事件
		    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
		    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
		    deleted_at DATETIME NOT NULL DEFAULT '0001-01-01 00:00:00', -- 软删除标记
		    unique key idx_external_userid_user_id_deleted_at (external_userid, user_id, deleted_at)
		) engine=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
	*/
	QwContactFollowEvent struct {
		Type      int    `json:"type"`                 // 事件类型：1-添加 2-删除
		Time      int64  `json:"time"`                 // 事件时间
		AddWay    int    `json:"add_way,omitempty"`    // 添加方式
		AddState  string `json:"add_state,omitempty"`  // 添加参数
		DelFlag   int    `json:"del_flag,omitempty"`   // 删除标记：0-未删除 1-被删除 2-主动删除 3-自动删除（接替）
		DelSource string `json:"del_source,omitempty"` // 删除来源
	}
	QwContactFollow struct {
		bun.BaseModel  `bun:"qw_contact_follow,alias:qcf"`
		ID             int64                  `bun:"id,pk,autoincrement" json:"id"`          // 主键ID
		UserID         string                 `bun:"user_id" json:"user_id"`                 // 用户ID
		ExternalUserID string                 `bun:"external_userid" json:"external_userid"` // 外部联系人的userid
		UnionID        string                 `bun:"union_id" json:"union_id"`               // 外部联系人的unionid
		QwUserID       string                 `bun:"qw_user_id" json:"qw_user_id"`           // 企微员工的userid
		AddWay         int                    `bun:"add_way" json:"add_way"`                 // 添加方式
		AddTime        int64                  `bun:"add_time" json:"add_time"`               // 添加时间
		AddState       string                 `bun:"add_state" json:"add_state"`             // 添加参数
		Remark         string                 `bun:"remark" json:"remark"`                   // 备注
		Description    string                 `bun:"description" json:"description"`         // 描述
		DelFlag        int                    `bun:"del_flag" json:"del_flag"`               // 删除标记：0-未删除 1-被删除 2-主动删除 3-自动删除（接替）
		DelTime        int64                  `bun:"del_time" json:"del_time"`               // 删除时间
		DelSource      string                 `bun:"del_source" json:"del_source"`           // 删除来源
		Events         []QwContactFollowEvent `bun:"events" json:"events"`                   // 事件列表：添加/删除事件
		DeletedAt      time.Time              `bun:",soft_delete,unique:idx_external_userid_user_id_deleted_at,default:'0001-01-01 00:00:00'"`
		BaseFields4Time
	}
)
