package model

import "github.com/uptrace/bun"

/*
create table `app_user_qw_kefu` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `user_id` varchar(36) NOT NULL COMMENT '用户ID',
    `add_state` varchar(14) NOT NULL COMMENT '添加参数',
    `type` int NOT NULL COMMENT '类型：1-客服（联系我），2-会员专属（获客链接）',
    `qw_contact_way_id` bigint NOT NULL COMMENT '企微联系ID',
    `qw_user_ids` json NOT NULL COMMENT '企微客服人员ID列表',
    `qw_user_ids_hashed` varchar(100) NOT NULL COMMENT '企微客服人员ID哈希值',
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX user_id (`user_id`),
    INDEX type (`type`),
    INDEX qw_contact_way_id (`qw_contact_way_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户企微客服关系表';

*/

type AppUserQwKefu struct {
	bun.BaseModel  `bun:"app_user_qw_kefu,alias:auqk"`
	ID             int64    `bun:"id,pk,autoincrement"`
	UserID         string   `bun:"user_id,notnull"`
	AddState       string   `bun:"add_state,notnull"`
	Type           int      `bun:"type,notnull"`
	QwContactWayID int64    `bun:"qw_contact_way_id,notnull"`
	QwUserIDs      []string `bun:"qw_user_ids,notnull"`

	BaseFields4Time
}
