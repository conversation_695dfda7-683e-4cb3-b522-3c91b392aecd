package server

import (
	"context"
	"github.com/go-co-op/gocron"
	"go.uber.org/zap"
	"time"
	"zodiacus/pkg/log"
)

type Task struct {
	log       *log.Logger
	scheduler *gocron.Scheduler
}

func NewTask(log *log.Logger) *Task {
	return &Task{
		log: log,
	}
}

func (slf *Task) Start(_ context.Context) error {
	gocron.SetPanicHandler(func(jobName string, recoverData interface{}) {
		slf.log.Error("Task Panic", zap.String("job", jobName), zap.Any("recover", recoverData))
	})

	//t.scheduler = gocron.NewScheduler(time.UTC) // UTC时区
	slf.scheduler = gocron.NewScheduler(time.FixedZone("PRC", 8*60*60)) // +8时区

	//if _, err := slf.scheduler.CronWithSeconds("0/3 * * * * *").Do(func() {
	//	slf.log.Info("I'm a Task1.")
	//}); err != nil {
	//	slf.log.Error("Task1 error", zap.Error(err))
	//}
	//
	//if _, err := slf.scheduler.Every("3s").Do(func() {
	//	slf.log.Info("I'm a Task2.")
	//}); err != nil {
	//	slf.log.Error("Task2 error", zap.Error(err))
	//}

	slf.scheduler.StartBlocking()
	return nil
}
func (slf *Task) Stop(_ context.Context) error {
	slf.scheduler.Stop()
	slf.log.Info("Task stop...")
	return nil
}
