package server

import (
	"zodiacus/docs"
	"zodiacus/internal/handler/cms"
	"zodiacus/internal/middleware"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/pkg/server/http"
	"zodiacus/third_party/casdoor"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func NewAtlasServer(logger *log.Logger, conf *viper.Viper,
	appUserHandler *cms.AppUserHandler,
	mingliRuleHandler *cms.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	mingliRuleSettingHandler *cms.MingliRuleConditionHandler,
	paipanRecordHandler *cms.PaipanRecordHandler,
	enumsHandler *cms.EnumsHandler,
	termHandler *cms.<PERSON>rm<PERSON>and<PERSON>,
	wecomHandler *cms.<PERSON><PERSON><PERSON><PERSON><PERSON>,
	offiaccountHandler *cms.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	userMingliHandler *cms.<PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	orderHandler *cms.<PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	mpHandler *cms.MiniProgramHandler,
	acHandler *cms.AppChannelHandler,
	avHandler *cms.AppVersionHandler,
	portalHandler *cms.PortalHandler,
	balHandler *cms.BaziAnalysisLinkHandler,
	submailHandler *cms.SubMailHandler,
	feedbackHandler *cms.FeedbackHandler,
	identity *casdoor.Client,
	jh *jwthub.Jwthub,
) *http.Server {
	if conf.GetString("env") == "prod" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}
	host, port := conf.GetString("http.cms.host"), conf.GetInt("http.cms.port")
	srv := http.NewServer(
		gin.Default(),
		logger,
		http.WithServerHost(host),
		http.WithServerPort(port),
	)

	// swagger doc
	docs.SwaggerInfocms.BasePath = "/v1"
	srv.GET("/swagger/*any", ginSwagger.WrapHandler(
		swaggerfiles.Handler,
		ginSwagger.DefaultModelsExpandDepth(-1),
		ginSwagger.PersistAuthorization(true),
		ginSwagger.InstanceName("cms"),
	))

	srv.Use(
		middleware.CORSMiddleware(),
		middleware.ResponseLogMiddleware(logger),
		middleware.RequestLogMiddleware(logger),
	)

	v1Group := srv.Group("/v1")
	{
		// 强制鉴权
		required := v1Group.Group("/")
		required.Use(middleware.StrictAuth(identity, jh, logger))
		// 可选鉴权
		optional := v1Group.Group("/")
		optional.Use(middleware.NoStrictAuth(identity, jh, logger))
		// 无需鉴权
		nameless := v1Group.Group("/")

		// 术语模块
		termHandler.Handle(required, optional, nameless)
		// 命理规则模块
		mingliRuleHandler.Handle(required, optional, nameless)
		// 命理规则设置模块
		mingliRuleSettingHandler.Handle(required, optional, nameless)
		// 排盘记录模块
		paipanRecordHandler.Handle(required, optional, nameless)
		// 枚举模块
		enumsHandler.Handle(required, optional, nameless)
		// 企业微信模块
		wecomHandler.Handle(required, optional, nameless)
		// 微信公众号模块
		offiaccountHandler.Handle(required, optional, nameless)
		// 用户命例模块
		userMingliHandler.Handle(required, optional, nameless)
		// 用户订单模块
		orderHandler.Handle(required, optional, nameless)
		// 用户模块
		appUserHandler.Handle(required, optional, nameless)
		// 小程序模块
		mpHandler.Handle(required, optional, nameless)
		// 渠道模块
		acHandler.Handle(required, optional, nameless)
		// 版本模块
		avHandler.Handle(required, optional, nameless)
		// 门户模块
		portalHandler.Handle(required, optional, nameless)
		// 测算模块
		balHandler.Handle(required, optional, nameless)
		// 短信模块
		submailHandler.Handle(required, optional, nameless)
		// 反馈模块
		feedbackHandler.Handle(required, optional, nameless)
	}

	return srv
}
