package server

import (
	"zodiacus/docs"
	"zodiacus/internal/handler/authcenter"
	"zodiacus/internal/middleware"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/pkg/server/http"
	"zodiacus/third_party/casdoor"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func NewAuthCenterServer(logger *log.Logger, conf *viper.Viper,
	authCenterHandler *authcenter.AuthCenterHandler,
	identity *casdoor.Client,
	jh *jwthub.Jwthub,
) *http.Server {
	if conf.GetString("env") == "prod" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}
	host, port := conf.GetString("http.authcenter.host"), conf.GetInt("http.authcenter.port")
	srv := http.NewServer(
		gin.Default(),
		logger,
		http.WithServerHost(host),
		http.WithServerPort(port),
	)

	// swagger doc
	docs.SwaggerInfoauthcenter.BasePath = "/v1"
	srv.GET("/swagger/*any", ginSwagger.WrapHandler(
		swaggerfiles.Handler,
		ginSwagger.DefaultModelsExpandDepth(-1),
		ginSwagger.PersistAuthorization(true),
		ginSwagger.InstanceName("authcenter"),
	))

	srv.Use(
		middleware.CORSMiddleware(),
		middleware.ResponseLogMiddleware(logger),
		middleware.RequestLogMiddleware(logger),
	)

	v1Group := srv.Group("/v1")
	{
		// 强制鉴权
		required := v1Group.Group("/")
		required.Use(middleware.StrictAuth(identity, jh, logger))
		// 可选鉴权
		optional := v1Group.Group("/")
		optional.Use(middleware.NoStrictAuth(identity, jh, logger))
		// 无需鉴权
		nameless := v1Group.Group("/")

		// 术语模块
		authCenterHandler.Handle(required, optional, nameless)
	}

	return srv
}
