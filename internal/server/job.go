package server

import (
	"context"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"zodiacus/pkg/log"
	"zodiacus/pkg/pubsub"
)

type Job struct {
	log       *log.Logger
	rdb       *redis.Client
	pubsub    *pubsub.PubSub
	publisher *pubsub.MessagePublisher
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
}

func NewJob(
	log *log.Logger,
	rdb *redis.Client,
) *Job {
	return &Job{
		log:       log,
		rdb:       rdb,
		pubsub:    pubsub.NewPubSub(rdb, log),
		publisher: pubsub.NewMessagePublisher(rdb, log),
	}
}

func (slf *Job) Start(ctx context.Context) error {
	slf.ctx, slf.cancel = context.WithCancel(ctx)

	slf.log.Info("Starting job consumers...")

	// 启动会员时长变更消费者
	slf.wg.Add(1)
	go slf.startMembershipChangeConsumer()

	slf.log.Info("All job consumers started successfully")
	return nil
}

func (slf *Job) Stop(ctx context.Context) error {
	slf.log.Info("Stopping job consumers...")

	if slf.cancel != nil {
		slf.cancel()
	}

	// 等待所有goroutine结束，最多等待30秒
	done := make(chan struct{})
	go func() {
		slf.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		slf.log.Info("All job consumers stopped successfully")
	case <-time.After(30 * time.Second):
		slf.log.Warn("Job consumers stop timeout")
	}

	return nil
}

// startMembershipChangeConsumer 启动会员时长变更消费者
func (slf *Job) startMembershipChangeConsumer() {
	defer slf.wg.Done()

	handler := pubsub.NewSimpleMembershipChangeHandler(slf.log)

	slf.log.Info("Starting membership change consumer")

	for {
		select {
		case <-slf.ctx.Done():
			slf.log.Info("Membership change consumer stopped")
			return
		default:
			err := slf.pubsub.SubscribeMembershipChange(slf.ctx, handler)
			if err != nil && err != context.Canceled {
				slf.log.Error("Membership change consumer error", zap.Error(err))
				time.Sleep(5 * time.Second) // 重连延迟
			}
		}
	}
}

// GetPublisher 获取消息发布器（用于在其他地方发布消息）
func (slf *Job) GetPublisher() *pubsub.MessagePublisher {
	return slf.publisher
}
