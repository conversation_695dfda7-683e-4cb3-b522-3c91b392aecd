package server

import (
	"context"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"zodiacus/pkg/log"
	"zodiacus/pkg/pubsub"
)

type Job struct {
	log       *log.Logger
	rdb       *redis.Client
	pubsub    *pubsub.PubSub
	publisher *pubsub.MessagePublisher
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
}

func NewJob(
	log *log.Logger,
	rdb *redis.Client,
) *Job {
	return &Job{
		log:       log,
		rdb:       rdb,
		pubsub:    pubsub.NewPubSub(rdb, log),
		publisher: pubsub.NewMessagePublisher(rdb, log),
	}
}

func (slf *Job) Start(ctx context.Context) error {
	slf.ctx, slf.cancel = context.WithCancel(ctx)

	slf.log.Info("Starting job consumers...")

	// 启动会员时长变更消费者
	slf.wg.Add(1)
	go slf.startMembershipChangeConsumer()

	slf.log.Info("All job consumers started successfully")
	return nil
}

func (slf *Job) Stop(ctx context.Context) error {
	slf.log.Info("Stopping job consumers...")

	if slf.cancel != nil {
		slf.cancel()
	}

	// 等待所有goroutine结束，最多等待30秒
	done := make(chan struct{})
	go func() {
		slf.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		slf.log.Info("All job consumers stopped successfully")
	case <-time.After(30 * time.Second):
		slf.log.Warn("Job consumers stop timeout")
	}

	return nil
}

// startUserEventConsumer 启动用户事件消费者
func (slf *Job) startUserEventConsumer() {
	defer slf.wg.Done()

	handler := pubsub.NewUserEventHandler(slf.log)
	channels := []string{pubsub.GetChannel("events", "user")}

	slf.log.Info("Starting user event consumer", zap.Strings("channels", channels))

	for {
		select {
		case <-slf.ctx.Done():
			slf.log.Info("User event consumer stopped")
			return
		default:
			err := slf.pubsub.Subscribe(slf.ctx, channels, handler)
			if err != nil && err != context.Canceled {
				slf.log.Error("User event consumer error", zap.Error(err))
				time.Sleep(5 * time.Second) // 重连延迟
			}
		}
	}
}

// startOrderEventConsumer 启动订单事件消费者
func (slf *Job) startOrderEventConsumer() {
	defer slf.wg.Done()

	handler := pubsub.NewOrderEventHandler(slf.log)
	channels := []string{pubsub.GetChannel("events", "order")}

	slf.log.Info("Starting order event consumer", zap.Strings("channels", channels))

	for {
		select {
		case <-slf.ctx.Done():
			slf.log.Info("Order event consumer stopped")
			return
		default:
			err := slf.pubsub.Subscribe(slf.ctx, channels, handler)
			if err != nil && err != context.Canceled {
				slf.log.Error("Order event consumer error", zap.Error(err))
				time.Sleep(5 * time.Second) // 重连延迟
			}
		}
	}
}

// startNotificationConsumer 启动通知消费者
func (slf *Job) startNotificationConsumer() {
	defer slf.wg.Done()

	handler := pubsub.NewNotificationHandler(slf.log)
	channels := []string{
		pubsub.GetChannel("notifications", "email"),
		pubsub.GetChannel("notifications", "sms"),
		pubsub.GetChannel("notifications", "push"),
	}

	slf.log.Info("Starting notification consumer", zap.Strings("channels", channels))

	for {
		select {
		case <-slf.ctx.Done():
			slf.log.Info("Notification consumer stopped")
			return
		default:
			err := slf.pubsub.Subscribe(slf.ctx, channels, handler)
			if err != nil && err != context.Canceled {
				slf.log.Error("Notification consumer error", zap.Error(err))
				time.Sleep(5 * time.Second) // 重连延迟
			}
		}
	}
}

// GetPublisher 获取消息发布器（用于在其他地方发布消息）
func (slf *Job) GetPublisher() *pubsub.MessagePublisher {
	return slf.publisher
}
