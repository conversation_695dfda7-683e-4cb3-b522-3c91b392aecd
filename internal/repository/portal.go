package repository

import (
	"context"
	"github.com/uptrace/bun"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type PortalRepository interface {
	CreateSection(ctx context.Context, section *model.PortalSection) (int64, error)
	UpdateSection(ctx context.Context, section *model.PortalSection) error
	DeleteSection(ctx context.Context, id int64) error
	FetchSectionByID(ctx context.Context, id int64) (*model.PortalSection, error)
	ListSection(ctx context.Context, req *v1.ListPortalSectionRequest) (v1.ListPortalSectionResponseData, error)
	CountArticleBySectionID(ctx context.Context, id int64) (int, error)
	CreateArticle(ctx context.Context, article *model.PortalArticle) (int64, error)
	UpdateArticle(ctx context.Context, article *model.PortalArticle) error
	DeleteArticle(ctx context.Context, id int64) error
	FetchArticleByID(ctx context.Context, id int64) (*model.PortalArticle, error)
	PageListArticle(ctx context.Context, req *v1.PageListPortalArticleRequest) (*v1.PageListPortalArticleResponseData, error)
	ListTag(ctx context.Context) ([]string, error)
	CreateTags(ctx context.Context, names []string) error
	DeleteTags(ctx context.Context, names []string) error
}

func NewPortalRepository(
	repo *Repository,
) PortalRepository {
	return &portalRepository{
		Repository: repo,
	}
}

type portalRepository struct {
	*Repository
}

func (slf *portalRepository) DeleteTags(ctx context.Context, names []string) error {
	if len(names) == 0 {
		return nil
	}
	_, err := slf.DB(ctx).NewDelete().Model(&model.PortalTag{}).
		Where("name in (?)", bun.In(names)).
		Exec(ctx)
	return err
}

func (slf *portalRepository) CreateTags(ctx context.Context, names []string) error {
	if len(names) == 0 {
		return nil
	}
	tags := make([]*model.PortalTag, 0, len(names))
	for _, tag := range names {
		tags = append(tags, &model.PortalTag{
			Name: tag,
		})
	}
	_, err := slf.DB(ctx).NewInsert().
		Model(&tags).
		Column("name").
		Ignore().
		Exec(ctx)
	return err
}

func (slf *portalRepository) ListTag(ctx context.Context) ([]string, error) {
	var tags []string
	if err := slf.DB(ctx).NewSelect().Model(&model.PortalTag{}).ColumnExpr("name").Order("name asc").Scan(ctx, &tags); err != nil {
		return nil, err
	}
	return tags, nil
}

func (slf *portalRepository) CreateSection(ctx context.Context, section *model.PortalSection) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(section).Exec(ctx)
	if err != nil {
		if slf.IsDuplicateEntry(err) {
			return 0, v1.ErrPortalSectionNameAlreadyTaken
		}
		return 0, err
	}
	return exec.LastInsertId()
}

func (slf *portalRepository) UpdateSection(ctx context.Context, section *model.PortalSection) error {
	_, err := slf.DB(ctx).NewUpdate().Model(&model.PortalSection{ID: section.ID}).
		Set("name = ?", section.Name).
		Set("remark = ?", section.Remark).
		Set("is_enable = ?", section.IsEnable).
		WherePK().
		OmitZero().
		Exec(ctx)
	return err
}

func (slf *portalRepository) DeleteSection(ctx context.Context, id int64) error {
	_, err := slf.DB(ctx).NewDelete().Model(&model.PortalSection{ID: id}).WherePK().Exec(ctx)
	return err
}

func (slf *portalRepository) ListSection(ctx context.Context, req *v1.ListPortalSectionRequest) (v1.ListPortalSectionResponseData, error) {
	var sections []*v1.ListPortalSectionResponseDataItem
	query := slf.DB(ctx).NewSelect().Model(&model.PortalSection{})
	if req.Keywords != nil {
		query.Where("name like concat('%', ?, '%') or remark like concat('%', ?, '%')", *req.Keywords, *req.Keywords)
	}
	if err := query.
		ColumnExpr("ps.id").
		ColumnExpr("ps.name").
		ColumnExpr("ps.remark").
		ColumnExpr("ps.is_enable").
		ColumnExpr("ps.created_at as created_at").
		ColumnExpr("ps.updated_at as updated_at").
		Order("ps.name asc").
		Scan(ctx, &sections); err != nil {
		return nil, err
	}
	return sections, nil
}

func (slf *portalRepository) FetchSectionByID(ctx context.Context, id int64) (*model.PortalSection, error) {
	var section model.PortalSection
	if err := slf.DB(ctx).NewSelect().Model(&model.PortalSection{}).
		Where("id = ?", id).
		Scan(ctx, &section); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &section, nil
}

func (slf *portalRepository) CountArticleBySectionID(ctx context.Context, id int64) (int, error) {
	count, err := slf.DB(ctx).NewSelect().Model(&model.PortalArticle{}).
		Where("section_id = ?", id).
		Count(ctx)
	return count, err
}

func (slf *portalRepository) FetchArticleByID(ctx context.Context, id int64) (*model.PortalArticle, error) {
	var article model.PortalArticle
	if err := slf.DB(ctx).NewSelect().Model(&model.PortalArticle{}).
		Where("id = ?", id).
		Scan(ctx, &article); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &article, nil
}

func (slf *portalRepository) CreateArticle(ctx context.Context, article *model.PortalArticle) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(article).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return exec.LastInsertId()
}

func (slf *portalRepository) UpdateArticle(ctx context.Context, article *model.PortalArticle) error {
	_, err := slf.DB(ctx).NewUpdate().Model(&model.PortalArticle{ID: article.ID}).
		Set("section_id = ?", article.SectionID).
		Set("title = ?", article.Title).
		Set("author = ?", article.Author).
		Set("tags = ?", article.Tags).
		Set("is_top = ?", article.IsTop).
		Set("top_time = ?", article.TopTime).
		Set("is_publish = ?", article.IsPublish).
		Set("publish_time = ?", article.PublishTime).
		Set("is_modify = ?", article.IsModify).
		Set("modify_time = ?", article.ModifyTime).
		Set("content = ?", article.Content).
		Set("remark = ?", article.Remark).
		WherePK().
		OmitZero().
		Exec(ctx)
	return err
}

func (slf *portalRepository) DeleteArticle(ctx context.Context, id int64) error {
	_, err := slf.DB(ctx).NewDelete().Model(&model.PortalArticle{ID: id}).WherePK().Exec(ctx)
	return err
}

func (slf *portalRepository) PageListArticle(ctx context.Context, req *v1.PageListPortalArticleRequest) (*v1.PageListPortalArticleResponseData, error) {
	var articles []*v1.PageListPortalArticleResponseDataItem
	query := slf.DB(ctx).NewSelect().Model(&model.PortalArticle{}).
		ColumnExpr("pa.id").
		ColumnExpr("pa.section_id").
		ColumnExpr("ps.name as section_name").
		ColumnExpr("pa.title").
		ColumnExpr("pa.author").
		ColumnExpr("pa.tags").
		ColumnExpr("pa.is_top").
		ColumnExpr("pa.top_time").
		ColumnExpr("pa.is_publish").
		ColumnExpr("pa.publish_time").
		ColumnExpr("pa.is_modify").
		ColumnExpr("pa.modify_time").
		ColumnExpr("pa.content").
		ColumnExpr("pa.remark").
		ColumnExpr("pa.created_at as created_at").
		ColumnExpr("pa.updated_at as updated_at")
	query.
		Join("JOIN portal_section ps ON ps.id = pa.section_id")
	if len(req.Param.SectionIDs) > 0 {
		query.Where("pa.section_id in (?)", bun.In(req.Param.SectionIDs))
	}
	if req.Param.Keywords != nil {
		query.Where("pa.id like concat('%', ?, '%') or  pa.title like concat('%', ?, '%') or pa.author like concat('%', ?, '%') or pa.content like concat('%', ?, '%') or pa.remark like concat('%', ?, '%')",
			*req.Param.Keywords, *req.Param.Keywords, *req.Param.Keywords, *req.Param.Keywords, *req.Param.Keywords)
	}
	if req.Param.IsPublish != nil {
		query.Where("pa.is_publish = ?", *req.Param.IsPublish)
		if *req.Param.IsPublish {
			if req.Param.PublishTime != nil {
				query.Where("pa.publish_time >= ?", req.Param.PublishTime.StartAt)
				query.Where("pa.publish_time <= ?", req.Param.PublishTime.EndAt)
			}
		}
	}
	if req.Param.IsTop != nil {
		query.Where("pa.is_top = ?", *req.Param.IsTop)
		if *req.Param.IsTop {
			if req.Param.TopTime != nil {
				query.Where("pa.top_time >= ?", req.Param.TopTime.StartAt)
				query.Where("pa.top_time <= ?", req.Param.TopTime.EndAt)
			}
		}
	}
	if req.Param.IsModify != nil {
		query.Where("pa.is_modify = ?", *req.Param.IsModify)
		if *req.Param.IsModify {
			if req.Param.ModifyTime != nil {
				query.Where("pa.modify_time >= ?", req.Param.ModifyTime.StartAt)
				query.Where("pa.modify_time <= ?", req.Param.ModifyTime.EndAt)
			}
		}
	}
	if req.Param.CreateTime != nil {
		query.Where("pa.created_at >= ?", req.Param.CreateTime.StartAt)
		query.Where("pa.created_at <= ?", req.Param.CreateTime.EndAt)
	}
	count, err := query.
		Offset(req.Offset()).
		Limit(req.Limit()).
		Order("pa.modify_time desc").
		Order("pa.top_time desc").
		ScanAndCount(ctx, &articles)
	if err != nil {
		return nil, err
	}
	return &v1.PageListPortalArticleResponseData{
		Total: count,
		List:  articles,
	}, nil
}
