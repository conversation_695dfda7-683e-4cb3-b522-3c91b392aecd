package repository

import (
	"context"
	"github.com/uptrace/bun"
	"zodiacus/internal/model"
)

type AppUserKefuRepository interface {
	FetchAppUserQwKefu(ctx context.Context, userID string, qwUserIDs []string) (*model.AppUserQwKefu, error)
	FetchQwKefuVip(ctx context.Context, userID string, contactWayID int64) (*model.AppUserQwKefu, error)
}

func NewAppUserKefuRepository(repo *Repository) AppUserKefuRepository {
	return &appUserKefuRepository{Repository: repo}
}

type appUserKefuRepository struct {
	*Repository
}

func (slf *appUserKefuRepository) FetchAppUserQwKefu(ctx context.Context, userID string, qwUserIDs []string) (*model.AppUserQwKefu, error) {
	var kefu model.AppUserQwKefu
	if err := slf.DB(ctx).NewSelect().Model(&kefu).
		Where("user_id = ?", userID).
		Where("qw_user_ids in (?)", bun.In(qwUserIDs)).
		Where("type = ?", 1).
		Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &kefu, nil
}

func (slf *appUserKefuRepository) FetchQwKefuVip(ctx context.Context, userID string, contactWayID int64) (*model.AppUserQwKefu, error) {
	var kefu model.AppUserQwKefu
	if err := slf.DB(ctx).NewSelect().Model(&kefu).
		Where("user_id = ?", userID).
		Where("qw_contact_way_id = ?", contactWayID).
		Where("type = ?", 2).
		Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &kefu, nil
}
