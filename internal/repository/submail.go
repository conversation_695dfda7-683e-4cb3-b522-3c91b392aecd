package repository

import (
	"context"
	"zodiacus/internal/model"
)

type SubMailRepository interface {
	CreateSubMailSMS(ctx context.Context, sms *model.SubMailSMS) (int64, error)
	FetchOnyBySendID(ctx context.Context, sendID string) (*model.SubMailSMS, error)
	UpdateSubMailSMS(ctx context.Context, sms *model.SubMailSMS) error
}

func NewSubMailRepository(
	repo *Repository,
) SubMailRepository {
	return &subMailRepository{
		Repository: repo,
	}
}

type subMailRepository struct {
	*Repository
}

func (slf *subMailRepository) UpdateSubMailSMS(ctx context.Context, sms *model.SubMailSMS) error {
	_, err := slf.DB(ctx).NewUpdate().Model(sms).
		WherePK().
		OmitZero().Exec(ctx)
	return err
}

func (slf *subMailRepository) FetchOnyBySendID(ctx context.Context, sendID string) (*model.SubMailSMS, error) {
	var sms model.SubMailSMS
	err := slf.DB(ctx).NewSelect().Model(&sms).Where("send_id = ?", sendID).Scan(ctx)
	if err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &sms, nil
}

func (slf *subMailRepository) CreateSubMailSMS(ctx context.Context, sms *model.SubMailSMS) (int64, error) {
	res, err := slf.DB(ctx).NewInsert().Model(sms).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return res.LastInsertId()
}
