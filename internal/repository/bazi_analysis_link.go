package repository

import (
	"context"
	"zodiacus/internal/model"
)

type BaziAnalysisLinkRepository interface {
	CreateLink(ctx context.Context, link *model.BaziAnalysisLink) (int64, error)
}

type baziAnalysisLinkRepository struct {
	*Repository
}

func NewBaziAnalysisLinkRepository(repo *Repository) BaziAnalysisLinkRepository {
	return &baziAnalysisLinkRepository{Repository: repo}
}

func (slf *baziAnalysisLinkRepository) CreateLink(ctx context.Context, link *model.BaziAnalysisLink) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(link).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return exec.LastInsertId()
}
