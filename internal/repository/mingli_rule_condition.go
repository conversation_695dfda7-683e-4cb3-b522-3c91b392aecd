package repository

import (
	"context"
	"database/sql"
	"errors"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"

	"github.com/uptrace/bun"
)

type MingliRuleConditionRepository interface {
	CreateMingliRuleCondition(ctx context.Context, condition *model.MingliRuleCondition) (int64, error)
	UpdateMingliRuleCondition(ctx context.Context, condition *model.MingliRuleCondition) error
	GetMingliRuleConditionByID(ctx context.Context, id int64) (*model.MingliRuleCondition, error)
	PageListMingliRuleCondition(ctx context.Context, req *v1.PageListMingliRuleConditionRequest) (*v1.PageListMingliRuleConditionResponseData, error)
	SetMingliRuleConditionZuodui(ctx context.Context, id int64, zuodui []*model.MingliRuleConditionKV) error
	SetMingliRuleConditionXiji(ctx context.Context, id int64, xiji []*model.MingliRuleConditionKV) error
	DeleteMingliRuleConditionByID(ctx context.Context, id int64) error
	GetMingliRuleConditionsByRuleIDAndGender(ctx context.Context, ruleID int64, gender int) ([]*model.MingliRuleCondition, error)
	GetMingliRuleConditionsByRuleIDsAndGender(ctx context.Context, ruleIDs []int64, gender int) ([]*model.MingliRuleCondition, error)
}

func NewMingliRuleConditionRepository(
	repo *Repository,
) MingliRuleConditionRepository {
	return &mingliRuleConditionRepository{
		Repository: repo,
	}
}

type mingliRuleConditionRepository struct {
	*Repository
}

func (slf *mingliRuleConditionRepository) CreateMingliRuleCondition(ctx context.Context, condition *model.MingliRuleCondition) (int64, error) {
	res, err := slf.DB(ctx).NewInsert().Model(condition).Exec(ctx)
	if err != nil {
		if slf.IsDuplicateEntry(err) {
			return 0, v1.ErrMingliRuleConditionNoAlreadyTaken
		}
		return 0, err
	}
	return res.LastInsertId()
}

func (slf *mingliRuleConditionRepository) UpdateMingliRuleCondition(ctx context.Context, condition *model.MingliRuleCondition) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(condition).
		Set("no = ?", condition.No).
		Set("name = ?", condition.Name).
		Set("category = ?", condition.Category).
		Set("type = ?", condition.Type).
		Set("criterion = ?", condition.Criterion).
		Set("gender = ?", condition.Gender).
		Set("weizhi_zuo = ?", condition.WeizhiZuo).
		Set("weizhi_dui = ?", condition.WeizhiDui).
		WherePK().OmitZero().Exec(ctx); err != nil {
		if slf.IsDuplicateEntry(err) {
			return v1.ErrMingliRuleConditionNoAlreadyTaken
		}
		return err
	}
	return nil
}

func (slf *mingliRuleConditionRepository) GetMingliRuleConditionByID(ctx context.Context, id int64) (*model.MingliRuleCondition, error) {
	var condition model.MingliRuleCondition
	err := slf.DB(ctx).NewSelect().Model(&model.MingliRuleCondition{ID: id}).
		WherePK().Scan(ctx, &condition)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &condition, nil
}

func (slf *mingliRuleConditionRepository) PageListMingliRuleCondition(ctx context.Context, req *v1.PageListMingliRuleConditionRequest) (*v1.PageListMingliRuleConditionResponseData, error) {
	var (
		err error
		res v1.PageListMingliRuleConditionResponseData
	)
	res.Total, err = slf.DB(ctx).NewSelect().Model((*model.MingliRuleCondition)(nil)).
		ColumnExpr("id").
		ColumnExpr("no").
		ColumnExpr("name").
		ColumnExpr("category").
		ColumnExpr("type").
		ColumnExpr("criterion").
		ColumnExpr("gender").
		ColumnExpr("weizhi_zuo").
		ColumnExpr("weizhi_dui").
		ColumnExpr("zuodui").
		ColumnExpr("xiji").
		Where("mingli_rule_id = ?", req.Param.MingliRuleID).
		Offset(req.Offset()).Limit(req.Limit()).Order("created_at desc").
		ScanAndCount(ctx, &res.List)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &res, nil
}

func (slf *mingliRuleConditionRepository) DeleteMingliRuleConditionByID(ctx context.Context, id int64) error {
	if _, err := slf.DB(ctx).NewDelete().Model(&model.MingliRuleCondition{ID: id}).WherePK().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *mingliRuleConditionRepository) SetMingliRuleConditionZuodui(ctx context.Context, id int64, zuodui []*model.MingliRuleConditionKV) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.MingliRuleCondition{ID: id}).
		Set("zuodui = ?", zuodui).
		WherePK().OmitZero().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *mingliRuleConditionRepository) SetMingliRuleConditionXiji(ctx context.Context, id int64, xiji []*model.MingliRuleConditionKV) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.MingliRuleCondition{ID: id}).
		Set("xiji = ?", xiji).
		WherePK().OmitZero().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *mingliRuleConditionRepository) GetMingliRuleConditionsByRuleIDAndGender(ctx context.Context, ruleID int64, gender int) ([]*model.MingliRuleCondition, error) {
	var conditions []*model.MingliRuleCondition
	if err := slf.DB(ctx).NewSelect().Model((*model.MingliRuleCondition)(nil)).
		Where("mingli_rule_id = ?", ruleID).
		Where("gender = ? OR gender = 1", gender).Scan(ctx, &conditions); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return conditions, nil
}

func (slf *mingliRuleConditionRepository) GetMingliRuleConditionsByRuleIDsAndGender(ctx context.Context, ruleIDs []int64, gender int) ([]*model.MingliRuleCondition, error) {
	var conditions []*model.MingliRuleCondition
	if err := slf.DB(ctx).NewSelect().Model((*model.MingliRuleCondition)(nil)).
		Where("mingli_rule_id IN (?)", bun.In(ruleIDs)).
		Where("gender = ? OR gender = 1", gender).Scan(ctx, &conditions); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return conditions, nil
}
