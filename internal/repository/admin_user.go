package repository

import (
	"context"
	"zodiacus/internal/model"
)

type AdminUserRepository interface {
	CreateAdminUser(ctx context.Context, user *model.AdminUser) (int64, error)
	FetchAdminUserByUserName(ctx context.Context, username string) (*model.AdminUser, error)
}

func NewAdminUserRepository(
	repo *Repository,
) AdminUserRepository {
	return &adminUserRepository{
		Repository: repo,
	}
}

type adminUserRepository struct {
	*Repository
}

func (slf *adminUserRepository) CreateAdminUser(ctx context.Context, user *model.AdminUser) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(user).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return exec.LastInsertId()
}

func (slf *adminUserRepository) FetchAdminUserByUserName(ctx context.Context, username string) (*model.AdminUser, error) {
	var user model.AdminUser
	if err := slf.DB(ctx).NewSelect().Model(&model.AdminUser{}).
		Where("username = ?", username).Scan(ctx, &user); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}
