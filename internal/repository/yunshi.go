package repository

import (
	"context"
	"database/sql"
	"errors"
	"github.com/uptrace/bun"
	"zodiacus/internal/model"
)

type YunshiRepository interface {
	GetGuaxiangByValue(ctx context.Context, value int) (*model.Guaxiang, error)
	GetGanzhiByName(ctx context.Context, name string) (*model.Ganzhi, error)
	GetShishenByRiyuan(ctx context.Context, ry1, ry2 string) (*model.RiyuanShishen, error)
	GetShishenLiunianSuggestion(ctx context.Context, shishen, gender string, scoreAbove50 bool) (*model.ShishenLiunianSuggestion, error)
	GetChangshengByName(ctx context.Context, name string) (*model.Changsheng, error)
	GetDizhiXiangchong(ctx context.Context, dizhi1, dizhi2 string) (*model.DizhiXiangchong, error)
	GetDizhiLiuhe(ctx context.Context, dizhi1, dizhi2 string) (*model.<PERSON><PERSON><PERSON><PERSON><PERSON>, error)
	GetDizhi<PERSON><PERSON><PERSON>(ctx context.Context, dizhi1, dizhi2, dizhi3 string) (*model.<PERSON><PERSON><PERSON><PERSON><PERSON>, error)
	GetTianganWuhe(ctx context.Context, tiangan1, tiangan2 string) (*model.TianganWuhe, error)
	GetWuxingXiangke(ctx context.Context, wuxing, xiangke string) (*model.WuxingXiangke, error)
	GetWuxingAboutXiangkeByName(ctx context.Context, name string) (*model.WuxingXiangke, error)
	GetWuxingXiangsheng(ctx context.Context, wuxing, xiangsheng string) (*model.WuxingXiangsheng, error)
	GetTiangan(ctx context.Context, name string) (*model.Tiangan, error)
	GetDizhi(ctx context.Context, name string) (*model.Dizhi, error)
	Nayins(ctx context.Context) ([]*model.Nayin, error)
	Shenshas(ctx context.Context) ([]*model.Shensha2, error)
	GetOffset4TimeByLocation(ctx context.Context, location string) (int, error)
}

func NewYunshiRepository(
	repo *Repository,
) YunshiRepository {
	return &yunshiRepository{
		Repository: repo,
	}
}

type yunshiRepository struct {
	*Repository
}

func (slf *yunshiRepository) GetOffset4TimeByLocation(ctx context.Context, location string) (int, error) {
	var t int
	if err := slf.DB(ctx).NewSelect().TableExpr("addr_localtotime").
		ColumnExpr("addtime").
		Where("fullname = ?", location).
		Scan(ctx, &t); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return 0, nil
		}
		return 0, err
	}
	return t, nil
}

func (slf *yunshiRepository) GetWuxingAboutXiangkeByName(ctx context.Context, name string) (*model.WuxingXiangke, error) {
	var wx model.WuxingXiangke
	if err := slf.DB(ctx).NewSelect().Model((*model.WuxingXiangke)(nil)).
		Where("wuxing_xiangke = ?", name).Scan(ctx, &wx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &wx, nil
}

func (slf *yunshiRepository) GetGuaxiangByValue(ctx context.Context, value int) (*model.Guaxiang, error) {
	var guaxiang model.Guaxiang
	if err := slf.DB(ctx).NewSelect().Model((*model.Guaxiang)(nil)).
		Where("value = ?", value).Scan(ctx, &guaxiang); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &guaxiang, nil
}

func (slf *yunshiRepository) GetGanzhiByName(ctx context.Context, name string) (*model.Ganzhi, error) {
	var ganzhi model.Ganzhi
	if err := slf.DB(ctx).NewSelect().Model((*model.Ganzhi)(nil)).
		Where("name = ?", name).Scan(ctx, &ganzhi); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &ganzhi, nil
}

func (slf *yunshiRepository) GetShishenByRiyuan(ctx context.Context, ry1, ry2 string) (*model.RiyuanShishen, error) {
	var ryss model.RiyuanShishen
	if err := slf.DB(ctx).NewSelect().Model((*model.RiyuanShishen)(nil)).
		Where("riyuan_1 = ? AND riyuan_2 = ?", ry1, ry2).Scan(ctx, &ryss); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &ryss, nil
}

func (slf *yunshiRepository) GetShishenLiunianSuggestion(ctx context.Context, shishen, gender string, scoreAbove50 bool) (*model.ShishenLiunianSuggestion, error) {
	var sls model.ShishenLiunianSuggestion
	query := slf.DB(ctx).NewSelect().Model((*model.ShishenLiunianSuggestion)(nil)).
		Where("shishen = ? AND score_above_50 = ?", shishen, scoreAbove50)
	if gender == "男" {
		query.Where("gender = 1 or gender = 0")
	} else if gender == "女" {
		query.Where("gender = 2 or gender = 0")
	} else {
		query.Where("gender = 0")
	}
	if err := query.Scan(ctx, &sls); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &sls, nil
}

func (slf *yunshiRepository) Shenshas(ctx context.Context) ([]*model.Shensha2, error) {
	var list []*model.Shensha2
	if err := slf.DB(ctx).NewSelect().Model((*model.Shensha2)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	for _, item := range list {
		item.Setup()
	}
	return list, nil
}

func (slf *yunshiRepository) Nayins(ctx context.Context) ([]*model.Nayin, error) {
	var list []*model.Nayin
	if err := slf.DB(ctx).NewSelect().Model((*model.Nayin)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *yunshiRepository) GetChangshengByName(ctx context.Context, name string) (*model.Changsheng, error) {
	var changsheng model.Changsheng
	if err := slf.DB(ctx).NewSelect().Model((*model.Changsheng)(nil)).
		Where("name = ?", name).Scan(ctx, &changsheng); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &changsheng, nil
}

func (slf *yunshiRepository) GetDizhiXiangchong(ctx context.Context, dizhi1, dizhi2 string) (*model.DizhiXiangchong, error) {
	var dzxc model.DizhiXiangchong
	if err := slf.DB(ctx).NewSelect().Model((*model.DizhiXiangchong)(nil)).
		Where("(dizhi = ? AND dizhi_two = ?) OR (dizhi = ? AND dizhi_two = ?)", dizhi1, dizhi2, dizhi2, dizhi1).Scan(ctx, &dzxc); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &dzxc, nil
}

func (slf *yunshiRepository) GetDizhiSanhe(ctx context.Context, dizhi1, dizhi2, dizhi3 string) (*model.DizhiSanhe, error) {
	arr := []string{dizhi1, dizhi2, dizhi3}
	var dzsh model.DizhiSanhe
	if err := slf.DB(ctx).NewSelect().Model((*model.DizhiSanhe)(nil)).
		Where("dizhi in (?) and dizhi_two in (?) and dizhi_three in (?)", bun.In(arr), bun.In(arr), bun.In(arr)).Scan(ctx, &dzsh); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &dzsh, nil
}

func (slf *yunshiRepository) GetDizhiLiuhe(ctx context.Context, dizhi1, dizhi2 string) (*model.DizhiLiuhe, error) {
	var dzlh model.DizhiLiuhe
	if err := slf.DB(ctx).NewSelect().Model((*model.DizhiLiuhe)(nil)).
		Where("(dizhi = ? AND dizhi_two = ?) OR (dizhi = ? AND dizhi_two = ?)", dizhi1, dizhi2, dizhi2, dizhi1).Scan(ctx, &dzlh); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &dzlh, nil
}

func (slf *yunshiRepository) GetDizhi(ctx context.Context, name string) (*model.Dizhi, error) {
	var dz model.Dizhi
	if err := slf.DB(ctx).NewSelect().Model((*model.Dizhi)(nil)).
		Where("dizhi = ?", name).Scan(ctx, &dz); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &dz, nil
}

func (slf *yunshiRepository) GetTianganWuhe(ctx context.Context, tiangan1, tiangan2 string) (*model.TianganWuhe, error) {
	var tgwh model.TianganWuhe
	if err := slf.DB(ctx).NewSelect().Model((*model.TianganWuhe)(nil)).
		Where("(tiangan = ? AND tiangan_two = ?) OR (tiangan = ? AND tiangan_two = ?)", tiangan1, tiangan2, tiangan2, tiangan1).Scan(ctx, &tgwh); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &tgwh, nil
}

func (slf *yunshiRepository) GetTiangan(ctx context.Context, name string) (*model.Tiangan, error) {
	var tiangan model.Tiangan
	if err := slf.DB(ctx).NewSelect().Model((*model.Tiangan)(nil)).
		Where("tiangan = ?", name).Scan(ctx, &tiangan); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &tiangan, nil
}

func (slf *yunshiRepository) GetWuxingXiangke(ctx context.Context, wuxing, xiangke string) (*model.WuxingXiangke, error) {
	var wx model.WuxingXiangke
	if err := slf.DB(ctx).NewSelect().Model((*model.WuxingXiangke)(nil)).
		Where("wuxing = ? AND wuxing_xiangke = ?", wuxing, xiangke).Scan(ctx, &wx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &wx, nil
}

func (slf *yunshiRepository) GetWuxingXiangsheng(ctx context.Context, wuxing, xiangsheng string) (*model.WuxingXiangsheng, error) {
	var wx model.WuxingXiangsheng
	if err := slf.DB(ctx).NewSelect().Model((*model.WuxingXiangsheng)(nil)).
		Where("wuxing = ? AND wuxing_xiangsheng = ?", wuxing, xiangsheng).Scan(ctx, &wx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &wx, nil
}
