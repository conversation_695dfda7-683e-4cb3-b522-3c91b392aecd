package pubsub

import (
	"context"
	"encoding/json"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"zodiacus/pkg/log"
)

// MembershipSubscriber 会员时长变更消息订阅器
type MembershipSubscriber struct {
	rdb    *redis.Client
	logger *log.Logger
}

// NewMembershipSubscriber 创建会员时长变更消息订阅器
func NewMembershipSubscriber(rdb *redis.Client, logger *log.Logger) *MembershipSubscriber {
	return &MembershipSubscriber{
		rdb:    rdb,
		logger: logger,
	}
}

// SubscribeMembershipChange 订阅会员时长变更消息
func (s *MembershipSubscriber) SubscribeMembershipChange(ctx context.Context, handler MembershipChangeHandler) error {
	// 创建订阅
	channel := "membership:change"
	pubsub := s.rdb.Subscribe(ctx, channel)
	defer pubsub.Close()

	s.logger.Info("Started subscribing to membership change channel", 
		zap.String("channel", channel))

	// 接收消息
	ch := pubsub.Channel()
	for {
		select {
		case <-ctx.Done():
			s.logger.Info("Membership change subscription context cancelled", zap.String("channel", channel))
			return ctx.Err()
		case redisMsg := <-ch:
			if redisMsg == nil {
				continue
			}
			
			// 解析消息
			var msg MembershipChangeMessage
			if err := json.Unmarshal([]byte(redisMsg.Payload), &msg); err != nil {
				s.logger.Error("Failed to unmarshal membership change message", 
					zap.Error(err), 
					zap.String("payload", redisMsg.Payload),
					zap.String("channel", redisMsg.Channel))
				continue
			}

			// 处理消息
			s.handleMembershipChangeMessage(ctx, &msg, handler)
		}
	}
}

// handleMembershipChangeMessage 处理单个会员时长变更消息
func (s *MembershipSubscriber) handleMembershipChangeMessage(ctx context.Context, msg *MembershipChangeMessage, handler MembershipChangeHandler) {
	startTime := time.Now()
	
	defer func() {
		duration := time.Since(startTime)
		s.logger.Info("Membership change message processing completed", 
			zap.String("messageId", msg.ID),
			zap.String("userId", msg.UserID),
			zap.Duration("duration", duration))
	}()

	// 执行处理器
	err := handler.HandleMembershipChange(ctx, msg)
	if err != nil {
		// 增加重试次数
		msg.Retry++
		s.logger.Error("Membership change message handler failed", 
			zap.Error(err),
			zap.String("messageId", msg.ID),
			zap.String("userId", msg.UserID),
			zap.Int("retryCount", msg.Retry))
		
		// 处理失败的消息
		s.handleFailedMessage(ctx, msg, err)
		return
	}

	s.logger.Info("Membership change message handled successfully", 
		zap.String("messageId", msg.ID),
		zap.String("userId", msg.UserID))
}

// handleFailedMessage 处理失败的消息
func (s *MembershipSubscriber) handleFailedMessage(ctx context.Context, msg *MembershipChangeMessage, err error) {
	const maxRetries = 3
	
	if msg.Retry <= maxRetries {
		s.logger.Info("Message will be retried", 
			zap.String("messageId", msg.ID),
			zap.String("userId", msg.UserID),
			zap.Int("retryCount", msg.Retry),
			zap.Int("maxRetries", maxRetries))
		
		// 可以在这里实现重试逻辑，比如：
		// 1. 延迟重新发布消息
		// 2. 发送到重试队列
		// 3. 使用指数退避策略
		
		// 示例：延迟重新发布（实际使用时可能需要更复杂的重试机制）
		go func() {
			delay := time.Duration(msg.Retry) * time.Second
			time.Sleep(delay)
			
			// 这里可以重新发布消息到同一个频道
			// 或者发布到专门的重试频道
		}()
	} else {
		s.logger.Error("Message exceeded max retries, sending to dead letter queue", 
			zap.String("messageId", msg.ID),
			zap.String("userId", msg.UserID),
			zap.Int("retryCount", msg.Retry),
			zap.Error(err))
		
		// 发送到死信队列或记录到失败日志
		s.sendToDeadLetterQueue(ctx, msg, err)
	}
}

// sendToDeadLetterQueue 发送到死信队列
func (s *MembershipSubscriber) sendToDeadLetterQueue(ctx context.Context, msg *MembershipChangeMessage, err error) {
	// 这里可以实现死信队列逻辑，比如：
	// 1. 发送到专门的死信频道
	// 2. 记录到数据库
	// 3. 发送告警通知
	
	s.logger.Error("Message sent to dead letter queue", 
		zap.String("messageId", msg.ID),
		zap.String("userId", msg.UserID),
		zap.Error(err))
	
	// 示例：发送到死信频道
	deadLetterChannel := "membership:change:dead_letter"
	data, marshalErr := json.Marshal(map[string]interface{}{
		"original_message": msg,
		"error":           err.Error(),
		"failed_at":       time.Now(),
	})
	
	if marshalErr != nil {
		s.logger.Error("Failed to marshal dead letter message", zap.Error(marshalErr))
		return
	}
	
	publishErr := s.rdb.Publish(ctx, deadLetterChannel, data).Err()
	if publishErr != nil {
		s.logger.Error("Failed to publish to dead letter queue", zap.Error(publishErr))
	}
}

// SubscribeWithWorkerPool 使用工作池订阅消息（并发处理）
func (s *MembershipSubscriber) SubscribeWithWorkerPool(ctx context.Context, handler MembershipChangeHandler, workerCount int) error {
	// 创建订阅
	channel := "membership:change"
	pubsub := s.rdb.Subscribe(ctx, channel)
	defer pubsub.Close()

	s.logger.Info("Started subscribing to membership change channel with worker pool", 
		zap.String("channel", channel),
		zap.Int("workerCount", workerCount))

	// 创建消息通道
	msgChan := make(chan *MembershipChangeMessage, workerCount*2)

	// 启动工作协程
	for i := 0; i < workerCount; i++ {
		go func(workerID int) {
			s.logger.Info("Worker started", zap.Int("workerId", workerID))
			for msg := range msgChan {
				s.logger.Debug("Worker processing message", 
					zap.Int("workerId", workerID),
					zap.String("messageId", msg.ID),
					zap.String("userId", msg.UserID))
				
				s.handleMembershipChangeMessage(ctx, msg, handler)
			}
			s.logger.Info("Worker stopped", zap.Int("workerId", workerID))
		}(i)
	}

	// 接收消息
	ch := pubsub.Channel()
	for {
		select {
		case <-ctx.Done():
			s.logger.Info("Membership change subscription context cancelled", zap.String("channel", channel))
			close(msgChan)
			return ctx.Err()
		case redisMsg := <-ch:
			if redisMsg == nil {
				continue
			}
			
			// 解析消息
			var msg MembershipChangeMessage
			if err := json.Unmarshal([]byte(redisMsg.Payload), &msg); err != nil {
				s.logger.Error("Failed to unmarshal membership change message", 
					zap.Error(err), 
					zap.String("payload", redisMsg.Payload),
					zap.String("channel", redisMsg.Channel))
				continue
			}

			// 发送到工作协程
			select {
			case msgChan <- &msg:
				// 消息已发送到工作协程
			case <-ctx.Done():
				s.logger.Info("Context cancelled while sending message to worker")
				close(msgChan)
				return ctx.Err()
			default:
				s.logger.Warn("Worker pool is busy, dropping message", 
					zap.String("messageId", msg.ID),
					zap.String("userId", msg.UserID))
			}
		}
	}
}

// GetChannelName 获取会员时长变更频道名
func (s *MembershipSubscriber) GetChannelName() string {
	return "membership:change"
}

// Close 关闭订阅器
func (s *MembershipSubscriber) Close() error {
	return s.rdb.Close()
}
