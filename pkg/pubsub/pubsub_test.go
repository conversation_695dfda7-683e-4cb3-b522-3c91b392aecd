package pubsub

import (
	"context"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"zodiacus/pkg/log"
)

func setupTestRedis() *redis.Client {
	return redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       1, // 使用测试数据库
	})
}

func TestPubSub_PublishAndSubscribe(t *testing.T) {
	rdb := setupTestRedis()
	defer rdb.Close()

	// 测试连接
	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		t.Skip("Redis not available, skipping test")
	}

	logger := log.NewLogger(&log.Config{
		LogLevel: "info",
		Encoding: "console",
	})

	pubsub := NewPubSub(rdb, logger)
	publisher := NewMessagePublisher(rdb, logger)

	// 创建测试处理器
	handler := NewMembershipChangeHandler(logger)
	channel := GetChannel("events", "membership")

	// 启动订阅者
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	go func() {
		err := pubsub.Subscribe(ctx, []string{channel}, handler)
		if err != nil && err != context.DeadlineExceeded {
			t.Errorf("Subscribe failed: %v", err)
		}
	}()

	// 等待订阅者启动
	time.Sleep(100 * time.Millisecond)

	// 发布消息
	err = publisher.PublishMembershipUpgrade(ctx, "test_user", "2024-01-01", "2024-12-31")
	assert.NoError(t, err)

	// 等待消息处理
	time.Sleep(1 * time.Second)
}

func TestMessagePublisher_PublishMembershipChange(t *testing.T) {
	rdb := setupTestRedis()
	defer rdb.Close()

	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		t.Skip("Redis not available, skipping test")
	}

	logger := log.NewLogger(&log.Config{
		LogLevel: "info",
		Encoding: "console",
	})

	publisher := NewMessagePublisher(rdb, logger)

	// 测试会员升级事件
	err = publisher.PublishMembershipUpgrade(ctx, "user123", "2024-01-01", "2024-12-31")
	assert.NoError(t, err)

	// 测试会员降级事件
	err = publisher.PublishMembershipDowngrade(ctx, "user123", "2024-12-31", "2024-06-30")
	assert.NoError(t, err)

	// 测试会员延期事件
	err = publisher.PublishMembershipExtend(ctx, "user123", "2024-06-30", "2024-12-31")
	assert.NoError(t, err)

	// 测试会员过期事件
	err = publisher.PublishMembershipExpire(ctx, "user123", "2024-01-01")
	assert.NoError(t, err)

	// 测试会员激活事件
	err = publisher.PublishMembershipActivate(ctx, "user123", "2024-12-31")
	assert.NoError(t, err)
}

func TestChannelHelpers(t *testing.T) {
	// 测试频道辅助函数
	channel := GetChannel("events", "user")
	assert.Equal(t, "events:user", channel)

	pattern := GetChannelPattern("events")
	assert.Equal(t, "events:*", pattern)
}

// 基准测试
func BenchmarkPublishMessage(b *testing.B) {
	rdb := setupTestRedis()
	defer rdb.Close()

	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		b.Skip("Redis not available, skipping benchmark")
	}

	logger := log.NewLogger(&log.Config{
		LogLevel: "error", // 减少日志输出
		Encoding: "console",
	})

	publisher := NewMessagePublisher(rdb, logger)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := publisher.PublishMembershipUpgrade(ctx, "user123", "2024-01-01", "2024-12-31")
		if err != nil {
			b.Fatalf("Failed to publish message: %v", err)
		}
	}
}
