# Redis PubSub 系统

基于Redis的发布订阅系统，用于异步消息处理和事件驱动架构。

## 功能特性

- ✅ **类型安全的消息处理** - 使用结构化消息和处理器接口
- ✅ **多种消息类型支持** - 用户事件、订单事件、通知等
- ✅ **自动重连机制** - 连接断开时自动重连
- ✅ **优雅关闭** - 支持graceful shutdown
- ✅ **日志记录** - 完整的消息处理日志
- ✅ **错误处理** - 完善的错误处理和重试机制

## 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Publisher     │───▶│   Redis PubSub  │───▶│   Subscriber    │
│                 │    │                 │    │                 │
│ - UserEvents    │    │ - events:user   │    │ - UserHandler   │
│ - OrderEvents   │    │ - events:order  │    │ - OrderHandler  │
│ - Notifications │    │ - notifications │    │ - NotifyHandler │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 快速开始

### 1. 基本使用

```go
package main

import (
    "context"
    "github.com/redis/go-redis/v9"
    "zodiacus/pkg/log"
    "zodiacus/pkg/pubsub"
)

func main() {
    // 初始化Redis客户端
    rdb := redis.NewClient(&redis.Options{
        Addr: "localhost:6379",
    })
    
    // 初始化日志
    logger := log.NewLogger(&log.Config{
        LogLevel: "info",
        Encoding: "console",
    })
    
    // 创建发布器
    publisher := pubsub.NewMessagePublisher(rdb, logger)
    
    // 发布用户注册事件
    ctx := context.Background()
    err := publisher.PublishUserRegister(ctx, "user123", "<EMAIL>")
    if err != nil {
        logger.Error("Failed to publish event", "error", err)
    }
}
```

### 2. 在Job中消费消息

Job服务会自动启动以下消费者：

- **用户事件消费者** - 处理用户注册、登录、登出事件
- **订单事件消费者** - 处理订单创建、支付、取消事件  
- **通知消费者** - 处理邮件、短信、推送通知

```go
// Job会自动启动，无需手动配置
job := server.NewJob(logger, rdb)
job.Start(ctx)
```

## 消息类型

### 用户事件 (user_event)

```go
// 用户注册
publisher.PublishUserRegister(ctx, "user123", "<EMAIL>")

// 用户登录
publisher.PublishUserLogin(ctx, "user123", "192.168.1.1")

// 用户登出
publisher.PublishUserLogout(ctx, "user123")
```

**频道**: `events:user`

### 订单事件 (order_event)

```go
// 订单创建
publisher.PublishOrderCreated(ctx, "order456", "user123", 99.99)

// 订单支付
publisher.PublishOrderPaid(ctx, "order456", 99.99, "credit_card")

// 订单取消
publisher.PublishOrderCancelled(ctx, "order456", "user_request")
```

**频道**: `events:order`

### 通知 (notification)

```go
// 邮件通知
publisher.PublishEmailNotification(ctx, "<EMAIL>", "Welcome!", "Welcome message")

// 短信通知
publisher.PublishSMSNotification(ctx, "+1234567890", "Your code is 123456")

// 推送通知
publisher.PublishPushNotification(ctx, "user123", "New Message", "You have a new message")
```

**频道**: 
- `notifications:email`
- `notifications:sms`
- `notifications:push`

## 自定义消息处理器

### 1. 实现MessageHandler接口

```go
type CustomHandler struct {
    logger *log.Logger
}

func (h *CustomHandler) Handle(ctx context.Context, msg *pubsub.Message) error {
    // 处理消息逻辑
    h.logger.Info("Processing custom message", "data", msg.Data)
    return nil
}

func (h *CustomHandler) GetType() string {
    return "custom_event"
}
```

### 2. 注册处理器

```go
// 在Job中添加自定义消费者
func (slf *Job) startCustomConsumer() {
    defer slf.wg.Done()
    
    handler := &CustomHandler{logger: slf.log}
    channels := []string{"events:custom"}
    
    for {
        select {
        case <-slf.ctx.Done():
            return
        default:
            err := slf.pubsub.Subscribe(slf.ctx, channels, handler)
            if err != nil && err != context.Canceled {
                slf.log.Error("Custom consumer error", "error", err)
                time.Sleep(5 * time.Second)
            }
        }
    }
}
```

## 配置说明

### Redis配置

```yaml
data:
  redis:
    addr: "localhost:6379"
    password: ""
    db: 0
    read_timeout: 2s
    write_timeout: 2s
```

### 日志配置

```yaml
log:
  log_level: info
  encoding: json
  log_file_name: "./storage/logs/pubsub.log"
```

## 最佳实践

### 1. 消息设计

- 保持消息结构简单
- 使用有意义的消息类型
- 包含必要的上下文信息

### 2. 错误处理

- 处理器中的错误不应该导致程序崩溃
- 记录详细的错误日志
- 考虑重试机制

### 3. 性能优化

- 避免在处理器中执行耗时操作
- 使用批量处理减少Redis连接数
- 监控消息处理延迟

### 4. 监控和调试

- 使用结构化日志
- 监控消息处理速度
- 设置告警机制

## 故障排除

### 常见问题

1. **Redis连接失败**
   - 检查Redis服务是否运行
   - 验证连接配置
   - 检查网络连接

2. **消息丢失**
   - 检查处理器是否正确注册
   - 验证消息类型匹配
   - 查看错误日志

3. **处理器异常**
   - 检查处理器实现
   - 验证消息数据格式
   - 添加错误处理

### 调试技巧

```go
// 启用详细日志
logger := log.NewLogger(&log.Config{
    LogLevel: "debug",
    Encoding: "console",
})

// 监控Redis连接
rdb.AddHook(&RedisHook{})
```

## 测试

```bash
# 运行单元测试
go test ./pkg/pubsub/...

# 运行基准测试
go test -bench=. ./pkg/pubsub/...

# 运行示例
go run examples/pubsub_example.go
```

## 扩展功能

- [ ] 消息持久化
- [ ] 死信队列
- [ ] 消息去重
- [ ] 批量处理
- [ ] 消息路由
- [ ] 性能监控面板
