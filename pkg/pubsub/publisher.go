package pubsub

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"zodiacus/pkg/log"
)

// MessagePublisher 消息发布器
type MessagePublisher struct {
	pubsub *PubSub
	logger *log.Logger
}

// NewMessagePublisher 创建消息发布器
func NewMessagePublisher(rdb *redis.Client, logger *log.Logger) *MessagePublisher {
	return &MessagePublisher{
		pubsub: NewPubSub(rdb, logger),
		logger: logger,
	}
}

// PublishUserEvent 发布用户事件
func (p *MessagePublisher) PublishUserEvent(ctx context.Context, action, userID string, extra map[string]interface{}) error {
	data := map[string]interface{}{
		"action":  action,
		"user_id": userID,
	}
	
	// 合并额外数据
	for k, v := range extra {
		data[k] = v
	}

	msg := &Message{
		ID:        uuid.New().String(),
		Type:      "user_event",
		Data:      data,
		Timestamp: time.Now(),
	}

	channel := GetChannel("events", "user")
	return p.pubsub.Publish(ctx, channel, msg)
}

// PublishOrderEvent 发布订单事件
func (p *MessagePublisher) PublishOrderEvent(ctx context.Context, action, orderID string, extra map[string]interface{}) error {
	data := map[string]interface{}{
		"action":   action,
		"order_id": orderID,
	}
	
	// 合并额外数据
	for k, v := range extra {
		data[k] = v
	}

	msg := &Message{
		ID:        uuid.New().String(),
		Type:      "order_event",
		Data:      data,
		Timestamp: time.Now(),
	}

	channel := GetChannel("events", "order")
	return p.pubsub.Publish(ctx, channel, msg)
}

// PublishNotification 发布通知
func (p *MessagePublisher) PublishNotification(ctx context.Context, notificationType string, data map[string]interface{}) error {
	// 确保包含通知类型
	data["type"] = notificationType

	msg := &Message{
		ID:        uuid.New().String(),
		Type:      "notification",
		Data:      data,
		Timestamp: time.Now(),
	}

	channel := GetChannel("notifications", notificationType)
	return p.pubsub.Publish(ctx, channel, msg)
}

// PublishEmailNotification 发布邮件通知
func (p *MessagePublisher) PublishEmailNotification(ctx context.Context, to, subject, content string) error {
	return p.PublishNotification(ctx, "email", map[string]interface{}{
		"to":      to,
		"subject": subject,
		"content": content,
	})
}

// PublishSMSNotification 发布短信通知
func (p *MessagePublisher) PublishSMSNotification(ctx context.Context, phone, content string) error {
	return p.PublishNotification(ctx, "sms", map[string]interface{}{
		"phone":   phone,
		"content": content,
	})
}

// PublishPushNotification 发布推送通知
func (p *MessagePublisher) PublishPushNotification(ctx context.Context, userID, title, content string) error {
	return p.PublishNotification(ctx, "push", map[string]interface{}{
		"user_id": userID,
		"title":   title,
		"content": content,
	})
}

// PublishCustomEvent 发布自定义事件
func (p *MessagePublisher) PublishCustomEvent(ctx context.Context, eventType, channel string, data map[string]interface{}) error {
	msg := &Message{
		ID:        uuid.New().String(),
		Type:      eventType,
		Data:      data,
		Timestamp: time.Now(),
	}

	return p.pubsub.Publish(ctx, channel, msg)
}

// 便捷方法：发布用户注册事件
func (p *MessagePublisher) PublishUserRegister(ctx context.Context, userID, email string) error {
	return p.PublishUserEvent(ctx, "register", userID, map[string]interface{}{
		"email": email,
	})
}

// 便捷方法：发布用户登录事件
func (p *MessagePublisher) PublishUserLogin(ctx context.Context, userID, ip string) error {
	return p.PublishUserEvent(ctx, "login", userID, map[string]interface{}{
		"ip": ip,
	})
}

// 便捷方法：发布用户登出事件
func (p *MessagePublisher) PublishUserLogout(ctx context.Context, userID string) error {
	return p.PublishUserEvent(ctx, "logout", userID, nil)
}

// 便捷方法：发布订单创建事件
func (p *MessagePublisher) PublishOrderCreated(ctx context.Context, orderID, userID string, amount float64) error {
	return p.PublishOrderEvent(ctx, "created", orderID, map[string]interface{}{
		"user_id": userID,
		"amount":  amount,
	})
}

// 便捷方法：发布订单支付事件
func (p *MessagePublisher) PublishOrderPaid(ctx context.Context, orderID string, amount float64, paymentMethod string) error {
	return p.PublishOrderEvent(ctx, "paid", orderID, map[string]interface{}{
		"amount":         amount,
		"payment_method": paymentMethod,
	})
}

// 便捷方法：发布订单取消事件
func (p *MessagePublisher) PublishOrderCancelled(ctx context.Context, orderID, reason string) error {
	return p.PublishOrderEvent(ctx, "cancelled", orderID, map[string]interface{}{
		"reason": reason,
	})
}

// GetPubSub 获取底层PubSub实例
func (p *MessagePublisher) GetPubSub() *PubSub {
	return p.pubsub
}
