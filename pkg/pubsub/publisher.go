package pubsub

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"zodiacus/pkg/log"
)

// MessagePublisher 消息发布器
type MessagePublisher struct {
	pubsub *PubSub
	logger *log.Logger
}

// NewMessagePublisher 创建消息发布器
func NewMessagePublisher(rdb *redis.Client, logger *log.Logger) *MessagePublisher {
	return &MessagePublisher{
		pubsub: NewPubSub(rdb, logger),
		logger: logger,
	}
}

// PublishMembershipChange 发布用户会员时长变更事件
func (p *MessagePublisher) PublishMembershipChange(ctx context.Context, userID string, extra map[string]interface{}) error {
	data := map[string]interface{}{
		"user_id": userID,
	}

	// 合并额外数据
	for k, v := range extra {
		data[k] = v
	}

	msg := &Message{
		ID:        uuid.New().String(),
		Type:      "membership_change",
		Data:      data,
		Timestamp: time.Now(),
	}

	channel := GetChannel("events", "membership")
	return p.pubsub.Publish(ctx, channel, msg)
}

// 便捷方法：发布会员升级事件
func (p *MessagePublisher) PublishMembershipUpgrade(ctx context.Context, userID, oldExpireTime, newExpireTime string) error {
	return p.PublishMembershipChange(ctx, userID, map[string]interface{}{
		"change_type":      "upgrade",
		"old_expire_time":  oldExpireTime,
		"new_expire_time":  newExpireTime,
	})
}

// 便捷方法：发布会员降级事件
func (p *MessagePublisher) PublishMembershipDowngrade(ctx context.Context, userID, oldExpireTime, newExpireTime string) error {
	return p.PublishMembershipChange(ctx, userID, map[string]interface{}{
		"change_type":      "downgrade",
		"old_expire_time":  oldExpireTime,
		"new_expire_time":  newExpireTime,
	})
}

// 便捷方法：发布会员延期事件
func (p *MessagePublisher) PublishMembershipExtend(ctx context.Context, userID, oldExpireTime, newExpireTime string) error {
	return p.PublishMembershipChange(ctx, userID, map[string]interface{}{
		"change_type":      "extend",
		"old_expire_time":  oldExpireTime,
		"new_expire_time":  newExpireTime,
	})
}

// 便捷方法：发布会员过期事件
func (p *MessagePublisher) PublishMembershipExpire(ctx context.Context, userID, expireTime string) error {
	return p.PublishMembershipChange(ctx, userID, map[string]interface{}{
		"change_type":      "expire",
		"old_expire_time":  expireTime,
		"new_expire_time":  "",
	})
}

// 便捷方法：发布会员激活事件
func (p *MessagePublisher) PublishMembershipActivate(ctx context.Context, userID, newExpireTime string) error {
	return p.PublishMembershipChange(ctx, userID, map[string]interface{}{
		"change_type":      "activate",
		"old_expire_time":  "",
		"new_expire_time":  newExpireTime,
	})
}

// GetPubSub 获取底层PubSub实例
func (p *MessagePublisher) GetPubSub() *PubSub {
	return p.pubsub
}
