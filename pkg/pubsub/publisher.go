package pubsub

import (
	"context"
	"github.com/redis/go-redis/v9"
	"zodiacus/pkg/log"
)

// MessagePublisher 消息发布器（简化版，专门用于会员时长变更）
type MessagePublisher struct {
	membershipPublisher *MembershipPublisher
	logger              *log.Logger
}

// NewMessagePublisher 创建消息发布器
func NewMessagePublisher(rdb *redis.Client, logger *log.Logger) *MessagePublisher {
	return &MessagePublisher{
		membershipPublisher: NewMembershipPublisher(rdb, logger),
		logger:              logger,
	}
}

// PublishMembershipChange 发布用户会员时长变更事件（简化版，只包含用户ID）
func (p *MessagePublisher) PublishMembershipChange(ctx context.Context, userID string) error {
	return p.membershipPublisher.PublishMembershipChange(ctx, userID)
}

// PublishMembershipChangeWithRetry 发布用户会员时长变更事件（带重试）
func (p *MessagePublisher) PublishMembershipChangeWithRetry(ctx context.Context, userID string, maxRetries int) error {
	return p.membershipPublisher.PublishMembershipChangeWithRetry(ctx, userID, maxRetries)
}

// PublishBatchMembershipChange 批量发布会员时长变更事件
func (p *MessagePublisher) PublishBatchMembershipChange(ctx context.Context, userIDs []string) error {
	return p.membershipPublisher.PublishBatchMembershipChange(ctx, userIDs)
}
