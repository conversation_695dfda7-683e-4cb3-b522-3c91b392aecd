package pubsub

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"zodiacus/pkg/log"
)

// MessagePublisher 消息发布器
type MessagePublisher struct {
	pubsub *PubSub
	logger *log.Logger
}

// NewMessagePublisher 创建消息发布器
func NewMessagePublisher(rdb *redis.Client, logger *log.Logger) *MessagePublisher {
	return &MessagePublisher{
		pubsub: NewPubSub(rdb, logger),
		logger: logger,
	}
}

// PublishQwRemarkUpdate 发布用户会员时长变更事件
func (p *MessagePublisher) PublishQwRemarkUpdate(ctx context.Context, userID string, extra map[string]interface{}) error {
	data := map[string]interface{}{
		"user_id": userID,
	}

	// 合并额外数据
	for k, v := range extra {
		data[k] = v
	}

	msg := &Message{
		ID:        uuid.New().String(),
		Type:      "membership_change",
		Data:      data,
		Timestamp: time.Now(),
	}

	channel := GetChannel("events", "membership")
	return p.pubsub.Publish(ctx, channel, msg)
}
