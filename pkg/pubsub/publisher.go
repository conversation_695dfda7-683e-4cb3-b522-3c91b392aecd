package pubsub

import (
	"context"
	"github.com/redis/go-redis/v9"
	"zodiacus/pkg/log"
)

// MessagePublisher 消息发布器
type MessagePublisher struct {
	pubsub *PubSub
	logger *log.Logger
}

// NewMessagePublisher 创建消息发布器
func NewMessagePublisher(rdb *redis.Client, logger *log.Logger) *MessagePublisher {
	return &MessagePublisher{
		pubsub: NewPubSub(rdb, logger),
		logger: logger,
	}
}

// PublishMembershipChange 发布用户会员时长变更事件（简化版，只包含用户ID）
func (p *MessagePublisher) PublishMembershipChange(ctx context.Context, userID string) error {
	return p.pubsub.PublishMembershipChange(ctx, userID)
}
