package pubsub

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"zodiacus/pkg/log"
)

// Message 消息结构
type Message struct {
	ID        string         `json:"id"`
	Type      string         `json:"type"`
	Data      map[string]any `json:"data"`
	Timestamp time.Time      `json:"timestamp"`
	Retry     int            `json:"retry"`
}

// MembershipChangeMessage 会员时长变更消息
type MembershipChangeMessage struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	Timestamp time.Time `json:"timestamp"`
	Retry     int       `json:"retry"`
}

// MessageHandler 消息处理器接口
type MessageHandler interface {
	Handle(ctx context.Context, msg *Message) error
	GetType() string
}

// MembershipChangeHandler 会员时长变更处理器接口
type MembershipChangeHandler interface {
	HandleMembershipChange(ctx context.Context, msg *MembershipChangeMessage) error
}

// Publisher 发布器接口
type Publisher interface {
	Publish(ctx context.Context, channel string, msg *Message) error
}

// Subscriber 订阅器接口
type Subscriber interface {
	Subscribe(ctx context.Context, channels []string, handler MessageHandler) error
	Close() error
}

// PubSub Redis发布订阅服务
type PubSub struct {
	rdb    *redis.Client
	logger *log.Logger
}

// NewPubSub 创建新的PubSub实例
func NewPubSub(rdb *redis.Client, logger *log.Logger) *PubSub {
	return &PubSub{
		rdb:    rdb,
		logger: logger,
	}
}

// Publish 发布消息
func (p *PubSub) Publish(ctx context.Context, channel string, msg *Message) error {
	if msg.Timestamp.IsZero() {
		msg.Timestamp = time.Now()
	}
	data, err := json.Marshal(msg)
	if err != nil {
		p.logger.Error("Failed to marshal message", zap.Error(err), zap.Any("message", msg))
		return fmt.Errorf("failed to marshal message: %w", err)
	}
	if err = p.rdb.Publish(ctx, channel, data).Err(); err != nil {
		p.logger.Error("Failed to publish message", zap.Error(err), zap.String("channel", channel))
		return fmt.Errorf("failed to publish message to channel %s: %w", channel, err)
	}
	p.logger.Info("Message published successfully",
		zap.String("channel", channel),
		zap.String("messageId", msg.ID),
		zap.String("messageType", msg.Type))
	return nil
}

// Subscribe 订阅消息
func (p *PubSub) Subscribe(ctx context.Context, channels []string, handler MessageHandler) error {
	pubsub := p.rdb.Subscribe(ctx, channels...)
	defer pubsub.Close()

	p.logger.Info("Started subscribing to channels",
		zap.Strings("channels", channels),
		zap.String("handlerType", handler.GetType()))

	// 接收消息
	ch := pubsub.Channel()
	for {
		select {
		case <-ctx.Done():
			p.logger.Info("Subscription context cancelled", zap.Strings("channels", channels))
			return ctx.Err()
		case redisMsg := <-ch:
			if redisMsg == nil {
				continue
			}

			// 解析消息
			var msg Message
			if err := json.Unmarshal([]byte(redisMsg.Payload), &msg); err != nil {
				p.logger.Error("Failed to unmarshal message",
					zap.Error(err),
					zap.String("payload", redisMsg.Payload),
					zap.String("channel", redisMsg.Channel))
				continue
			}

			// 检查消息类型是否匹配
			if msg.Type != handler.GetType() {
				p.logger.Debug("Message type mismatch, skipping",
					zap.String("expectedType", handler.GetType()),
					zap.String("actualType", msg.Type))
				continue
			}

			// 处理消息
			if err := p.handleMessage(ctx, &msg, handler); err != nil {

			}
		}
	}
}

// handleMessage 处理单个消息
func (p *PubSub) handleMessage(ctx context.Context, msg *Message, handler MessageHandler) error {
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		p.logger.Info("处理消息完成",
			zap.String("消息ID", msg.ID),
			zap.String("消息类型", msg.Type),
			zap.Duration("处理耗时", duration))
	}()
	if err := handler.Handle(ctx, msg); err != nil {
		msg.Retry++
		return err
	}
	return nil
}

// Close 关闭PubSub连接
func (p *PubSub) Close() error {
	return p.rdb.Close()
}

// GetChannelPattern 获取频道模式
func GetChannelPattern(prefix string) string {
	return fmt.Sprintf("%s:*", prefix)
}

// GetChannel 获取具体频道名
func GetChannel(prefix, suffix string) string {
	return fmt.Sprintf("%s:%s", prefix, suffix)
}

// PublishMembershipChange 发布会员时长变更消息
func (p *PubSub) PublishMembershipChange(ctx context.Context, userID string) error {
	// 创建会员时长变更消息
	msg := &MembershipChangeMessage{
		ID:        fmt.Sprintf("membership_%d", time.Now().UnixNano()),
		UserID:    userID,
		Timestamp: time.Now(),
		Retry:     0,
	}
	data, err := json.Marshal(msg)
	if err != nil {
		p.logger.Error("Failed to marshal membership change message", zap.Error(err), zap.Any("message", msg))
		return fmt.Errorf("failed to marshal membership change message: %w", err)
	}
	channel := "membership:change"
	err = p.rdb.Publish(ctx, channel, data).Err()
	if err != nil {
		p.logger.Error("Failed to publish membership change message", zap.Error(err), zap.String("channel", channel))
		return fmt.Errorf("failed to publish membership change message to channel %s: %w", channel, err)
	}
	p.logger.Info("Membership change message published successfully",
		zap.String("channel", channel),
		zap.String("messageId", msg.ID),
		zap.String("userId", msg.UserID))

	return nil
}

// SubscribeMembershipChange 订阅会员时长变更消息
func (p *PubSub) SubscribeMembershipChange(ctx context.Context, handler MembershipChangeHandler) error {
	// 创建订阅
	channel := "membership:change"
	pubsub := p.rdb.Subscribe(ctx, channel)
	defer pubsub.Close()

	p.logger.Info("Started subscribing to membership change channel",
		zap.String("channel", channel))

	// 接收消息
	ch := pubsub.Channel()
	for {
		select {
		case <-ctx.Done():
			p.logger.Info("Membership change subscription context cancelled", zap.String("channel", channel))
			return ctx.Err()
		case redisMsg := <-ch:
			if redisMsg == nil {
				continue
			}

			// 解析消息
			var msg MembershipChangeMessage
			if err := json.Unmarshal([]byte(redisMsg.Payload), &msg); err != nil {
				p.logger.Error("Failed to unmarshal membership change message",
					zap.Error(err),
					zap.String("payload", redisMsg.Payload),
					zap.String("channel", redisMsg.Channel))
				continue
			}

			// 处理消息
			if err := p.handleMembershipChangeMessage(ctx, &msg, handler); err != nil {
				p.logger.Error("Failed to handle membership change message",
					zap.Error(err),
					zap.String("messageId", msg.ID),
					zap.String("userId", msg.UserID),
					zap.String("channel", redisMsg.Channel))
			}
		}
	}
}

// handleMembershipChangeMessage 处理单个会员时长变更消息
func (p *PubSub) handleMembershipChangeMessage(ctx context.Context, msg *MembershipChangeMessage, handler MembershipChangeHandler) error {
	startTime := time.Now()

	defer func() {
		duration := time.Since(startTime)
		p.logger.Info("Membership change message processing completed",
			zap.String("messageId", msg.ID),
			zap.String("userId", msg.UserID),
			zap.Duration("duration", duration))
	}()

	// 执行处理器
	err := handler.HandleMembershipChange(ctx, msg)
	if err != nil {
		// 增加重试次数
		msg.Retry++
		p.logger.Error("Membership change message handler failed",
			zap.Error(err),
			zap.String("messageId", msg.ID),
			zap.String("userId", msg.UserID),
			zap.Int("retryCount", msg.Retry))
		return err
	}

	p.logger.Info("Membership change message handled successfully",
		zap.String("messageId", msg.ID),
		zap.String("userId", msg.UserID))

	return nil
}
