package pubsub

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"zodiacus/pkg/log"
)

// Message 消息结构
type Message struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	Retry     int                    `json:"retry"`
}

// MessageHandler 消息处理器接口
type MessageHandler interface {
	Handle(ctx context.Context, msg *Message) error
	GetType() string
}

// Publisher 发布器接口
type Publisher interface {
	Publish(ctx context.Context, channel string, msg *Message) error
}

// Subscriber 订阅器接口
type Subscriber interface {
	Subscribe(ctx context.Context, channels []string, handler MessageHandler) error
	Close() error
}

// PubSub Redis发布订阅服务
type PubSub struct {
	rdb    *redis.Client
	logger *log.Logger
}

// NewPubSub 创建新的PubSub实例
func NewPubSub(rdb *redis.Client, logger *log.Logger) *PubSub {
	return &PubSub{
		rdb:    rdb,
		logger: logger,
	}
}

// Publish 发布消息
func (p *PubSub) Publish(ctx context.Context, channel string, msg *Message) error {
	// 设置消息时间戳
	if msg.Timestamp.IsZero() {
		msg.Timestamp = time.Now()
	}

	// 序列化消息
	data, err := json.Marshal(msg)
	if err != nil {
		p.logger.Error("Failed to marshal message", zap.Error(err), zap.Any("message", msg))
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	// 发布到Redis
	err = p.rdb.Publish(ctx, channel, data).Err()
	if err != nil {
		p.logger.Error("Failed to publish message", zap.Error(err), zap.String("channel", channel))
		return fmt.Errorf("failed to publish message to channel %s: %w", channel, err)
	}

	p.logger.Info("Message published successfully",
		zap.String("channel", channel),
		zap.String("messageId", msg.ID),
		zap.String("messageType", msg.Type))
	
	return nil
}

// Subscribe 订阅消息
func (p *PubSub) Subscribe(ctx context.Context, channels []string, handler MessageHandler) error {
	// 创建订阅
	pubsub := p.rdb.Subscribe(ctx, channels...)
	defer pubsub.Close()

	p.logger.Info("Started subscribing to channels",
		zap.Strings("channels", channels),
		zap.String("handlerType", handler.GetType()))

	// 接收消息
	ch := pubsub.Channel()
	for {
		select {
		case <-ctx.Done():
			p.logger.Info("Subscription context cancelled", zap.Strings("channels", channels))
			return ctx.Err()
		case redisMsg := <-ch:
			if redisMsg == nil {
				continue
			}
			
			// 解析消息
			var msg Message
			if err := json.Unmarshal([]byte(redisMsg.Payload), &msg); err != nil {
				p.logger.Error("Failed to unmarshal message",
					zap.Error(err),
					zap.String("payload", redisMsg.Payload),
					zap.String("channel", redisMsg.Channel))
				continue
			}

			// 检查消息类型是否匹配
			if msg.Type != handler.GetType() {
				p.logger.Debug("Message type mismatch, skipping",
					zap.String("expectedType", handler.GetType()),
					zap.String("actualType", msg.Type))
				continue
			}

			// 处理消息
			if err := p.handleMessage(ctx, &msg, handler); err != nil {
				p.logger.Error("Failed to handle message",
					zap.Error(err),
					zap.String("messageId", msg.ID),
					zap.String("messageType", msg.Type),
					zap.String("channel", redisMsg.Channel))
			}
		}
	}
}

// handleMessage 处理单个消息
func (p *PubSub) handleMessage(ctx context.Context, msg *Message, handler MessageHandler) error {
	startTime := time.Now()
	
	defer func() {
		duration := time.Since(startTime)
		p.logger.Info("Message processing completed",
			zap.String("messageId", msg.ID),
			zap.String("messageType", msg.Type),
			zap.Duration("duration", duration))
	}()

	// 执行处理器
	err := handler.Handle(ctx, msg)
	if err != nil {
		// 增加重试次数
		msg.Retry++
		p.logger.Error("Message handler failed",
			zap.Error(err),
			zap.String("messageId", msg.ID),
			zap.String("messageType", msg.Type),
			zap.Int("retryCount", msg.Retry))
		return err
	}

	p.logger.Info("Message handled successfully",
		zap.String("messageId", msg.ID),
		zap.String("messageType", msg.Type))
	
	return nil
}

// Close 关闭PubSub连接
func (p *PubSub) Close() error {
	return p.rdb.Close()
}

// GetChannelPattern 获取频道模式
func GetChannelPattern(prefix string) string {
	return fmt.Sprintf("%s:*", prefix)
}

// GetChannel 获取具体频道名
func GetChannel(prefix, suffix string) string {
	return fmt.Sprintf("%s:%s", prefix, suffix)
}
