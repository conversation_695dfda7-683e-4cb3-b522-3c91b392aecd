package pubsub

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"zodiacus/pkg/log"
)

// MembershipPublisher 会员时长变更消息发布器
type MembershipPublisher struct {
	rdb    *redis.Client
	logger *log.Logger
}

// NewMembershipPublisher 创建会员时长变更消息发布器
func NewMembershipPublisher(rdb *redis.Client, logger *log.Logger) *MembershipPublisher {
	return &MembershipPublisher{
		rdb:    rdb,
		logger: logger,
	}
}

// PublishMembershipChange 发布会员时长变更消息
func (p *MembershipPublisher) PublishMembershipChange(ctx context.Context, userID string) error {
	// 创建会员时长变更消息
	msg := &MembershipChangeMessage{
		ID:        fmt.Sprintf("membership_%d", time.Now().UnixNano()),
		UserID:    userID,
		Timestamp: time.Now(),
		Retry:     0,
	}

	// 序列化消息
	data, err := json.Marshal(msg)
	if err != nil {
		p.logger.Error("Failed to marshal membership change message", zap.Error(err), zap.Any("message", msg))
		return fmt.Errorf("failed to marshal membership change message: %w", err)
	}

	// 发布到Redis
	channel := "membership:change"
	err = p.rdb.Publish(ctx, channel, data).Err()
	if err != nil {
		p.logger.Error("Failed to publish membership change message", zap.Error(err), zap.String("channel", channel))
		return fmt.Errorf("failed to publish membership change message to channel %s: %w", channel, err)
	}

	p.logger.Info("Membership change message published successfully", 
		zap.String("channel", channel), 
		zap.String("messageId", msg.ID), 
		zap.String("userId", msg.UserID))
	
	return nil
}

// PublishMembershipChangeWithRetry 发布会员时长变更消息（带重试）
func (p *MembershipPublisher) PublishMembershipChangeWithRetry(ctx context.Context, userID string, maxRetries int) error {
	var lastErr error
	
	for i := 0; i <= maxRetries; i++ {
		err := p.PublishMembershipChange(ctx, userID)
		if err == nil {
			return nil
		}
		
		lastErr = err
		p.logger.Warn("Failed to publish membership change message, retrying...", 
			zap.Error(err),
			zap.String("userId", userID),
			zap.Int("attempt", i+1),
			zap.Int("maxRetries", maxRetries))
		
		if i < maxRetries {
			// 等待一段时间后重试
			time.Sleep(time.Duration(i+1) * time.Second)
		}
	}
	
	return fmt.Errorf("failed to publish membership change message after %d retries: %w", maxRetries, lastErr)
}

// PublishBatchMembershipChange 批量发布会员时长变更消息
func (p *MembershipPublisher) PublishBatchMembershipChange(ctx context.Context, userIDs []string) error {
	if len(userIDs) == 0 {
		return nil
	}

	p.logger.Info("Publishing batch membership change messages", zap.Int("count", len(userIDs)))

	var errors []error
	successCount := 0

	for _, userID := range userIDs {
		err := p.PublishMembershipChange(ctx, userID)
		if err != nil {
			errors = append(errors, fmt.Errorf("failed to publish for user %s: %w", userID, err))
			p.logger.Error("Failed to publish membership change for user", 
				zap.Error(err), 
				zap.String("userId", userID))
		} else {
			successCount++
		}
	}

	p.logger.Info("Batch membership change publishing completed", 
		zap.Int("total", len(userIDs)),
		zap.Int("success", successCount),
		zap.Int("failed", len(errors)))

	if len(errors) > 0 {
		return fmt.Errorf("batch publishing failed for %d users: %v", len(errors), errors)
	}

	return nil
}

// GetChannelName 获取会员时长变更频道名
func (p *MembershipPublisher) GetChannelName() string {
	return "membership:change"
}

// Close 关闭发布器
func (p *MembershipPublisher) Close() error {
	return p.rdb.Close()
}
