package jwthub

import (
	"context"
	"fmt"
	"github.com/golang-jwt/jwt/v4"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
	"strings"
	"time"
)

type Claim struct {
	Username string `json:"username,omitempty"`
	jwt.RegisteredClaims
}

type Auth struct {
	TokenID  string `json:"token_id"`
	UserID   string `json:"user_id"`
	Username string `json:"username"`
}

func (slf *Claim) TokenID() string {
	return slf.ID
}

func (slf *Claim) UserID() string {
	return slf.Subject
}

func (slf *Claim) Auth() *Auth {
	return &Auth{
		TokenID:  slf.ID,
		UserID:   slf.Subject,
		Username: slf.Username,
	}
}

type Jwthub struct {
	prefix4App   string
	prefix4Admin string
	jwtKey       []byte
	rdb          *redis.Client
}

func NewJwthub(conf *viper.Viper, rdb *redis.Client) *Jwthub {
	return &Jwthub{
		prefix4App:   conf.GetString("security.jwthub.appPrefix"),
		prefix4Admin: conf.GetString("security.jwthub.adminPrefix"),
		jwtKey:       []byte(conf.GetString("security.jwthub.jwtKey")),
		rdb:          rdb,
	}
}

func (slf *Jwthub) tokenKey(tokenID string) string {
	return fmt.Sprintf("%s:token:%s", slf.prefix4App, tokenID)
}

func (slf *Jwthub) userTokenKey(userID string) string {
	return fmt.Sprintf("%s:user_token:%s", slf.prefix4App, userID)
}

func (slf *Jwthub) setToken(ctx context.Context, claim *Claim, token string) error {
	var (
		tokenID    = claim.TokenID()
		userID     = claim.UserID()
		expiresAt  = claim.ExpiresAt.Time
		expireTs   = float64(expiresAt.Unix())
		expireBuff = time.Minute
		current    = time.Now()
	)

	tokenKey := slf.tokenKey(tokenID)
	userTokenKey := slf.userTokenKey(userID)

	pipe := slf.rdb.Pipeline()
	pipe.ZAdd(ctx, userTokenKey, redis.Z{
		Score:  expireTs,
		Member: tokenID,
	})
	ttl := time.Until(expiresAt)
	pipe.Set(ctx, tokenKey, token, ttl)
	maxScore := pipe.ZRevRangeWithScores(ctx, userTokenKey, 0, 0)
	_, err := pipe.Exec(ctx)
	if err != nil {
		return err
	}

	if len(maxScore.Val()) > 0 {
		currMaxScore := maxScore.Val()[0].Score
		if expireTs > currMaxScore {
			newExpire := time.Duration(expireTs)*time.Second + expireBuff
			slf.rdb.Expire(ctx, userTokenKey, newExpire)
		}
	} else {
		newExpire := time.Duration(expireTs)*time.Second + expireBuff
		slf.rdb.Expire(ctx, userTokenKey, newExpire)
	}
	slf.rdb.ZRemRangeByScore(ctx, userTokenKey, "-inf", fmt.Sprintf("(%d", current.Unix()))

	return nil
}

func (slf *Jwthub) getToken(ctx context.Context, tokenID string) (string, error) {
	tokenKey := slf.tokenKey(tokenID)
	val, err := slf.rdb.Get(ctx, tokenKey).Result()
	if errors.Is(err, redis.Nil) {
		return "", nil
	}
	return val, err
}

func (slf *Jwthub) delToken(ctx context.Context, tokenID, userID string) error {
	userTokenKey := slf.userTokenKey(userID)
	tokenKey := slf.tokenKey(tokenID)
	pipe := slf.rdb.Pipeline()
	pipe.Del(ctx, tokenKey)
	pipe.ZRem(ctx, userTokenKey, tokenID)
	_, err := pipe.Exec(ctx)
	return err
}

func (slf *Jwthub) delAllTokens(ctx context.Context, userID string) error {
	userTokenKey := slf.userTokenKey(userID)
	tokenIDs, err := slf.rdb.ZRange(ctx, userTokenKey, 0, -1).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return fmt.Errorf("failed to get tokenIDs: %w", err)
	}
	var kvKeys []string
	for _, tokenID := range tokenIDs {
		kvKeys = append(kvKeys, slf.tokenKey(tokenID))
	}
	pipe := slf.rdb.TxPipeline()
	pipe.Del(ctx, userTokenKey)
	if len(kvKeys) > 0 {
		pipe.Del(ctx, kvKeys...)
	}
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to delete tokens: %w", err)
	}
	return nil
}

func (slf *Jwthub) DelToken(ctx context.Context, tokenID, userID string) error {
	return slf.delToken(ctx, tokenID, userID)
}

func (slf *Jwthub) DelAllTokens(ctx context.Context, userID string) error {
	return slf.delAllTokens(ctx, userID)
}

func (slf *Jwthub) GenToken(ctx context.Context, id, userID string, username string, expiresAt time.Time) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, Claim{
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "",
			Subject:   userID,
			ID:        id,
			Audience:  []string{},
		},
	})
	signed, err := token.SignedString(slf.jwtKey)
	if err != nil {
		return "", err
	}
	claim := token.Claims.(Claim)
	if err = slf.setToken(ctx, &claim, signed); err != nil {
		return "", err
	}
	return signed, nil
}

func (slf *Jwthub) ParseToken(ctx context.Context, tokenStr string) (*Claim, error) {
	tokenStr = strings.TrimPrefix(tokenStr, "Bearer ")
	if strings.TrimSpace(tokenStr) == "" {
		return nil, errors.New("token is empty")
	}
	token, err := jwt.ParseWithClaims(tokenStr, &Claim{}, func(token *jwt.Token) (any, error) {
		return slf.jwtKey, nil
	})
	if err != nil {
		return nil, err
	}
	if claims, ok := token.Claims.(*Claim); ok && token.Valid {
		if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
			return nil, errors.New("token is expired")
		}
		getToken, err := slf.getToken(ctx, claims.TokenID())
		if err != nil {
			return nil, err
		}
		if getToken == "" {
			return nil, errors.New("token is not found")
		}
		return claims, nil
	} else {
		return nil, err
	}
}
