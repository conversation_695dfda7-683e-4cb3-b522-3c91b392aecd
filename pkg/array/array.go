package array

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"sort"

	"golang.org/x/exp/constraints"
)

func Filter[T any](arr []T, f func(T) bool) []T {
	var result []T
	for _, v := range arr {
		if f(v) {
			result = append(result, v)
		}
	}
	return result
}

func Exist[T any](arr []T, f func(T) bool) bool {
	for _, v := range arr {
		if f(v) {
			return true
		}
	}
	return false
}

func Range[T any](arr []T, f func(T)) {
	for _, v := range arr {
		f(v)
	}
}

func RangeWithIndex[T any](arr []T, f func(int, T)) {
	for i, v := range arr {
		f(i, v)
	}
}

func SubArray[T any](arr []T, indexes ...int) []T {
	var result []T
	if len(indexes) == 0 {
		return result
	}
	for _, i := range indexes {
		if i >= 0 && i < len(arr) {
			result = append(result, arr[i])
		}
	}
	return result
}

func Unique[T comparable](arr []T) []T {
	var (
		result []T
		seen   = make(map[T]struct{})
	)
	for _, v := range arr {
		if _, ok := seen[v]; !ok {
			seen[v] = struct{}{}
			result = append(result, v)
		}
	}
	return result
}

func Copy[T any](arr []T, fn ...func(T) T) []T {
	var result []T
	for _, v := range arr {
		if len(fn) > 0 {
			result = append(result, fn[0](v))
		} else {
			result = append(result, v)
		}
	}
	return result
}

func SubArrayExist(target []string, sub []string) bool {
	subMap := make(map[string]struct{})
	for _, s := range sub {
		subMap[s] = struct{}{}
	}
	for _, t := range target {
		if _, ok := subMap[t]; !ok {
			return false
		}
	}
	return true
}

func Merge[T any](arr ...[]T) []T {
	var result []T
	for _, v := range arr {
		result = append(result, v...)
	}
	return result
}

func Equal[T comparable](items ...T) bool {
	if len(items) < 2 {
		return true
	}
	first := items[0]
	for _, item := range items[1:] {
		if item != first {
			return false
		}
	}
	return true
}

func EqualTarget[T comparable](target T, items ...T) bool {
	for _, item := range items {
		if item != target {
			return false
		}
	}
	return true
}

func Has[T comparable](arr []T, target ...T) bool {
	if len(target) == 0 {
		return false
	}
	m := make(map[T]struct{})
	for _, t := range arr {
		m[t] = struct{}{}
	}
	for _, v := range target {
		if _, ok := m[v]; !ok {
			return false
		}
	}
	return true
}

func Count[T comparable](arr []T, target ...T) int {
	if len(target) == 0 {
		return 0
	}
	count := 0
	m := make(map[T]struct{})
	for _, t := range target {
		m[t] = struct{}{}
	}
	for _, v := range arr {
		if _, ok := m[v]; ok {
			count++
		}
	}
	return count
}

func Index[T comparable](arr []T, target T, idx ...int) int {
	if len(idx) == 0 {
		idx = make([]int, len(arr))
		for i := range idx {
			idx[i] = i
		}
	}
	for _, i := range idx {
		if arr[i] == target {
			return i
		}
	}
	return -1
}

func HasAny[T comparable](arr []T, targets ...T) bool {
	if len(targets) == 0 {
		return false
	}
	for _, v := range arr {
		for _, t := range targets {
			if v == t {
				return true
			}
		}
	}
	return false
}

func Remove[T comparable](arr []T, target T, n ...int) []T {
	var result []T
	limit := -1
	if len(n) > 0 {
		limit = n[0]
	}
	count := 0
	for _, v := range arr {
		if v == target && (limit == -1 || count < limit) {
			count++
			continue
		}
		result = append(result, v)
	}
	return result
}

func Combinations[T comparable](arr []T, n int) [][]T {
	arr = Unique(arr)
	var result [][]T
	var comb []T
	var dfs func(start int)

	dfs = func(start int) {
		if len(comb) == n {
			temp := make([]T, n)
			copy(temp, comb)
			result = append(result, temp)
			return
		}
		for i := start; i < len(arr); i++ {
			comb = append(comb, arr[i])
			dfs(i + 1)
			comb = comb[:len(comb)-1]
		}
	}
	dfs(0)
	return result
}

func Hashed[T constraints.Ordered](arr []T) string {
	sort.Slice(arr, func(i, j int) bool {
		return arr[i] < arr[j]
	})
	marshal, err := json.Marshal(arr)
	if err != nil {
		return ""
	}
	hash := sha256.Sum256(marshal)
	return hex.EncodeToString(hash[:])
}
