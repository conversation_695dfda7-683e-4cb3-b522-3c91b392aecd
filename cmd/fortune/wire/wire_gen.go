// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/spf13/viper"
	"zodiacus/internal/handler"
	"zodiacus/internal/handler/fortune"
	"zodiacus/internal/repository"
	"zodiacus/internal/server"
	"zodiacus/internal/service"
	"zodiacus/pkg/app"
	"zodiacus/pkg/geoip"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/pkg/oss/aliyun"
	"zodiacus/pkg/server/http"
	"zodiacus/pkg/sid"
	"zodiacus/third_party/aliyun/sms"
	"zodiacus/third_party/casdoor"
	"zodiacus/third_party/corona"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	client := casdoor.NewClient(viperViper)
	baseHandler := handler.NewHandler(logger)
	db := repository.NewDB(viperViper, logger)
	redisClient := repository.NewRedis(viperViper)
	repositoryRepository := repository.NewRepository(logger, db, redisClient)
	transaction := repository.NewTransaction(repositoryRepository)
	sidSid := sid.NewSid()
	aliyun_smsClient := aliyun_sms.NewClient(viperViper, logger)
	coronaClient := corona.NewClient(viperViper, aliyun_smsClient, redisClient, logger)
	geoipClient := geoip.NewClient(viperViper)
	aliyun_ossClient := aliyun_oss.NewClient(viperViper)
	jwthubJwthub := jwthub.NewJwthub(viperViper, redisClient)
	serviceService := service.NewService(transaction, logger, sidSid, coronaClient, geoipClient, aliyun_ossClient, jwthubJwthub)
	datetimeService := service.NewDatetimeService(serviceService)
	dateTimeHandler := fortune.NewDateTimeHandler(baseHandler, datetimeService)
	yunshiRepository := repository.NewYunshiRepository(repositoryRepository)
	userPaipanRecordRepository := repository.NewUserPaipanRecordRepository(repositoryRepository)
	yunshiService := service.NewYunshiService(serviceService, yunshiRepository, userPaipanRecordRepository)
	yunshiHandler := fortune.NewYunshiHandler(baseHandler, yunshiService)
	enumsRepository := repository.NewEnumsRepository(repositoryRepository)
	locationService := service.NewLocationService(serviceService, enumsRepository)
	locationHandler := fortune.NewLocationHandler(baseHandler, locationService)
	enumsService := service.NewEnumsService(serviceService, enumsRepository)
	enumsHandler := fortune.NewEnumsHandler(baseHandler, enumsService)
	dateRepository := repository.NewDateRepository(repositoryRepository)
	userMingliRepository := repository.NewUserMingliRepository(repositoryRepository)
	userMingliGroupRepository := repository.NewUserMingliGroupRepository(repositoryRepository)
	dateService := service.NewDateService(serviceService, dateRepository, enumsRepository, userMingliRepository, userMingliGroupRepository)
	dateHandler := fortune.NewDateHandler(baseHandler, dateService)
	appRepository := repository.NewAppRepository(repositoryRepository)
	appVersionRepository := repository.NewAppVersionRepository(repositoryRepository)
	appVersionService := service.NewAppVersionService(serviceService, appRepository, appVersionRepository)
	appVersionHandler := fortune.NewAppVersionHandler(baseHandler, appVersionService)
	paipanRecordService := service.NewPaipanRecordService(serviceService, userPaipanRecordRepository)
	paipanRecordHandler := fortune.NewPaipanRecordHandler(baseHandler, paipanRecordService)
	httpServer := server.NewFortuneHTTPServer(logger, viperViper, client, dateTimeHandler, yunshiHandler, locationHandler, enumsHandler, dateHandler, appVersionHandler, paipanRecordHandler, jwthubJwthub)
	job := server.NewJob(logger)
	appApp := newApp(httpServer, job)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRedis, repository.NewRepository, repository.NewTransaction, repository.NewYunshiRepository, repository.NewEnumsRepository, repository.NewUserPaipanRecordRepository, repository.NewDateRepository, repository.NewUserMingliRepository, repository.NewUserMingliGroupRepository, repository.NewAppVersionRepository, repository.NewAppRepository)

var serviceSet = wire.NewSet(service.NewService, service.NewYunshiService, service.NewLocationService, service.NewEnumsService, service.NewDatetimeService, service.NewDateService, service.NewAppVersionService, service.NewPaipanRecordService)

var handlerSet = wire.NewSet(handler.NewHandler, fortune.NewYunshiHandler, fortune.NewEnumsHandler, fortune.NewLocationHandler, fortune.NewDateTimeHandler, fortune.NewDateHandler, fortune.NewAppVersionHandler, fortune.NewPaipanRecordHandler)

var serverSet = wire.NewSet(server.NewFortuneHTTPServer, server.NewJob)

// build App
func newApp(
	httpServer *http.Server,
	job *server.Job,

) *app.App {
	return app.NewApp(app.WithServer(httpServer, job), app.WithName("yunshi-app-api"))
}
