//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/spf13/viper"
	"zodiacus/internal/handler"
	"zodiacus/internal/handler/wealth"
	"zodiacus/internal/repository"
	"zodiacus/internal/server"
	"zodiacus/internal/service"
	"zodiacus/pkg/app"
	"zodiacus/pkg/geoip"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	aliyun_oss "zodiacus/pkg/oss/aliyun"
	"zodiacus/pkg/server/http"
	"zodiacus/pkg/sid"
	aliyun_sms "zodiacus/third_party/aliyun/sms"
	"zodiacus/third_party/casdoor"
	"zodiacus/third_party/corona"
)

var repositorySet = wire.NewSet(
	repository.NewDB,
	repository.NewRedis,
	repository.NewRepository,
	repository.NewTransaction,
	repository.NewLuncaiRepository,
	repository.NewEnumsRepository,
	repository.NewUserPaipanRecordRepository,
	repository.NewVIPRepository,
	repository.NewUserOrderRepository,
	repository.NewDateRepository,
	repository.NewUserMingliRepository,
	repository.NewUserMingliGroupRepository,
	repository.NewAppVersionRepository,
	repository.NewAppRepository,
)

var serviceSet = wire.NewSet(
	service.NewService,
	service.NewLuncaiService,
	service.NewLocationService,
	service.NewEnumsService,
	service.NewDatetimeService,
	service.NewPaipanRecordService,
	service.NewDateService,
	service.NewAppVersionService,
)

var handlerSet = wire.NewSet(
	handler.NewHandler,
	wealth.NewLuncaiHandler,
	wealth.NewDateTimeHandler,
	wealth.NewLocationHandler,
	wealth.NewEnumsHandler,
	wealth.NewPaipanRecordHandler,
	wealth.NewDateHandler,
	wealth.NewAppVersionHandler,
)

var serverSet = wire.NewSet(
	server.NewWealthHTTPServer,
	server.NewJob,
)

// build App
func newApp(
	httpServer *http.Server,
	job *server.Job,
	// task *server.Task,
) *app.App {
	return app.NewApp(
		app.WithServer(httpServer, job),
		app.WithName("luncai-app-api"),
	)
}

func NewWire(*viper.Viper, *log.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		repositorySet,
		serviceSet,
		handlerSet,
		serverSet,
		sid.NewSid,
		casdoor.NewClient,
		aliyun_sms.NewClient,
		corona.NewClient,
		geoip.NewClient,
		aliyun_oss.NewClient,
		jwthub.NewJwthub,
		newApp,
	))
}
