// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/spf13/viper"
	"zodiacus/internal/handler"
	"zodiacus/internal/handler/cms"
	"zodiacus/internal/repository"
	"zodiacus/internal/server"
	"zodiacus/internal/service"
	"zodiacus/pkg/app"
	"zodiacus/pkg/geoip"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/pkg/oss/aliyun"
	"zodiacus/pkg/server/http"
	"zodiacus/pkg/sid"
	"zodiacus/third_party/aliyun/sms"
	"zodiacus/third_party/casdoor"
	"zodiacus/third_party/corona"
	"zodiacus/third_party/miniprogram"
	"zodiacus/third_party/offiaccount"
	"zodiacus/third_party/submail"
	"zodiacus/third_party/wecom"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	baseHandler := handler.NewHandler(logger)
	db := repository.NewDB(viperViper, logger)
	client := repository.NewRedis(viperViper)
	repositoryRepository := repository.NewRepository(logger, db, client)
	transaction := repository.NewTransaction(repositoryRepository)
	sidSid := sid.NewSid()
	aliyun_smsClient := aliyun_sms.NewClient(viperViper, logger)
	coronaClient := corona.NewClient(viperViper, aliyun_smsClient, client, logger)
	geoipClient := geoip.NewClient(viperViper)
	aliyun_ossClient := aliyun_oss.NewClient(viperViper)
	jwthubJwthub := jwthub.NewJwthub(viperViper, client)
	serviceService := service.NewService(transaction, logger, sidSid, coronaClient, geoipClient, aliyun_ossClient, jwthubJwthub)
	appUserRepository := repository.NewAppUserRepository(repositoryRepository)
	appUserService := service.NewAppUserService(serviceService, appUserRepository)
	appUserHandler := cms.NewAppUserHandler(baseHandler, appUserService)
	mingliRuleRepository := repository.NewMingliRuleRepository(repositoryRepository)
	mingliRuleConditionRepository := repository.NewMingliRuleConditionRepository(repositoryRepository)
	enumsRepository := repository.NewEnumsRepository(repositoryRepository)
	mingliRuleService := service.NewMingliRuleService(serviceService, mingliRuleRepository, mingliRuleConditionRepository, enumsRepository)
	mingliRuleHandler := cms.NewMingliRuleHandler(baseHandler, mingliRuleService)
	mingliRuleConditionService := service.NewMingliRuleConditionService(serviceService, mingliRuleRepository, mingliRuleConditionRepository)
	mingliRuleConditionHandler := cms.NewMingliRuleConditionHandler(baseHandler, mingliRuleConditionService)
	userPaipanRecordRepository := repository.NewUserPaipanRecordRepository(repositoryRepository)
	paipanRecordService := service.NewPaipanRecordService(serviceService, userPaipanRecordRepository)
	paipanRecordHandler := cms.NewPaipanRecordHandler(baseHandler, paipanRecordService)
	enumsService := service.NewEnumsService(serviceService, enumsRepository)
	enumsHandler := cms.NewEnumsHandler(baseHandler, enumsService)
	termRepository := repository.NewTermRepository(repositoryRepository)
	termService := service.NewTermService(serviceService, termRepository)
	termHandler := cms.NewTermHandler(baseHandler, termService)
	work := wecom.NewDoraemon(viperViper, logger)
	qwRepository := repository.NewQwRepository(repositoryRepository)
	wecomService := service.NewWecomService(serviceService, work, qwRepository)
	wecomHandler := cms.NewWecomHandler(baseHandler, wecomService)
	officialAccount := offiaccount.NewOfficialAccount(viperViper, logger)
	offiaccountRepository := repository.NewOffiaccountRepository(repositoryRepository)
	offiaccountService := service.NewOffiaccountService(serviceService, officialAccount, offiaccountRepository)
	offiaccountHandler := cms.NewOffiaccountHandler(baseHandler, offiaccountService)
	userMingliRepository := repository.NewUserMingliRepository(repositoryRepository)
	dateRepository := repository.NewDateRepository(repositoryRepository)
	userMingliGroupRepository := repository.NewUserMingliGroupRepository(repositoryRepository)
	userMingliService := service.NewUserMingliService(serviceService, userMingliRepository, dateRepository, userPaipanRecordRepository, userMingliGroupRepository)
	userMingliHandler := cms.NewUserMingliHandler(baseHandler, userMingliService)
	userOrderRepository := repository.NewUserOrderRepository(repositoryRepository)
	userOrderService := service.NewUserOrderService(serviceService, userOrderRepository)
	userOrderHandler := cms.NewUserOrderHandler(baseHandler, userOrderService)
	manager := miniprogram.NewManager(viperViper, logger)
	minProgramRepository := repository.NewMinProgramRepository(repositoryRepository)
	appRepository := repository.NewAppRepository(repositoryRepository)
	appChannelRepository := repository.NewAppChannelRepository(repositoryRepository)
	miniProgramService := service.NewMiniProgramService(serviceService, manager, minProgramRepository, appRepository, appChannelRepository)
	miniProgramHandler := cms.NewMiniProgramHandler(baseHandler, miniProgramService)
	appChannelService := service.NewAppChannelService(serviceService, appRepository, appChannelRepository, minProgramRepository)
	appChannelHandler := cms.NewAppChannelHandler(baseHandler, appChannelService)
	appVersionRepository := repository.NewAppVersionRepository(repositoryRepository)
	appVersionService := service.NewAppVersionService(serviceService, appRepository, appVersionRepository)
	appVersionHandler := cms.NewAppVersionHandler(baseHandler, appVersionService)
	portalRepository := repository.NewPortalRepository(repositoryRepository)
	portalService := service.NewPortalService(serviceService, portalRepository)
	portalHandler := cms.NewPortalHandler(baseHandler, portalService)
	baziAnalysisLinkRepository := repository.NewBaziAnalysisLinkRepository(repositoryRepository)
	baziAnalysisLinkService := service.NewBaziAnalysisLinkService(serviceService, work, qwRepository, baziAnalysisLinkRepository)
	baziAnalysisLinkHandler := cms.NewBaziAnalysisLinkHandler(baseHandler, baziAnalysisLinkService)
	subMailRepository := repository.NewSubMailRepository(repositoryRepository)
	submailClient := submail.NewClient(viperViper)
	subMailService := service.NewSubMailService(serviceService, subMailRepository, submailClient)
	subMailHandler := cms.NewSubMailHandler(baseHandler, subMailService)
	feedbackRepository := repository.NewFeedbackRepository(repositoryRepository)
	feedbackService := service.NewFeedbackService(serviceService, feedbackRepository)
	feedbackHandler := cms.NewFeedbackHandler(baseHandler, feedbackService)
	casdoorClient := casdoor.NewClient(viperViper)
	httpServer := server.NewAtlasServer(logger, viperViper, appUserHandler, mingliRuleHandler, mingliRuleConditionHandler, paipanRecordHandler, enumsHandler, termHandler, wecomHandler, offiaccountHandler, userMingliHandler, userOrderHandler, miniProgramHandler, appChannelHandler, appVersionHandler, portalHandler, baziAnalysisLinkHandler, subMailHandler, feedbackHandler, casdoorClient, jwthubJwthub)
	job := server.NewJob(logger)
	task := server.NewTask(logger)
	appApp := newApp(httpServer, job, task)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRedis, repository.NewRepository, repository.NewTransaction, repository.NewMingliRuleRepository, repository.NewEnumsRepository, repository.NewMingliRuleConditionRepository, repository.NewUserPaipanRecordRepository, repository.NewTermRepository, repository.NewQwRepository, repository.NewAtlasProductRepository, repository.NewUserMingliRepository, repository.NewUserMingliGroupRepository, repository.NewDateRepository, repository.NewUserOrderRepository, repository.NewAppUserRepository, repository.NewOffiaccountRepository, repository.NewAppRepository, repository.NewMinProgramRepository, repository.NewAppChannelRepository, repository.NewAppVersionRepository, repository.NewPortalRepository, repository.NewBaziAnalysisLinkRepository, repository.NewSubMailRepository, repository.NewFeedbackRepository, repository.NewAdminUserRepository)

var serviceSet = wire.NewSet(service.NewService, service.NewMingliRuleService, service.NewEnumsService, service.NewMingliRuleConditionService, service.NewPaipanRecordService, service.NewTermService, service.NewWecomService, service.NewOffiaccountService, service.NewAtlasProductService, service.NewUserMingliService, service.NewUserOrderService, service.NewAppUserService, service.NewMiniProgramService, service.NewAppChannelService, service.NewAppVersionService, service.NewPortalService, service.NewBaziAnalysisLinkService, service.NewSubMailService, service.NewFeedbackService)

var handlerSet = wire.NewSet(handler.NewHandler, cms.NewMingliRuleHandler, cms.NewEnumsHandler, cms.NewMingliRuleConditionHandler, cms.NewPaipanRecordHandler, cms.NewTermHandler, cms.NewWecomHandler, cms.NewAtlasProductHandler, cms.NewUserMingliHandler, cms.NewUserOrderHandler, cms.NewAppUserHandler, cms.NewOffiaccountHandler, cms.NewMiniProgramHandler, cms.NewAppChannelHandler, cms.NewAppVersionHandler, cms.NewPortalHandler, cms.NewBaziAnalysisLinkHandler, cms.NewSubMailHandler, cms.NewFeedbackHandler)

var serverSet = wire.NewSet(server.NewAtlasServer, server.NewJob)

// build App
func newApp(
	httpServer *http.Server,
	job *server.Job,
	task *server.Task,
) *app.App {
	return app.NewApp(app.WithServer(httpServer, job, task), app.WithName("cms-server"))
}
