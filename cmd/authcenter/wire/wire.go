//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/spf13/viper"
	"zodiacus/internal/handler"
	"zodiacus/internal/handler/authcenter"
	"zodiacus/internal/repository"
	"zodiacus/internal/server"
	"zodiacus/internal/service"
	"zodiacus/pkg/app"
	"zodiacus/pkg/geoip"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	aliyun_oss "zodiacus/pkg/oss/aliyun"
	"zodiacus/pkg/server/http"
	"zodiacus/pkg/sid"
	aliyun_sms "zodiacus/third_party/aliyun/sms"
	"zodiacus/third_party/casdoor"
	"zodiacus/third_party/corona"
	"zodiacus/third_party/submail"
	"zodiacus/third_party/uniapp"
	wechat_app "zodiacus/third_party/wechat"
	"zodiacus/third_party/wecom"
)

var repositorySet = wire.NewSet(
	repository.NewDB,
	repository.NewRedis,
	repository.NewRepository,
	repository.NewTransaction,
	repository.NewAppUserRepository,
	repository.NewAuthRepository,
	repository.NewUserOrderRepository,
	repository.NewAdminUserRepository,
)

var serviceSet = wire.NewSet(
	service.NewService,
	service.NewAppUserService,
	service.NewAuthService,
)

var handlerSet = wire.NewSet(
	handler.NewHandler,
	authcenter.NewAuthHandler,
)

var serverSet = wire.NewSet(
	server.NewAuthCenterServer,
	server.NewJob,
)

// build App
func newApp(
	httpServer *http.Server,
	job *server.Job,
	task *server.Task,
) *app.App {
	return app.NewApp(
		app.WithServer(httpServer, job, task),
		app.WithName("authcenter-server"),
	)
}

func NewWire(*viper.Viper, *log.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		repositorySet,
		serviceSet,
		handlerSet,
		serverSet,
		sid.NewSid,
		server.NewTask,
		newApp,
		casdoor.NewClient,
		wecom.NewDoraemon,
		wechat_app.NewOfficialAccount,
		wechat_app.NewMiniProgram,
		aliyun_sms.NewClient,
		corona.NewClient,
		geoip.NewClient,
		submail.NewClient,
		aliyun_oss.NewClient,
		jwthub.NewJwthub,
		uniapp.NewClient,
	))
}
