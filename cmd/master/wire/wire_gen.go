// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/spf13/viper"
	"zodiacus/internal/handler"
	"zodiacus/internal/handler/master"
	"zodiacus/internal/repository"
	"zodiacus/internal/server"
	"zodiacus/internal/service"
	"zodiacus/pkg/app"
	"zodiacus/pkg/geoip"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/pkg/oss/aliyun"
	"zodiacus/pkg/server/http"
	"zodiacus/pkg/sid"
	"zodiacus/third_party/aliyun/sms"
	"zodiacus/third_party/casdoor"
	"zodiacus/third_party/corona"
	"zodiacus/third_party/wecom"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	client := casdoor.NewClient(viperViper)
	baseHandler := handler.NewHandler(logger)
	db := repository.NewDB(viperViper, logger)
	redisClient := repository.NewRedis(viperViper)
	repositoryRepository := repository.NewRepository(logger, db, redisClient)
	transaction := repository.NewTransaction(repositoryRepository)
	sidSid := sid.NewSid()
	aliyun_smsClient := aliyun_sms.NewClient(viperViper, logger)
	coronaClient := corona.NewClient(viperViper, aliyun_smsClient, redisClient, logger)
	geoipClient := geoip.NewClient(viperViper)
	aliyun_ossClient := aliyun_oss.NewClient(viperViper)
	jwthubJwthub := jwthub.NewJwthub(viperViper, redisClient)
	serviceService := service.NewService(transaction, logger, sidSid, coronaClient, geoipClient, aliyun_ossClient, jwthubJwthub)
	datetimeService := service.NewDatetimeService(serviceService)
	dateTimeHandler := master.NewDateTimeHandler(baseHandler, datetimeService)
	masterRepository := repository.NewMasterRepository(repositoryRepository)
	userPaipanRecordRepository := repository.NewUserPaipanRecordRepository(repositoryRepository)
	userHepanRecordRepository := repository.NewUserHepanRecordRepository(repositoryRepository)
	luncaiRepository := repository.NewLuncaiRepository(repositoryRepository)
	userMingliRepository := repository.NewUserMingliRepository(repositoryRepository)
	mingliRuleRepository := repository.NewMingliRuleRepository(repositoryRepository)
	mingliRuleConditionRepository := repository.NewMingliRuleConditionRepository(repositoryRepository)
	enumsRepository := repository.NewEnumsRepository(repositoryRepository)
	dateRepository := repository.NewDateRepository(repositoryRepository)
	masterService := service.NewMasterService(serviceService, masterRepository, userPaipanRecordRepository, userHepanRecordRepository, luncaiRepository, userMingliRepository, mingliRuleRepository, mingliRuleConditionRepository, enumsRepository, dateRepository)
	masterHandler := master.NewMasterHandler(baseHandler, masterService)
	locationService := service.NewLocationService(serviceService, enumsRepository)
	locationHandler := master.NewLocationHandler(baseHandler, locationService)
	enumsService := service.NewEnumsService(serviceService, enumsRepository)
	enumsHandler := master.NewEnumsHandler(baseHandler, enumsService)
	userMingliGroupRepository := repository.NewUserMingliGroupRepository(repositoryRepository)
	dateService := service.NewDateService(serviceService, dateRepository, enumsRepository, userMingliRepository, userMingliGroupRepository)
	dateHandler := master.NewDateHandler(baseHandler, dateService)
	appRepository := repository.NewAppRepository(repositoryRepository)
	appVersionRepository := repository.NewAppVersionRepository(repositoryRepository)
	appVersionService := service.NewAppVersionService(serviceService, appRepository, appVersionRepository)
	appVersionHandler := master.NewAppVersionHandler(baseHandler, appVersionService)
	paipanRecordService := service.NewPaipanRecordService(serviceService, userPaipanRecordRepository)
	paipanRecordHandler := master.NewPaipanRecordHandler(baseHandler, paipanRecordService)
	appUserInvitationRepository := repository.NewAppUserInvitationRepository(repositoryRepository)
	appUserRepository := repository.NewAppUserRepository(repositoryRepository)
	appUserInvitationService := service.NewAppUserInvitationService(serviceService, appUserInvitationRepository, appUserRepository)
	appUserInvitationHandler := master.NewAppUserInvitationHandler(baseHandler, appUserInvitationService)
	qwRepository := repository.NewQwRepository(repositoryRepository)
	appUserKefuRepository := repository.NewAppUserKefuRepository(repositoryRepository)
	work := wecom.NewDoraemon(viperViper, logger)
	kefuService := service.NewAppUserKefuService(serviceService, qwRepository, appUserKefuRepository, work)
	kefuHandler := master.NewKefuHandler(baseHandler, kefuService)
	httpServer := server.NewMasterHTTPServer(logger, viperViper, client, dateTimeHandler, masterHandler, locationHandler, enumsHandler, dateHandler, appVersionHandler, paipanRecordHandler, appUserInvitationHandler, kefuHandler, jwthubJwthub)
	job := server.NewJob(logger)
	appApp := newApp(httpServer, job)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRedis, repository.NewRepository, repository.NewTransaction, repository.NewEnumsRepository, repository.NewMasterRepository, repository.NewUserPaipanRecordRepository, repository.NewDateRepository, repository.NewUserMingliRepository, repository.NewUserMingliGroupRepository, repository.NewAppVersionRepository, repository.NewAppRepository, repository.NewLuncaiRepository, repository.NewMingliRuleRepository, repository.NewMingliRuleConditionRepository, repository.NewUserHepanRecordRepository, repository.NewAppUserInvitationRepository, repository.NewAppUserRepository, repository.NewAppUserKefuRepository, repository.NewQwRepository)

var serviceSet = wire.NewSet(service.NewService, service.NewLocationService, service.NewEnumsService, service.NewDatetimeService, service.NewMasterService, service.NewDateService, service.NewAppVersionService, service.NewPaipanRecordService, service.NewAppUserInvitationService, service.NewAppUserKefuService)

var handlerSet = wire.NewSet(handler.NewHandler, master.NewDateTimeHandler, master.NewLocationHandler, master.NewEnumsHandler, master.NewMasterHandler, master.NewDateHandler, master.NewAppVersionHandler, master.NewPaipanRecordHandler, master.NewAppUserInvitationHandler, master.NewKefuHandler)

var serverSet = wire.NewSet(server.NewMasterHTTPServer, server.NewJob)

// build App
func newApp(
	httpServer *http.Server,
	job *server.Job,

) *app.App {
	return app.NewApp(app.WithServer(httpServer, job), app.WithName("master-app-api"))
}
