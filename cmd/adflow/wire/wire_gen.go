// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/spf13/viper"
	"zodiacus/internal/handler"
	"zodiacus/internal/handler/adflow"
	"zodiacus/internal/repository"
	"zodiacus/internal/server"
	"zodiacus/internal/service"
	"zodiacus/pkg/app"
	"zodiacus/pkg/geoip"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/pkg/oss/aliyun"
	"zodiacus/pkg/server/http"
	"zodiacus/pkg/sid"
	"zodiacus/third_party/aliyun/sms"
	"zodiacus/third_party/casdoor"
	"zodiacus/third_party/corona"
	"zodiacus/third_party/submail"
	"zodiacus/third_party/wecom"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	client := casdoor.NewClient(viperViper)
	baseHandler := handler.NewHandler(logger)
	db := repository.NewDB(viperViper, logger)
	redisClient := repository.NewRedis(viperViper)
	repositoryRepository := repository.NewRepository(logger, db, redisClient)
	transaction := repository.NewTransaction(repositoryRepository)
	sidSid := sid.NewSid()
	aliyun_smsClient := aliyun_sms.NewClient(viperViper, logger)
	coronaClient := corona.NewClient(viperViper, aliyun_smsClient, redisClient, logger)
	geoipClient := geoip.NewClient(viperViper)
	aliyun_ossClient := aliyun_oss.NewClient(viperViper)
	jwthubJwthub := jwthub.NewJwthub(viperViper, redisClient)
	serviceService := service.NewService(transaction, logger, sidSid, coronaClient, geoipClient, aliyun_ossClient, jwthubJwthub)
	datetimeService := service.NewDatetimeService(serviceService)
	dateTimeHandler := adflow.NewDateTimeHandler(baseHandler, datetimeService)
	enumsRepository := repository.NewEnumsRepository(repositoryRepository)
	locationService := service.NewLocationService(serviceService, enumsRepository)
	locationHandler := adflow.NewLocationHandler(baseHandler, locationService)
	enumsService := service.NewEnumsService(serviceService, enumsRepository)
	enumsHandler := adflow.NewEnumsHandler(baseHandler, enumsService)
	userPaipanRecordRepository := repository.NewUserPaipanRecordRepository(repositoryRepository)
	paipanRecordService := service.NewPaipanRecordService(serviceService, userPaipanRecordRepository)
	paipanRecordHandler := adflow.NewPaipanRecordHandler(baseHandler, paipanRecordService)
	vipRepository := repository.NewVIPRepository(repositoryRepository)
	dateRepository := repository.NewDateRepository(repositoryRepository)
	userMingliRepository := repository.NewUserMingliRepository(repositoryRepository)
	userMingliGroupRepository := repository.NewUserMingliGroupRepository(repositoryRepository)
	dateService := service.NewDateService(serviceService, dateRepository, enumsRepository, userMingliRepository, userMingliGroupRepository)
	dateHandler := adflow.NewDateHandler(baseHandler, dateService)
	appRepository := repository.NewAppRepository(repositoryRepository)
	appVersionRepository := repository.NewAppVersionRepository(repositoryRepository)
	appVersionService := service.NewAppVersionService(serviceService, appRepository, appVersionRepository)
	appVersionHandler := adflow.NewAppVersionHandler(baseHandler, appVersionService)
	luncaiRepository := repository.NewLuncaiRepository(repositoryRepository)
	userOrderRepository := repository.NewUserOrderRepository(repositoryRepository)
	yunshiRepository := repository.NewYunshiRepository(repositoryRepository)
	work := wecom.NewDoraemon(viperViper, logger)
	qwRepository := repository.NewQwRepository(repositoryRepository)
	adflowRepository := repository.NewAdflowRepository(repositoryRepository)
	subMailRepository := repository.NewSubMailRepository(repositoryRepository)
	userEventRepository := repository.NewUserEventRepository(repositoryRepository)
	submailClient := submail.NewClient(viperViper)
	adflowService := service.NewAdflowService(serviceService, luncaiRepository, userPaipanRecordRepository, userOrderRepository, yunshiRepository, work, qwRepository, adflowRepository, dateRepository, subMailRepository, userEventRepository, submailClient)
	adflowHandler := adflow.NewAdflowHandler(baseHandler, adflowService)
	httpServer := server.NewAdFlowHTTPServer(logger, viperViper, client, dateTimeHandler, locationHandler, enumsHandler, paipanRecordHandler, vipRepository, dateHandler, appVersionHandler, adflowHandler, jwthubJwthub)
	job := server.NewJob(logger)
	appApp := newApp(httpServer, job)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRedis, repository.NewRepository, repository.NewTransaction, repository.NewLuncaiRepository, repository.NewYunshiRepository, repository.NewEnumsRepository, repository.NewUserPaipanRecordRepository, repository.NewVIPRepository, repository.NewUserOrderRepository, repository.NewDateRepository, repository.NewUserMingliRepository, repository.NewUserMingliGroupRepository, repository.NewAppVersionRepository, repository.NewAppRepository, repository.NewAdflowRepository, repository.NewQwRepository, repository.NewSubMailRepository, repository.NewUserEventRepository)

var serviceSet = wire.NewSet(service.NewService, service.NewLuncaiService, service.NewLocationService, service.NewEnumsService, service.NewDatetimeService, service.NewPaipanRecordService, service.NewDateService, service.NewAppVersionService, service.NewAdflowService)

var handlerSet = wire.NewSet(handler.NewHandler, adflow.NewDateTimeHandler, adflow.NewLocationHandler, adflow.NewEnumsHandler, adflow.NewPaipanRecordHandler, adflow.NewDateHandler, adflow.NewAppVersionHandler, adflow.NewAdflowHandler)

var serverSet = wire.NewSet(server.NewAdFlowHTTPServer, server.NewJob)

// build App
func newApp(
	httpServer *http.Server,
	job *server.Job,

) *app.App {
	return app.NewApp(app.WithServer(httpServer, job), app.WithName("adflow-app-api"))
}
