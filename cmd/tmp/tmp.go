package main

import (
	"fmt"
	"math/rand"
	"sort"
	"strings"
	"time"
	"unicode"

	"github.com/Lofanmi/pinyin-golang/pinyin"
)

type Item struct {
	NameA     string `json:"nameA"`     // 拼音首字母
	Name      string `json:"name"`      // 可由特殊符号、字母、数字或者中文或日语等其他语言组成。开头的第一个字符作为nameA（当第一个字母为中文时使用拼音首字母的大写）
	FirstChar string `json:"firstChar"` // 第一个字符（用于中文分组）
	CreatedAt time.Time
}

// calculateNameA 计算NameA字段（拼音首字母）
func calculateNameA(name string) string {
	if name == "" {
		return ""
	}

	// 获取第一个字符
	firstChar := []rune(name)[0]

	// 如果是英文字母，直接转大写
	if unicode.IsLetter(firstChar) && firstChar <= 127 {
		return strings.ToUpper(string(firstChar))
	}

	// 如果是数字
	if unicode.IsDigit(firstChar) {
		return string(firstChar)
	}

	// 如果是中文字符，使用pinyin-golang库的人名模式获取首字母
	if unicode.Is(unicode.Han, firstChar) {
		// 使用人名模式转换拼音
		dict := pinyin.NewDict()
		pinyinResult := dict.Name(string(firstChar), " ").ASCII()
		if pinyinResult != "" {
			// 获取第一个拼音的首字母
			words := strings.Fields(pinyinResult)
			if len(words) > 0 && len(words[0]) > 0 {
				return strings.ToUpper(string(words[0][0]))
			}
		}
	}

	// 如果是其他字符（特殊符号等），返回原字符
	return string(firstChar)
}

// getFirstChar 获取第一个字符
func getFirstChar(name string) string {
	if name == "" {
		return ""
	}
	return string([]rune(name)[0])
}

// 姓名数据库
var (
	// 中文姓氏
	chineseSurnames = []string{
		"张", "王", "李", "赵", "钱", "孙", "周", "吴", "郑", "冯", "陈", "褚", "卫", "蒋", "沈", "韩", "杨", "朱", "秦", "尤",
		"许", "何", "吕", "施", "张", "孔", "曹", "严", "华", "金", "魏", "陶", "姜", "戚", "谢", "邹", "喻", "柏", "水", "窦",
		"章", "云", "苏", "潘", "葛", "奚", "范", "彭", "郎", "鲁", "韦", "昌", "马", "苗", "凤", "花", "方", "俞", "任", "袁",
		"柳", "酆", "鲍", "史", "唐", "费", "廉", "岑", "薛", "雷", "贺", "倪", "汤", "滕", "殷", "罗", "毕", "郝", "邬", "安",
		"常", "乐", "于", "时", "傅", "皮", "卞", "齐", "康", "伍", "余", "元", "卜", "顾", "孟", "平", "黄", "和", "穆", "萧",
		"尹", "姚", "邵", "湛", "汪", "祁", "毛", "禹", "狄", "米", "贝", "明", "臧", "计", "伏", "成", "戴", "谈", "宋", "茅",
		"庞", "熊", "纪", "舒", "屈", "项", "祝", "董", "梁", "杜", "阮", "蓝", "闵", "席", "季", "麻", "强", "贾", "路", "娄",
		"危", "江", "童", "颜", "郭", "梅", "盛", "林", "刁", "钟", "徐", "邱", "骆", "高", "夏", "蔡", "田", "樊", "胡", "凌",
		"霍", "虞", "万", "支", "柯", "昝", "管", "卢", "莫", "经", "房", "裘", "缪", "干", "解", "应", "宗", "丁", "宣", "贲",
		"邓", "郁", "单", "杭", "洪", "包", "诸", "左", "石", "崔", "吉", "钮", "龚", "程", "嵇", "邢", "滑", "裴", "陆", "荣",
	}

	// 中文名字
	chineseNames = []string{
		"伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰", "霞",
		"平", "刚", "桂英", "建华", "文", "华", "红", "玲", "志强", "秀珍", "春梅", "海燕", "雪", "荣", "爱华", "建国", "如意", "宝贝",
		"小明", "小红", "小李", "小王", "小张", "小刘", "小陈", "小杨", "小赵", "小黄", "小周", "小吴", "小徐", "小孙", "小马", "小朱",
		"建军", "建设", "建平", "建华", "建国", "建民", "建新", "建中", "建东", "建南", "建西", "建北", "建上", "建下", "建左", "建右",
		"志华", "志明", "志强", "志刚", "志勇", "志军", "志平", "志国", "志民", "志新", "志中", "志东", "志南", "志西", "志北", "志上",
		"春花", "春草", "春燕", "春雨", "春风", "春雷", "春雪", "春阳", "春光", "春色", "春意", "春天", "春日", "春晓", "春宵", "春梦",
		"秋月", "秋水", "秋风", "秋雨", "秋叶", "秋色", "秋光", "秋意", "秋天", "秋日", "秋晓", "秋宵", "秋梦", "秋思", "秋怀", "秋韵",
		"冬梅", "冬雪", "冬阳", "冬日", "冬天", "冬季", "冬至", "冬青", "冬竹", "冬松", "冬柏", "冬桂", "冬兰", "冬菊", "冬荷", "冬莲",
		"夏雨", "夏风", "夏雷", "夏雪", "夏阳", "夏光", "夏色", "夏意", "夏天", "夏日", "夏晓", "夏宵", "夏梦", "夏思", "夏怀", "夏韵",
	}

	// 英文姓氏
	englishSurnames = []string{
		"Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez",
		"Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas", "Taylor", "Moore", "Jackson", "Martin",
		"Lee", "Perez", "Thompson", "White", "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson",
		"Walker", "Young", "Allen", "King", "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores",
		"Green", "Adams", "Nelson", "Baker", "Hall", "Rivera", "Campbell", "Mitchell", "Carter", "Roberts",
	}

	// 英文名字
	englishNames = []string{
		"James", "Robert", "John", "Michael", "David", "William", "Richard", "Joseph", "Thomas", "Christopher",
		"Charles", "Daniel", "Matthew", "Anthony", "Mark", "Donald", "Steven", "Paul", "Andrew", "Joshua",
		"Kenneth", "Kevin", "Brian", "George", "Timothy", "Ronald", "Jason", "Edward", "Jeffrey", "Ryan",
		"Jacob", "Gary", "Nicholas", "Eric", "Jonathan", "Stephen", "Larry", "Justin", "Scott", "Brandon",
		"Mary", "Patricia", "Jennifer", "Linda", "Elizabeth", "Barbara", "Susan", "Jessica", "Sarah", "Karen",
		"Lisa", "Nancy", "Betty", "Helen", "Sandra", "Donna", "Carol", "Ruth", "Sharon", "Michelle",
		"Laura", "Sarah", "Kimberly", "Deborah", "Dorothy", "Lisa", "Nancy", "Karen", "Betty", "Helen",
		"Sandra", "Donna", "Carol", "Ruth", "Sharon", "Michelle", "Laura", "Sarah", "Kimberly", "Deborah",
	}

	// 日文姓氏
	japaneseSurnames = []string{
		"佐藤", "鈴木", "高橋", "田中", "渡辺", "伊藤", "山本", "中村", "小林", "加藤",
		"吉田", "山田", "佐々木", "山口", "松本", "井上", "木村", "林", "斎藤", "清水",
		"山崎", "森", "池田", "橋本", "阿部", "石川", "石井", "藤井", "小川", "後藤",
		"岡田", "長谷川", "村上", "近藤", "石田", "前田", "藤田", "丸山", "上田", "原田",
	}

	// 日文名字
	japaneseNames = []string{
		"太郎", "花子", "一郎", "二郎", "三郎", "四郎", "五郎", "六郎", "七郎", "八郎",
		"美咲", "愛", "結愛", "陽菜", "さくら", "ひなた", "美羽", "結衣", "心春", "美桜",
		"翔太", "大翔", "悠真", "悠人", "陽翔", "蓮", "大和", "湊", "樹", "大樹",
		"美月", "美優", "美穂", "美紀", "美香", "美智子", "美代子", "美奈子", "美恵子", "美和子",
		"健太", "健一", "健二", "健三", "健四", "健五", "健六", "健七", "健八", "健九",
		"智子", "恵子", "裕子", "由美子", "真由美", "久美子", "美智子", "美代子", "美奈子", "美恵子",
	}

	// 特殊符号开头的名字
	specialNames = []string{
		"@admin", "@user", "@test", "@demo", "@guest", "@root", "@system", "@public", "@private", "@secure",
		"#hashtag", "#tag", "#label", "#mark", "#sign", "#symbol", "#code", "#data", "#info", "#news",
		"$money", "$cash", "$coin", "$gold", "$silver", "$dollar", "$euro", "$yen", "$pound", "$franc",
		"&and", "&plus", "&more", "&extra", "&bonus", "&special", "&unique", "&custom", "&personal", "&private",
		"*star", "*shine", "*bright", "*light", "*glow", "*spark", "*flash", "*beam", "*ray", "*sun",
		"!important", "!urgent", "!critical", "!warning", "!alert", "!notice", "!info", "!debug", "!error", "!success",
		"?question", "?ask", "?help", "?support", "?guide", "?manual", "?doc", "?info", "?data", "?test",
		"+plus", "+add", "+new", "+create", "+build", "+make", "+do", "+work", "+play", "+fun",
		"-minus", "-sub", "-remove", "-delete", "-clear", "-clean", "-empty", "-void", "-null", "-zero",
		"=equal", "=same", "=match", "=compare", "=check", "=verify", "=confirm", "=validate", "=test", "=demo",
		"~wave", "~flow", "~stream", "~river", "~sea", "~ocean", "~water", "~blue", "~sky", "~cloud",
		"`code", "`script", "`program", "`app", "`software", "`system", "`tool", "`util", "`helper", "`lib",
		"|pipe", "|line", "|bar", "|stick", "|rod", "|pole", "|column", "|pillar", "|tower", "|wall",
		"\\back", "\\slash", "\\path", "\\dir", "\\folder", "\\file", "\\doc", "\\text", "\\data", "\\info",
		"/forward", "/slash", "/path", "/url", "/link", "/site", "/page", "/web", "/net", "/online",
		"<less", "<small", "<mini", "<tiny", "<micro", "<nano", "<pico", "<femto", "<atto", "<zepto",
		">more", ">big", ">large", ">huge", ">giant", ">mega", ">giga", ">tera", ">peta", ">exa",
		"[bracket", "[square", "[box", "[container", "[holder", "[wrapper", "[frame", "[border", "[edge", "[side",
		"]close", "]end", "]finish", "]complete", "]done", "]ready", "]final", "]last", "]stop", "]exit",
		"{brace", "{curly", "{open", "{start", "{begin", "{init", "{create", "{new", "{fresh", "{clean",
		"}close", "}end", "}finish", "}complete", "}done", "}ready", "}final", "}last", "}stop", "}exit",
		"(paren", "(round", "(circle", "(open", "(start", "(begin", "(init", "(create", "(new", "(fresh",
		")close", ")end", ")finish", ")complete", ")done", ")ready", ")final", ")last", ")stop", ")exit",
		"\"quote", "\"string", "\"text", "\"word", "\"name", "\"title", "\"label", "\"tag", "\"mark", "\"sign",
		"'single", "'quote", "'char", "'letter", "'symbol", "'mark", "'sign", "'dot", "'point", "'spot",
		":colon", ":time", ":clock", ":hour", ":minute", ":second", ":moment", ":instant", ":now", ":today",
		";semi", ";half", ";part", ";piece", ";fragment", ";section", ";segment", ";portion", ";share", ";bit",
		",comma", ",pause", ",break", ",stop", ",rest", ",wait", ",delay", ",slow", ",calm", ",quiet",
		".dot", ".point", ".spot", ".mark", ".sign", ".symbol", ".char", ".letter", ".end", ".finish",
	}

	// 数字开头的名字
	numberNames = []string{
		"123test", "456demo", "789user", "001admin", "999guest", "888lucky", "666devil", "777angel", "555phone", "000null",
		"1st", "2nd", "3rd", "4th", "5th", "6th", "7th", "8th", "9th", "10th",
		"11th", "12th", "13th", "14th", "15th", "16th", "17th", "18th", "19th", "20th",
		"100percent", "200ok", "300redirect", "400error", "500server", "600custom", "700special", "800unique", "900test", "1000complete",
		"2023year", "2024next", "2025future", "2026coming", "2027soon", "2028later", "2029far", "2030decade", "2031new", "2032fresh",
		"1monday", "2tuesday", "3wednesday", "4thursday", "5friday", "6saturday", "7sunday", "8weekend", "9holiday", "0everyday",
	}
)

// generateRandomItems 生成指定长度的随机Item切片
func generateRandomItems(count int) []Item {
	rand.Seed(time.Now().UnixNano())
	items := make([]Item, count)

	// 基准时间：2020年1月1日到2024年12月31日
	startTime := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	endTime := time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC)
	timeRange := endTime.Sub(startTime)

	for i := 0; i < count; i++ {
		// 随机选择姓名类型
		nameType := rand.Intn(6) // 0-5: 中文、英文、日文、特殊符号、数字、混合

		var name string
		switch nameType {
		case 0: // 中文姓名
			surname := chineseSurnames[rand.Intn(len(chineseSurnames))]
			firstName := chineseNames[rand.Intn(len(chineseNames))]
			name = surname + firstName
		case 1: // 英文姓名
			surname := englishSurnames[rand.Intn(len(englishSurnames))]
			firstName := englishNames[rand.Intn(len(englishNames))]
			name = firstName + " " + surname
		case 2: // 日文姓名
			surname := japaneseSurnames[rand.Intn(len(japaneseSurnames))]
			firstName := japaneseNames[rand.Intn(len(japaneseNames))]
			name = surname + firstName
		case 3: // 特殊符号开头
			name = specialNames[rand.Intn(len(specialNames))]
		case 4: // 数字开头
			name = numberNames[rand.Intn(len(numberNames))]
		case 5: // 混合类型
			// 随机组合不同类型
			switch rand.Intn(4) {
			case 0: // 中文+英文
				chineseName := chineseSurnames[rand.Intn(len(chineseSurnames))] + chineseNames[rand.Intn(len(chineseNames))]
				englishName := englishNames[rand.Intn(len(englishNames))]
				name = chineseName + "(" + englishName + ")"
			case 1: // 特殊符号+中文
				special := specialNames[rand.Intn(len(specialNames))]
				chinese := chineseSurnames[rand.Intn(len(chineseSurnames))] + chineseNames[rand.Intn(len(chineseNames))]
				name = special + chinese
			case 2: // 数字+日文
				number := numberNames[rand.Intn(len(numberNames))]
				japanese := japaneseSurnames[rand.Intn(len(japaneseSurnames))] + japaneseNames[rand.Intn(len(japaneseNames))]
				name = number + japanese
			case 3: // 完全随机组合
				parts := []string{
					chineseSurnames[rand.Intn(len(chineseSurnames))],
					englishNames[rand.Intn(len(englishNames))],
					specialNames[rand.Intn(len(specialNames))],
				}
				name = strings.Join(parts, "")
			}
		}

		// 生成随机时间
		randomDuration := time.Duration(rand.Int63n(int64(timeRange)))
		randomTime := startTime.Add(randomDuration)

		items[i] = Item{
			Name:      name,
			CreatedAt: randomTime,
		}
	}

	return items
}

func main() {
	fmt.Println("=== 生成随机数据测试 ===")
	items := generateRandomItems(1000) // 生成30个随机Item
	start := time.Now()
	for i := range items {
		items[i].NameA = calculateNameA(items[i].Name)
		items[i].FirstChar = getFirstChar(items[i].Name)
	}
	//fmt.Printf("生成了 %d 个随机Item，排序前:\n", len(items))
	//for _, item := range items {
	//	fmt.Printf("Name: %-25s, NameA: %-3s, FirstChar: %-3s, CreatedAt: %s\n",
	//		item.Name, item.NameA, item.FirstChar, item.CreatedAt.Format("2006-01-02 15:04:05"))
	//}
	sort.Slice(items, func(i, j int) bool {
		if items[i].NameA != items[j].NameA {
			return items[i].NameA < items[j].NameA
		}
		if items[i].FirstChar != items[j].FirstChar {
			return items[i].FirstChar < items[j].FirstChar
		}
		return items[i].CreatedAt.After(items[j].CreatedAt)
	})
	microseconds := time.Now().Sub(start).Milliseconds()
	fmt.Printf("排序耗时: %d毫秒\n", microseconds)

	//fmt.Printf("\n排序后:\n")
	//for _, item := range items {
	//	fmt.Printf("Name: %-25s, NameA: %-3s, FirstChar: %-3s, CreatedAt: %s\n",
	//		item.Name, item.NameA, item.FirstChar, item.CreatedAt.Format("2006-01-02 15:04:05"))
	//}
	/*
		fmt.Printf("\n=== 统计信息 ===\n")
		nameACount := make(map[string]int)
		typeCount := make(map[string]int)
		for _, item := range items {
			nameACount[item.NameA]++
			firstChar := []rune(item.Name)[0]
			if unicode.Is(unicode.Han, firstChar) {
				typeCount["中文"]++
			} else if unicode.IsLetter(firstChar) && firstChar <= 127 {
				typeCount["英文"]++
			} else if unicode.IsDigit(firstChar) {
				typeCount["数字"]++
			} else {
				typeCount["特殊符号"]++
			}
		}
		fmt.Printf("按首字母分布:\n")
		for nameA, count := range nameACount {
			fmt.Printf("  %s: %d个\n", nameA, count)
		}
		fmt.Printf("\n按类型分布:\n")
		for typ, count := range typeCount {
			fmt.Printf("  %s: %d个\n", typ, count)
		}
	*/
}
