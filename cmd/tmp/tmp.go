package main

import (
	"fmt"
	"sort"
	"strings"
	"time"
	"unicode"

	"github.com/Lofanmi/pinyin-golang/pinyin"
)

type Item struct {
	NameA     string `json:"nameA"`     // 拼音首字母
	Name      string `json:"name"`      // 可由特殊符号、字母、数字或者中文或日语等其他语言组成。开头的第一个字符作为nameA（当第一个字母为中文时使用拼音首字母的大写）
	FirstChar string `json:"firstChar"` // 第一个字符（用于中文分组）
	CreatedAt time.Time
}

// calculateNameA 计算NameA字段（拼音首字母）
func calculateNameA(name string) string {
	if name == "" {
		return ""
	}

	// 获取第一个字符
	firstChar := []rune(name)[0]

	// 如果是英文字母，直接转大写
	if unicode.IsLetter(firstChar) && firstChar <= 127 {
		return strings.ToUpper(string(firstChar))
	}

	// 如果是数字
	if unicode.IsDigit(firstChar) {
		return string(firstChar)
	}

	// 如果是中文字符，使用pinyin-golang库的人名模式获取首字母
	if unicode.Is(unicode.Han, firstChar) {
		// 使用人名模式转换拼音
		dict := pinyin.NewDict()
		pinyinResult := dict.Name(string(firstChar), " ").ASCII()
		if pinyinResult != "" {
			// 获取第一个拼音的首字母
			words := strings.Fields(pinyinResult)
			if len(words) > 0 && len(words[0]) > 0 {
				return strings.ToUpper(string(words[0][0]))
			}
		}
	}

	// 如果是其他字符（特殊符号等），返回原字符
	return string(firstChar)
}

// getFirstChar 获取第一个字符
func getFirstChar(name string) string {
	if name == "" {
		return ""
	}
	return string([]rune(name)[0])
}

func main() {
	// 创建Item的切片，增加更多测试数据
	items := []Item{
		{Name: "阿一", CreatedAt: time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC)},
		{Name: "房", CreatedAt: time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC)},
		{Name: "张三", CreatedAt: time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC)},
		{Name: "李四", CreatedAt: time.Date(2023, 1, 2, 10, 0, 0, 0, time.UTC)},
		{Name: "单珍珍", CreatedAt: time.Date(2023, 1, 2, 10, 0, 0, 0, time.UTC)},
		{Name: "王五", CreatedAt: time.Date(2023, 1, 3, 10, 0, 0, 0, time.UTC)},
		{Name: "Alice", CreatedAt: time.Date(2023, 1, 4, 10, 0, 0, 0, time.UTC)},
		{Name: "Bob", CreatedAt: time.Date(2023, 1, 5, 10, 0, 0, 0, time.UTC)},
		{Name: "Charlie", CreatedAt: time.Date(2023, 1, 6, 10, 0, 0, 0, time.UTC)},
		{Name: "123测试", CreatedAt: time.Date(2023, 1, 7, 10, 0, 0, 0, time.UTC)},
		{Name: "@特殊", CreatedAt: time.Date(2023, 1, 8, 10, 0, 0, 0, time.UTC)},
		{Name: "赵六", CreatedAt: time.Date(2023, 1, 9, 10, 0, 0, 0, time.UTC)},
		{Name: "Amy", CreatedAt: time.Date(2023, 1, 10, 10, 0, 0, 0, time.UTC)},
		{Name: "张七", CreatedAt: time.Date(2023, 1, 11, 10, 0, 0, 0, time.UTC)}, // 同样是Z开头，但时间更晚
		{Name: "周八", CreatedAt: time.Date(2023, 1, 12, 10, 0, 0, 0, time.UTC)}, // 也是Z开头，不同汉字
		{Name: "朱九", CreatedAt: time.Date(2023, 1, 13, 10, 0, 0, 0, time.UTC)}, // 也是Z开头，不同汉字
		{Name: "张十", CreatedAt: time.Date(2023, 1, 14, 10, 0, 0, 0, time.UTC)}, // 张姓，时间最晚
	}

	// 计算NameA和FirstChar
	for i := range items {
		items[i].NameA = calculateNameA(items[i].Name)
		items[i].FirstChar = getFirstChar(items[i].Name)
	}

	fmt.Println("排序前:")
	for _, item := range items {
		fmt.Printf("Name: %s, NameA: %s, FirstChar: %s, CreatedAt: %s\n",
			item.Name, item.NameA, item.FirstChar, item.CreatedAt.Format("2006-01-02 15:04:05"))
	}

	// 新的排序逻辑：
	// 1. 先按拼音首字母(NameA)排序
	// 2. 拼音首字母相同时，按第一个汉字(FirstChar)分组
	// 3. 第一个汉字相同时，按时间倒序排序
	sort.Slice(items, func(i, j int) bool {
		// 首先按NameA排序
		if items[i].NameA != items[j].NameA {
			return items[i].NameA < items[j].NameA
		}

		// NameA相同时，按FirstChar排序
		if items[i].FirstChar != items[j].FirstChar {
			return items[i].FirstChar < items[j].FirstChar
		}

		// NameA和FirstChar都相同时，按CreatedAt降序排序（时间晚的在前）
		return items[i].CreatedAt.After(items[j].CreatedAt)
	})

	fmt.Println("\n排序后:")
	for _, item := range items {
		fmt.Printf("Name: %s, NameA: %s, FirstChar: %s, CreatedAt: %s\n",
			item.Name, item.NameA, item.FirstChar, item.CreatedAt.Format("2006-01-02 15:04:05"))
	}
}
