package main

import (
	"fmt"
	"sort"
	"strings"
	"time"
	"unicode"

	"github.com/mozillazg/go-pinyin"
)

type Item struct {
	NameA     string `json:"nameA"` //
	Name      string `json:"name"`  // 可由特殊符号、字母、数字或者中文或日语等其他语言组成。开头的第一个字符作为nameA（当第一个字母为中文时使用拼音首字母的大写）
	CreatedAt time.Time
}

// calculateNameA 计算NameA字段
func calculateNameA(name string) string {
	if name == "" {
		return ""
	}

	// 获取第一个字符
	firstChar := []rune(name)[0]

	// 如果是英文字母，直接转大写
	if unicode.IsLetter(firstChar) && firstChar <= 127 {
		return strings.ToUpper(string(firstChar))
	}

	// 如果是数字
	if unicode.IsDigit(firstChar) {
		return string(firstChar)
	}

	// 如果是中文字符，使用go-pinyin库获取首字母
	if unicode.Is(unicode.Han, firstChar) {
		args := pinyin.NewArgs()
		args.Style = pinyin.FirstLetter
		result := pinyin.SinglePinyin(firstChar, args)
		if len(result) > 0 && result[0] != "" {
			return strings.ToUpper(result[0])
		}
	}

	// 如果是其他字符（特殊符号等），返回原字符
	return string(firstChar)
}

func main() {
	// 创建Item的切片
	items := []Item{
		{Name: "张三", CreatedAt: time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC)},
		{Name: "李四", CreatedAt: time.Date(2023, 1, 2, 10, 0, 0, 0, time.UTC)},
		{Name: "王五", CreatedAt: time.Date(2023, 1, 3, 10, 0, 0, 0, time.UTC)},
		{Name: "Alice", CreatedAt: time.Date(2023, 1, 4, 10, 0, 0, 0, time.UTC)},
		{Name: "Bob", CreatedAt: time.Date(2023, 1, 5, 10, 0, 0, 0, time.UTC)},
		{Name: "Charlie", CreatedAt: time.Date(2023, 1, 6, 10, 0, 0, 0, time.UTC)},
		{Name: "123测试", CreatedAt: time.Date(2023, 1, 7, 10, 0, 0, 0, time.UTC)},
		{Name: "@特殊", CreatedAt: time.Date(2023, 1, 8, 10, 0, 0, 0, time.UTC)},
		{Name: "赵六", CreatedAt: time.Date(2023, 1, 9, 10, 0, 0, 0, time.UTC)},
		{Name: "Amy", CreatedAt: time.Date(2023, 1, 10, 10, 0, 0, 0, time.UTC)},
		{Name: "张七", CreatedAt: time.Date(2023, 1, 11, 10, 0, 0, 0, time.UTC)}, // 同样是Z开头，但时间更晚
	}

	// 计算NameA
	for i := range items {
		items[i].NameA = calculateNameA(items[i].Name)
	}

	fmt.Println("排序前:")
	for _, item := range items {
		fmt.Printf("Name: %s, NameA: %s, CreatedAt: %s\n",
			item.Name, item.NameA, item.CreatedAt.Format("2006-01-02 15:04:05"))
	}

	// 根据NameA排序，NameA相同的根据CreatedAt降序排序
	sort.Slice(items, func(i, j int) bool {
		if items[i].NameA == items[j].NameA {
			// NameA相同时，按CreatedAt降序排序（时间晚的在前）
			return items[i].CreatedAt.After(items[j].CreatedAt)
		}
		// 按NameA升序排序
		return items[i].NameA < items[j].NameA
	})

	fmt.Println("\n排序后:")
	for _, item := range items {
		fmt.Printf("Name: %s, NameA: %s, CreatedAt: %s\n",
			item.Name, item.NameA, item.CreatedAt.Format("2006-01-02 15:04:05"))
	}
}
