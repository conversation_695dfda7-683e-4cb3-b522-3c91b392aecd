# zodiacus
万年历、运势、论财、排盘专家版。

## 本地调试

认证服务转发：
```bash
kubectl port-forward -n paipan casdoor-deployment-857c8959dc-bxqcp 9527:60111
# 或者
kubectl port-forward -n paipan svc/casdoor-service 9527:80
```

# macos本地编译:
```bash
# 设置交叉编译环境变量
export GOOS=linux
export GOARCH=amd64
export CGO_ENABLED=0

# 执行编译 # 会在根目录生成master文件
go build -o master ./cmd/master/main.go
# 验证编译结果
file master  # 这时应该显示 "ELF 64-bit LSB executable, x86-64"

# 添加执行权限
chmod +x master

# 运行程序（使用默认配置文件路径） 使用的是默认的是 etc/local.yml
./master

# 运行程序（后台运行并输出日志）
nohup ./master > master.log 2>&1 &

# 查看进程是否运行
ps -ef | grep master
# 实时查看日志
tail -f master.log

# 执行好后在浏览器可访问swagger
http://**************:60115/swagger/index.html

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~以下写到服务器上为restart.sh,并且权限为777,自动启动命令:
# 停止已存在的master进程
echo "正在停止已存在的master进程..."
pid=$(ps -ef | grep './master' | grep -v grep | awk '{print $2}')

if [ -z "$pid" ]; then
    echo "没有找到运行中的master进程"
else
    echo "终止进程 PID: $pid"
    kill -9 $pid
fi

# 等待进程完全结束
sleep 2

# 启动新的master进程
echo "正在启动master应用..."
nohup ./master > master.log 2>&1 &

# 打印新进程ID
new_pid=$(ps -ef | grep './master' | grep -v grep | awk '{print $2}')
echo "master进程已启动，PID: $new_pid"
                                                           
```