package main

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"zodiacus/pkg/pubsub"
)

func main() {
	// 初始化日志
	logger, _ := zap.NewDevelopment()

	// 初始化Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})

	ctx := context.Background()

	// 示例1：使用map参数订阅多个频道
	demonstrateMultiChannelSubscription(ctx, rdb, logger)

	// 示例2：使用便捷方法订阅单个频道
	demonstrateSingleChannelSubscription(ctx, rdb, logger)
}

// 演示多频道订阅
func demonstrateMultiChannelSubscription(ctx context.Context, rdb *redis.Client, logger *zap.Logger) {
	logger.Info("=== 多频道订阅示例 ===")

	// 创建不同的处理器
	userHandler := &UserEventHandler{logger: logger}
	orderHandler := &OrderEventHandler{logger: logger}
	notificationHandler := &NotificationHandler{logger: logger}

	// 使用map参数：key为channel，value为对应的handler
	channelHandlers := map[string]pubsub.MessageHandler{
		"events:user":         userHandler,
		"events:order":        orderHandler,
		"events:notification": notificationHandler,
	}

	// 创建PubSub实例
	ps := pubsub.NewPubSub(rdb, logger)

	// 启动订阅（在实际应用中，这通常在goroutine中运行）
	go func() {
		err := ps.Subscribe(ctx, channelHandlers)
		if err != nil {
			logger.Error("Multi-channel subscription failed", zap.Error(err))
		}
	}()

	logger.Info("Multi-channel subscription started")
}

// 演示单频道订阅
func demonstrateSingleChannelSubscription(ctx context.Context, rdb *redis.Client, logger *zap.Logger) {
	logger.Info("=== 单频道订阅示例 ===")

	// 创建处理器
	membershipHandler := pubsub.NewSimpleMembershipChangeHandler(logger)

	// 创建PubSub实例
	ps := pubsub.NewPubSub(rdb, logger)

	// 使用便捷方法订阅单个频道
	go func() {
		err := ps.SubscribeSingle(ctx, "membership:change", membershipHandler)
		if err != nil {
			logger.Error("Single channel subscription failed", zap.Error(err))
		}
	}()

	logger.Info("Single channel subscription started")
}

// 示例处理器实现
type UserEventHandler struct {
	logger *zap.Logger
}

func (h *UserEventHandler) Handle(ctx context.Context, msg *pubsub.Message) error {
	h.logger.Info("Processing user event", 
		zap.String("messageId", msg.ID),
		zap.String("type", msg.Type))
	// 处理用户事件的逻辑
	return nil
}

func (h *UserEventHandler) GetType() string {
	return "user_event"
}

type OrderEventHandler struct {
	logger *zap.Logger
}

func (h *OrderEventHandler) Handle(ctx context.Context, msg *pubsub.Message) error {
	h.logger.Info("Processing order event", 
		zap.String("messageId", msg.ID),
		zap.String("type", msg.Type))
	// 处理订单事件的逻辑
	return nil
}

func (h *OrderEventHandler) GetType() string {
	return "order_event"
}

type NotificationHandler struct {
	logger *zap.Logger
}

func (h *NotificationHandler) Handle(ctx context.Context, msg *pubsub.Message) error {
	h.logger.Info("Processing notification", 
		zap.String("messageId", msg.ID),
		zap.String("type", msg.Type))
	// 处理通知的逻辑
	return nil
}

func (h *NotificationHandler) GetType() string {
	return "notification"
}

// 发布消息示例
func publishExampleMessages(ctx context.Context, rdb *redis.Client, logger *zap.Logger) {
	ps := pubsub.NewPubSub(rdb, logger)

	// 发布用户事件
	userMsg := &pubsub.Message{
		ID:        "user_001",
		Type:      "user_event",
		Data:      map[string]interface{}{"action": "login", "user_id": "123"},
		Timestamp: time.Now(),
	}
	ps.Publish(ctx, "events:user", userMsg)

	// 发布订单事件
	orderMsg := &pubsub.Message{
		ID:        "order_001",
		Type:      "order_event",
		Data:      map[string]interface{}{"action": "created", "order_id": "456"},
		Timestamp: time.Now(),
	}
	ps.Publish(ctx, "events:order", orderMsg)

	// 发布通知
	notificationMsg := &pubsub.Message{
		ID:        "notification_001",
		Type:      "notification",
		Data:      map[string]interface{}{"type": "email", "to": "<EMAIL>"},
		Timestamp: time.Now(),
	}
	ps.Publish(ctx, "events:notification", notificationMsg)

	logger.Info("Example messages published")
}

// 使用场景说明
func usageScenarios() {
	/*
	使用场景1：多频道多处理器
	适用于：一个服务需要处理多种不同类型的消息
	
	channelHandlers := map[string]MessageHandler{
		"events:user":    userHandler,
		"events:order":   orderHandler,
		"events:payment": paymentHandler,
	}
	pubsub.Subscribe(ctx, channelHandlers)

	使用场景2：单频道单处理器
	适用于：专门处理某一种消息的服务
	
	pubsub.SubscribeSingle(ctx, "membership:change", membershipHandler)

	使用场景3：同一类型消息的不同频道
	适用于：按地区、业务线等维度分离的同类消息
	
	channelHandlers := map[string]MessageHandler{
		"orders:us":   orderHandler,
		"orders:eu":   orderHandler,
		"orders:asia": orderHandler,
	}
	pubsub.Subscribe(ctx, channelHandlers)
	*/
}
