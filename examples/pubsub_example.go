package main

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"zodiacus/pkg/log"
	"zodiacus/pkg/pubsub"
)

func main() {
	// 初始化日志
	logger, _ := zap.NewDevelopment()

	// 初始化Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})

	// 测试Redis连接
	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		logger.Fatal("Failed to connect to Redis", zap.Error(err))
	}

	// 创建消息发布器
	publisher := pubsub.NewMessagePublisher(rdb, logger)

	// 示例：发布会员时长变更事件
	logger.Info("Publishing membership change events...")

	// 会员升级
	err = publisher.PublishMembershipUpgrade(ctx, "user123", "2024-01-01", "2024-12-31")
	if err != nil {
		logger.Error("Failed to publish membership upgrade event", "error", err)
	}

	// 会员降级
	err = publisher.PublishMembershipDowngrade(ctx, "user123", "2024-12-31", "2024-06-30")
	if err != nil {
		logger.Error("Failed to publish membership downgrade event", "error", err)
	}

	// 会员延期
	err = publisher.PublishMembershipExtend(ctx, "user123", "2024-06-30", "2024-12-31")
	if err != nil {
		logger.Error("Failed to publish membership extend event", "error", err)
	}

	// 会员过期
	err = publisher.PublishMembershipExpire(ctx, "user123", "2024-01-01")
	if err != nil {
		logger.Error("Failed to publish membership expire event", "error", err)
	}

	// 会员激活
	err = publisher.PublishMembershipActivate(ctx, "user123", "2024-12-31")
	if err != nil {
		logger.Error("Failed to publish membership activate event", "error", err)
	}

	logger.Info("All messages published successfully!")
	logger.Info("You can now run the job consumer to see the messages being processed.")
	
	// 关闭Redis连接
	rdb.Close()
}

// 这个示例展示了如何在实际业务代码中使用PubSub
func BusinessLogicExample() {
	// 假设这些是从依赖注入容器中获取的
	var (
		logger    *log.Logger
		publisher *pubsub.MessagePublisher
		ctx       = context.Background()
	)

	// 会员升级业务逻辑
	handleMembershipUpgrade := func(userID, oldExpireTime, newExpireTime string) error {
		// 1. 更新数据库中的用户会员信息
		// ... 数据库操作 ...

		// 2. 发布会员升级事件（异步处理后续逻辑）
		err := publisher.PublishMembershipUpgrade(ctx, userID, oldExpireTime, newExpireTime)
		if err != nil {
			logger.Error("Failed to publish membership upgrade event", "error", err)
			// 注意：这里不应该返回错误，因为主要业务逻辑已经完成
		}

		return nil
	}

	// 会员过期检查业务逻辑
	handleMembershipExpireCheck := func(userID, expireTime string) error {
		// 1. 检查会员是否过期
		// ... 业务逻辑 ...

		// 2. 如果过期，更新用户状态
		// ... 数据库操作 ...

		// 3. 发布会员过期事件
		err := publisher.PublishMembershipExpire(ctx, userID, expireTime)
		if err != nil {
			logger.Error("Failed to publish membership expire event", "error", err)
		}

		return nil
	}

	// 使用示例
	_ = handleMembershipUpgrade("user123", "2024-01-01", "2024-12-31")
	_ = handleMembershipExpireCheck("user456", "2024-01-01")
}
