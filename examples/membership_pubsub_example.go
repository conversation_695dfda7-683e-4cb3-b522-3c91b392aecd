package main

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"zodiacus/pkg/pubsub"
)

func main() {
	// 初始化日志
	logger, _ := zap.NewDevelopment()

	// 初始化Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})

	// 测试Redis连接
	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		logger.Fatal("Failed to connect to Redis", zap.Error(err))
	}

	// 创建发布器
	publisher := pubsub.NewMembershipPublisher(rdb, logger)

	// 示例1：发布单个会员时长变更事件
	logger.Info("Publishing single membership change event...")
	err = publisher.PublishMembershipChange(ctx, "user123")
	if err != nil {
		logger.Error("Failed to publish membership change event", zap.Error(err))
	}

	// 示例2：发布带重试的会员时长变更事件
	logger.Info("Publishing membership change event with retry...")
	err = publisher.PublishMembershipChangeWithRetry(ctx, "user456", 3)
	if err != nil {
		logger.Error("Failed to publish membership change event with retry", zap.Error(err))
	}

	// 示例3：批量发布会员时长变更事件
	logger.Info("Publishing batch membership change events...")
	userIDs := []string{"user789", "user101", "user102", "user103"}
	err = publisher.PublishBatchMembershipChange(ctx, userIDs)
	if err != nil {
		logger.Error("Failed to publish batch membership change events", zap.Error(err))
	}

	logger.Info("All membership change events published successfully!")
	logger.Info("Channel:", zap.String("channel", publisher.GetChannelName()))

	// 关闭连接
	rdb.Close()
}

// 订阅示例（通常在单独的服务中运行）
func subscribeExample() {
	// 初始化日志
	logger, _ := zap.NewDevelopment()

	// 初始化Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})

	// 创建订阅器和处理器
	subscriber := pubsub.NewMembershipSubscriber(rdb, logger)
	handler := pubsub.NewSimpleMembershipChangeHandler(logger)

	ctx := context.Background()

	logger.Info("Starting membership change subscriber...")

	// 方式1：普通订阅
	err := subscriber.SubscribeMembershipChange(ctx, handler)
	if err != nil {
		logger.Error("Subscription failed", zap.Error(err))
	}

	// 方式2：使用工作池订阅（并发处理）
	// err := subscriber.SubscribeWithWorkerPool(ctx, handler, 5)
	// if err != nil {
	//     logger.Error("Worker pool subscription failed", zap.Error(err))
	// }
}

// 业务逻辑示例
func businessLogicExample() {
	// 假设这些是从依赖注入容器中获取的
	var (
		logger    *zap.Logger
		publisher *pubsub.MembershipPublisher
		ctx       = context.Background()
	)

	// 会员升级业务逻辑
	handleMembershipUpgrade := func(userID string) error {
		// 1. 更新数据库中的用户会员信息
		// ... 数据库操作 ...

		// 2. 发布会员时长变更事件（异步处理后续逻辑）
		err := publisher.PublishMembershipChange(ctx, userID)
		if err != nil {
			logger.Error("Failed to publish membership change event", zap.Error(err))
			// 注意：这里不应该返回错误，因为主要业务逻辑已经完成
		}

		return nil
	}

	// 批量会员状态更新
	handleBatchMembershipUpdate := func(userIDs []string) error {
		// 1. 批量更新数据库
		// ... 数据库操作 ...

		// 2. 批量发布会员时长变更事件
		err := publisher.PublishBatchMembershipChange(ctx, userIDs)
		if err != nil {
			logger.Error("Failed to publish batch membership change events", zap.Error(err))
		}

		return nil
	}

	// 使用示例
	_ = handleMembershipUpgrade("user123")
	_ = handleBatchMembershipUpdate([]string{"user456", "user789"})
}
