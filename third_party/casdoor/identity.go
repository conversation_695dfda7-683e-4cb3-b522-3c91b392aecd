package casdoor

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/spf13/viper"
	"strings"
)

type (
	Claims = casdoorsdk.Claims
	Client struct {
		endpoint string
	}
	User = casdoorsdk.User
)

func NewClient(conf *viper.Viper) *Client {
	return &Client{
		//endpoint: conf.GetString("identity.endpoint"),
	}
}

func (slf *Client) ParseJwtToken(ctx context.Context, accessToken, path string) (*Claims, error) {
	/*
		var (
			url  = fmt.Sprintf("%s/v1/user/getUserInfo", slf.endpoint)
			body = &parseTokenRequest{
				AccessToken: accessToken,
				Path:        path,
			}
			result = &parseTokenResponse{}
		)
		response, err := resty.New().R().SetContext(ctx).
			SetHeader("Content-Type", "application/json").
			SetBody(body).SetResult(result).Post(url)
		if err != nil {
			return nil, err
		}
		if response.StatusCode() != 200 {
			return nil, fmt.Errorf("status code: %d", response.StatusCode())
		} else if result.Code != 0 {
			return nil, fmt.Errorf("code: %d, message: %s", result.Code, result.Message)
		}
	*/
	// 注：临时由本地解析token。
	parts := strings.Split(accessToken, ".")
	if len(parts) < 2 {
		return nil, fmt.Errorf("invalid token")
	}
	raw, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return nil, err
	}
	var user casdoorsdk.User
	if err = json.Unmarshal(raw, &user); err != nil {
		return nil, err
	}
	var result = &parseTokenResponse{
		Data: Claims{
			User: user,
		},
	}
	return &result.Data, nil
}
