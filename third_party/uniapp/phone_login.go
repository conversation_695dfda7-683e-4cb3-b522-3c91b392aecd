package uniapp

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"sort"
	"strings"
)

type (
	QuicklyLoginRequest struct {
		AccessToken string `json:"accessToken"`
		OpenId      string `json:"openid"`
	}
	QuicklyLoginResponse struct {
		Code        int    `json:"code"`
		ErrCode     int    `json:"errCode"`
		ErrMsg      string `json:"errMsg"`
		Success     bool   `json:"success"`
		PhoneNumber string `json:"phoneNumber"`
	}
)

func (slf *Client) signature(params map[string]string, secret string) string {
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	pairs := make([]string, 0, len(params))
	for _, k := range keys {
		pairs = append(pairs, fmt.Sprintf("%s=%s", k, params[k]))
	}
	signStr := strings.Join(pairs, "&")
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(signStr))
	return hex.EncodeToString(h.Sum(nil))
}

func (slf *Client) QuicklyLogin(ctx context.Context, req *QuicklyLoginRequest) (*QuicklyLoginResponse, error) {
	sign := slf.signature(map[string]string{
		"access_token": req.AccessToken,
		"openid":       req.OpenId,
	}, slf.secret)

	resp, err := resty.New().R().SetContext(ctx).
		SetQueryParams(map[string]string{
			"access_token": req.AccessToken,
			"openid":       req.OpenId,
			"sign":         sign,
		}).
		Get(slf.endpoint)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("uniapp response status code: %d", resp.StatusCode())
	}
	if resp.IsError() {
		return nil, fmt.Errorf("uniapp response error: %s", resp.Status())
	}
	var result QuicklyLoginResponse
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return nil, fmt.Errorf("解析响应错误: %v", err)
	}
	return &result, nil
}
