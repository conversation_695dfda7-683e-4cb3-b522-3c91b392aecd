package test

import (
	"fmt"
	"regexp"
	"sort"
	"strings"
	"testing"
	"zodiacus/pkg/array"
	"zodiacus/pkg/geoip"
	"zodiacus/pkg/sid"
	"zodiacus/pkg/strs"
	"zodiacus/third_party/corona"
)

func TestAnything(t *testing.T) {
	regex := regexp.MustCompile(`(?P<Month>\p{Han}+月)(?P<Day>\p{Han}+)`)
	matches := regex.FindStringSubmatch("腊月十八")
	fmt.Println(matches)
}

func TestZh2Number(t *testing.T) {
	number := strs.Digit2ZhUpper("2006年一月二十1日")
	fmt.Println(number)
}

func TestNo1(t *testing.T) {
	// 给定字符串
	text := "一九九八年十月初一 午时"

	// 正则表达式
	pattern := `月(.*?)\s(.*)`

	// 编译正则表达式
	re := regexp.MustCompile(pattern)

	// 提取匹配内容
	matches := re.FindStringSubmatch(text)

	// 检查匹配结果
	if len(matches) > 2 {
		day := matches[1]  // 提取“初一”
		time := matches[2] // 提取“午时”
		fmt.Println("日子:", day)
		fmt.Println("时辰:", time)
	} else {
		fmt.Println("未匹配到内容")
	}
}

func Test2(t *testing.T) {
	suffix := strings.TrimSuffix("申时", "时")
	fmt.Println(suffix)
}

func TestMeow(t *testing.T) {
	newSid := sid.NewSid()
	fmt.Println(newSid.String())
}

func TestANy(t *testing.T) {
	// 示例列表和起始索引
	scores := []int{5, 3, 8, 9, 2, 7, 8, 6, 4, 10, 3, 6, 8, 7, 9, 5, 2, 8, 7, 10, 7, 4, 7}
	startIndex := 20

	// 给定起始索引，从数组里找出从起始索引开始的至多20个元素里评分最高和次高的两个索引（如果有两个相同的最高评分/次高评分则取距离起始索引最近的）

	// 调用函数获取结果
	//topIndices, lowestIndices := findTopTwoIndices(scores, startIndex)
	//fmt.Println(topIndices, lowestIndices)

	fmt.Println(findTopNIndices(scores, startIndex, 2))
	fmt.Println(findTopNIndicesPlus(scores, startIndex, 2))
}

// 返回前n个最高分的索引数组，索引按照分数降序排序
func findTopNIndices(arr []int, startIndex int, n int) ([]int, []int) {
	// 确定实际要处理的元素数量（不超过20个）
	length := len(arr) - startIndex
	if length > 20 {
		length = 20
	}
	if length <= 0 || n <= 0 {
		return nil, nil
	}
	type scoreIndex struct {
		index int
		score int
	}
	scores := make([]scoreIndex, length)
	for i := 0; i < length; i++ {
		scores[i] = scoreIndex{
			index: startIndex + i,
			score: arr[startIndex+i],
		}
	}
	// 创建用于最高分排序的副本
	topScores := make([]scoreIndex, length)
	copy(topScores, scores)
	// 最高分排序（分数相同时索引小的优先）
	sort.Slice(topScores, func(i, j int) bool {
		if topScores[i].score == topScores[j].score {
			return topScores[i].index < topScores[j].index
		}
		return topScores[i].score > topScores[j].score
	})
	// 最低分排序（分数相同时索引小的优先）
	sort.Slice(scores, func(i, j int) bool {
		if scores[i].score == scores[j].score {
			return scores[i].index < scores[j].index
		}
		return scores[i].score < scores[j].score
	})
	// 确定返回结果的长度（不超过n和实际长度）
	resultLen := n
	if resultLen > length {
		resultLen = length
	}
	var (
		topIndices    = make([]int, resultLen) // 按分数从高到低排序的索引数组
		bottomIndices = make([]int, resultLen) // 按分数从低到高排序的索引数组
	)
	// 填充最高分索引数组
	for i := 0; i < resultLen; i++ {
		topIndices[i] = topScores[i].index
	}

	// 填充最低分索引数组
	for i := 0; i < resultLen; i++ {
		bottomIndices[i] = scores[i].index
	}
	return topIndices, bottomIndices
}

func findTopNIndicesPlus(arr []int, startIndex int, n int) ([]int, []int) {
	// 确定实际要处理的元素数量（不超过20个）
	length := len(arr) - startIndex
	if length > 20 {
		length = 20
	}
	if length <= 0 || n <= 0 {
		return nil, nil
	}

	type scoreIndex struct {
		index int
		score int
	}
	scores := make([]scoreIndex, length)
	for i := 0; i < length; i++ {
		scores[i] = scoreIndex{
			index: startIndex + i,
			score: arr[startIndex+i],
		}
	}

	// 创建用于最高分排序的副本
	topScores := make([]scoreIndex, length)
	copy(topScores, scores)

	// 最高分排序（只按分数降序）
	sort.Slice(topScores, func(i, j int) bool {
		return topScores[i].score > topScores[j].score
	})

	// 最低分排序（只按分数升序）
	sort.Slice(scores, func(i, j int) bool {
		return scores[i].score < scores[j].score
	})

	// 收集最高分索引（包含相同分数）
	topIndices := []int{}
	if len(topScores) > 0 {
		currentScore := topScores[0].score
		for i := 0; i < length && len(topIndices) < n; i++ {
			if topScores[i].score == currentScore {
				topIndices = append(topIndices, topScores[i].index)
			} else if len(topIndices) < n {
				currentScore = topScores[i].score
				topIndices = append(topIndices, topScores[i].index)
			}
		}
	}

	// 收集最低分索引（包含相同分数）
	bottomIndices := []int{}
	if len(scores) > 0 {
		currentScore := scores[0].score
		for i := 0; i < length && len(bottomIndices) < n; i++ {
			if scores[i].score == currentScore {
				bottomIndices = append(bottomIndices, scores[i].index)
			} else if len(bottomIndices) < n {
				currentScore = scores[i].score
				bottomIndices = append(bottomIndices, scores[i].index)
			}
		}
	}

	return topIndices, bottomIndices
}

type Result struct {
	data []string
}

func find2And3CombFunc(arr []string, targets ...string) ([][]string, [][]string) {
	arr = array.Unique(arr)
	var twoCombos [][]string
	var threeCombos [][]string
	n := len(arr)
	var actualTargets []string
	if len(targets) == 0 {
		seen := make(map[string]bool)
		for _, v := range arr {
			if !seen[v] {
				actualTargets = append(actualTargets, v)
				seen[v] = true
			}
		}
	} else {
		actualTargets = targets
	}

	for _, tgt := range actualTargets {
		for i := 0; i < n; i++ {
			if arr[i] == tgt {
				// two-combos
				for j := 0; j < n; j++ {
					if i != j {
						twoCombos = append(twoCombos, []string{tgt, arr[j]})
					}
				}
				// three-combos
				for j := 0; j < n; j++ {
					for k := j + 1; k < n; k++ {
						if i != j && i != k && j != k {
							threeCombos = append(threeCombos, []string{tgt, arr[j], arr[k]})
						}
					}
				}
			}
		}
	}
	return twoCombos, threeCombos
}

func TestAny(t *testing.T) {
	arr := []string{"A", "B", "C", "A"}

	// 用法1：不传 target，使用 arr 中所有唯一元素
	two1, three1 := find2And3CombFunc(arr)
	fmt.Println("== 所有元素作为 target ==")
	fmt.Println("Two:", two1)
	fmt.Println("Three:", three1)

	// 用法2：只传某些 target
	two2, three2 := find2And3CombFunc(arr, "B")
	fmt.Println("== 仅 B 作为 target ==")
	fmt.Println("Two:", two2)
	fmt.Println("Three:", three2)
}

func TestDizhi(t *testing.T) {
	helper := corona.NewHelper()
	fmt.Println(helper.NextDizhi("子"))
	fmt.Println(helper.NextDizhi("丑"))
	fmt.Println(helper.NextDizhi("亥"))

	fmt.Println(helper.PrevDizhi("子"))
	fmt.Println(helper.PrevDizhi("丑"))
	fmt.Println(helper.PrevDizhi("亥"))
}

var comb23 = func(arr []string, targets ...string) ([][]string, [][]string) {
	var uniqueComb = func(combos [][]string) [][]string {
		seen := make(map[string]struct{})
		var tmp [][]string
		for _, combo := range combos {
			sorted := append([]string{}, combo...)
			sort.Strings(sorted)
			key := strings.Join(sorted, "|")
			if _, exists := seen[key]; !exists {
				seen[key] = struct{}{}
				tmp = append(tmp, combo)
			}
		}
		return tmp
	}
	arr = array.Unique(arr)
	var twoCombos [][]string
	var threeCombos [][]string
	n := len(arr)
	var actualTargets []string
	if len(targets) == 0 {
		seen := make(map[string]bool)
		for _, v := range arr {
			if !seen[v] {
				actualTargets = append(actualTargets, v)
				seen[v] = true
			}
		}
	} else {
		actualTargets = targets
	}

	for _, tgt := range actualTargets {
		// two-combos
		for j := 0; j < n; j++ {
			if arr[j] != tgt {
				twoCombos = append(twoCombos, []string{tgt, arr[j]})
			}
		}
		// three-combos
		for j := 0; j < n; j++ {
			for k := j + 1; k < n; k++ {
				if arr[j] != arr[k] && arr[j] != tgt && arr[k] != tgt {
					threeCombos = append(threeCombos, []string{tgt, arr[j], arr[k]})
				}
			}
		}
	}
	return uniqueComb(twoCombos), uniqueComb(threeCombos)
}

var fnZhuIdxes = func(arr []string, target string) []string {
	var res []string
	for i, s := range arr {
		if s == target {
			switch i {
			case 0:
				res = append(res, "年")
			case 1:
				res = append(res, "月")
			case 2:
				res = append(res, "日")
			case 3:
				res = append(res, "时")
			}
		}
	}
	return res
}

func TestComb23(t *testing.T) {
	dizhi := []string{"午", "巳", "午", "未"} // 四柱地支
	dyDz := "午"                           // 大运地支
	_, three := comb23(dizhi, dyDz)
	var tmp []string
	for _, arr := range three {
		he, ok := func(a, b, c string) (string, bool) {
			str := a + b + c
			if strings.Contains(str, "子") &&
				strings.Contains(str, "辰") &&
				strings.Contains(str, "申") {
				return "水", true
			}
			return "", false
		}(arr[0], arr[1], arr[2])
		if !ok {
			continue
		}
		var (
			anotherDz = array.Remove(arr, dyDz)
			idxes1    = fnZhuIdxes(dizhi[:4], anotherDz[0])
			idxes2    = fnZhuIdxes(dizhi[:4], anotherDz[1])
		)
		for _, idx1 := range idxes1 {
			for _, idx2 := range idxes2 {
				tmp = append(tmp, fmt.Sprintf("%s%s%s大运与%s与%s三合%s局", dyDz, anotherDz[0], anotherDz[1], idx1, idx2, he))
			}
		}
	}
	fmt.Println(tmp)
}

func TestFormatTime(t *testing.T) {
	var padYear = func(birthtime string) string {
		parts := strings.SplitN(birthtime, "-", 2)
		year := parts[0]
		if len(year) >= 4 {
			return birthtime
		}
		paddedYear := fmt.Sprintf("%04s", year)
		return paddedYear + "-" + parts[1]
	}
	fmt.Println(padYear("77-01-01 00:00:00"))
}

func reorder(arr []int) []int {
	n := len(arr)
	if n < 5 || n > 9 {
		return arr
	}
	prefix := arr[:n-4]
	suffix := arr[n-4:]
	for i, j := 0, len(prefix)-1; i < j; i, j = i+1, j-1 {
		prefix[i], prefix[j] = prefix[j], prefix[i]
	}
	result := append(suffix, prefix...)
	return result
}

func TestAny2(t *testing.T) {
	//arr := []int{9, 8, 7, 6, 5, 1, 2, 3, 4}
	arr := []int{6, 5, 1, 2, 3, 4}
	newArr := reorder(arr)
	fmt.Println(newArr) // 输出: [1 2 3 4 5 6 7 8 9]
}

func TestIP(t *testing.T) {
	client := geoip.NewClient(conf)
	ret, err := client.Query("**************")
	if err != nil {
		panic(err)
	}
	fmt.Println(ret)
}
