package v1

import (
	"time"
	"zodiacus/pkg/jwthub"
)

type (
	DeletePaipanRecordRequest struct {
		IDs  []int64      `json:"ids"` // ID
		User *jwthub.Auth `json:"-"`   // 用户信息
	}
	DeletePaipanRecordResponse struct {
		Response
	}
)

type (
	PageListPaipanRecordRequestParam struct {
		Name              *string      `json:"name"`              // 命例名称
		Gender            []int        `json:"gender"`            // 性别：1-男、2-女
		BirthTimeStart    *string      `json:"birthTimeStart"`    // 出生日期开始
		BirthTimeEnd      *string      `json:"birthTimeEnd"`      // 出生日期结束
		Bazi              *string      `json:"bazi"`              // 八字
		Birthplace        *string      `json:"birthplace"`        // 出生地
		BirthTimeSunStart *string      `json:"birthTimeSunStart"` // 真太阳时开始
		BirthTimeSunEnd   *string      `json:"birthTimeSunEnd"`   // 真太阳时结束
		AppIDs            []int        `json:"appIDs"`            // 应用ID：2-排盘、3-万年历、4-运势、5-论财
		User              *jwthub.Auth `json:"-"`                 // 用户信息
		Application       string       `json:"application"`       // 应用标识
	}
	PageListPaipanRecordRequest          = PagerIn[PageListPaipanRecordRequestParam]
	PageListPaipanRecordResponseDataItem struct {
		ID              int64          `json:"id" bun:"id"`
		UserID          string         `json:"userID" bun:"user_id"`                            // 用户ID
		Name            string         `json:"name" bun:"name"`                                 // 命例名称
		Gender          int            `json:"-" bun:"gender"`                                  // 性别：1-男, 2-女
		GenderStr       string         `json:"gender" bun:"-" example:"1"`                      // 性别：1-男, 2-女
		Birthtime       time.Time      `json:"-" bun:"birthtime" example:"2021-01-01 00:00:00"` // 出生日期
		BirthtimeStr    string         `json:"birthtime" bun:"-" example:"2021-01-01 00:00:00"` // 出生日期
		BirthtimeSun    time.Time      `json:"-" bun:"birthtime_sun"`                           // 真太阳时
		BirthtimeSunStr string         `json:"birthtimeSun" bun:"-"`                            // 真太阳时
		BirthtimeLunar  string         `json:"birthtimeLunar" bun:"birthtime_lunar"`            // 农历出生日期
		Bazi            []string       `json:"bazi" bun:"bazi"`                                 // 八字
		Birthplace      []string       `json:"birthplace" bun:"birthplace"`                     // 出生地
		Type            int            `json:"-" bun:"type" example:"1"`                        // 类型
		TypeStr         string         `json:"type" bun:"-"`                                    // 类型
		SaveTime        time.Time      `json:"-" bun:"save_time"`                               // 保存时间
		SaveTimeStr     string         `json:"saveTime" bun:"-"`                                // 保存时间
		UserAgent       string         `json:"userAgent" bun:"user_agent"`                      // 用户代理
		AppID           int64          `json:"appID" bun:"app_id" example:"1"`                  // 应用ID
		AppName         string         `json:"appName" bun:"app_name"`                          // 应用名称
		AppPlatformID   int64          `json:"appPlatformID" bun:"app_platform_id" example:"1"` // 应用平台ID
		AppPlatformName string         `json:"appPlatformName" bun:"app_platform_name"`         // 应用平台名称
		CreatedAt       time.Time      `json:"createdAt" bun:"created_at"`                      // 创建时间
		CreatedTime     string         `json:"createdTime" bun:"-"`                             // 创建时间
		ExtraInfo       map[string]any `json:"extraInfo" bun:"extra_info"`                      // 额外信息
	}
	PageListPaipanRecordResponseData = PagerOut[*PageListPaipanRecordResponseDataItem]
	PageListPaipanRecordResponse     struct {
		Response
		Data PageListPaipanRecordResponseData `json:"data"`
	}
)

type (
	PaipanRecordOwnRequest struct {
		IDs  []int64      `json:"ids" binding:"required"` // ID
		User *jwthub.Auth `json:"-"`                      // 用户信息
	}
	PaipanRecordOwnResponse struct {
		Response
	}
)
