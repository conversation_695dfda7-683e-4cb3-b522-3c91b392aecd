package v1

type (
	DateSubRequest struct {
		Date1 string `json:"date1" binding:"required" example:"2021-01-01"` // 日期1
		Date2 string `json:"date2" binding:"required" example:"2021-01-02"` // 日期2
	} // 日期1必须大于日期2
	DateSubResponseData = int
	DateSubResponse     struct {
		Response
		Data DateSubResponseData `json:"data"`
	}
)

type (
	DateSubPastRequest struct {
		Date string `json:"date" binding:"required" example:"2021-01-01"` // 日期
		Days int    `json:"days" binding:"required" example:"1"`          // 天数
	}
	DateSubPastResponseData = string
	DateSubPastResponse     struct {
		Response
		Data DateSubPastResponseData `json:"data"`
	}
)

type (
	DateSubFutureRequest struct {
		Date string `json:"date" binding:"required" example:"2021-01-01"` // 日期
		Days int    `json:"days" binding:"required" example:"1"`          // 天数
	}
	DateSubFutureResponseData = string
	DateSubFutureResponse     struct {
		Response
		Data DateSubFutureResponseData `json:"data"`
	}
)
