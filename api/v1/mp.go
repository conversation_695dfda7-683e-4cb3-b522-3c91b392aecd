package v1

import "zodiacus/pkg/jwthub"

type (
	MpQrCodeRequest struct {
		ChannelID int64        `json:"channelID" binding:"required"`
		AppID     int64        `json:"appID" binding:"required"`
		Page      string       `json:"page" binding:"required"`
		User      *jwthub.Auth `json:"-"`
	}
	MpQrCodeResponseData = Media
)

type (
	MpQrCodePageListRequestParam struct {
		AppIDs        []int64 `json:"appIDs"`
		SceneStr      *string `json:"sceneStr"`
		Remark        *string `json:"remark"`
		CreateAtStart *string `json:"createAtStart"`
		CreateAtEnd   *string `json:"createAtEnd"`
	}
	MpQrCodePageListRequest          = PagerIn[MpQrCodePageListRequestParam]
	MpQrCodePageListResponseDataItem struct {
		AppID     string `json:"appID" bun:"app_id"`
		AppName   string `json:"appName" bun:"app_name"`
		SceneStr  string `json:"sceneStr" bun:"scene_str"`
		Remark    string `json:"remark" bun:"remark"`
		Page      string `json:"page" bun:"page"`
		ImageData []byte `json:"imageData" bun:"image_data"`
		ImageType string `json:"imageType" bun:"image_type"`
		CreatedAt string `json:"createdAt" bun:"created_at"`
	}
	MpQrCodePageListResponseData = PagerOut[*MpQrCodePageListResponseDataItem]
	MpQrCodePageListResponse     struct {
		Response
		Data *MpQrCodePageListResponseData `json:"data"`
	}
)
