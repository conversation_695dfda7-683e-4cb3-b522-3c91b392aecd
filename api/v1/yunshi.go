package v1

import (
	"zodiacus/internal/model"
)

type (
	YunshiGuaxiang   = model.Guaxiang
	YunshiScoreDayun struct {
		Ganzhi string  `json:"ganzhi"`
		Score  float32 `json:"score"`
	}
	YunshiNianScore struct {
		InitialScore float32 `json:"-"`
		OverallScore float32 `json:"-"`
		FinalScore   float32 `json:"finalScore"`
		Keyword      string  `json:"keyword"`
		Ganzhi       string  `json:"ganzhi"`
	}
	YunshiYueScore struct {
		Jieqi        string  `json:"jieqi"`
		Ganzhi       string  `json:"ganzhi,omitempty"`
		JieqiTime    string  `json:"jieqiTime"`
		InitialScore float32 `json:"-"`
		OverallScore float32 `json:"-"`
		FinalScore   float32 `json:"finalScore,omitempty"`
	}
	YunshiScore struct {
		Dayun   *YunshiScoreDayun `json:"dayun"`   // 大运
		Liunian *YunshiNianScore  `json:"liunian"` // 流年
		Liuyue  []*YunshiYueScore `json:"liuyue"`  // 流月（十三个月，一月到下一年一月）
	}
	Yun<PERSON><PERSON><PERSON><PERSON>hu struct {
		Name           string   `json:"name"`           // 姓名
		Birthtime      string   `json:"birthtime"`      // 出生日期
		BirthtimeLunar string   `json:"birthtimeLunar"` // 农历生日
		Birthplace     []string `json:"birthplace"`     // 出生地
		Gender         string   `json:"gender"`         // 性别
		Bazi           []string `json:"bazi"`           // 八字
		Wuxing         []string `json:"wuxing"`         // 五行：用神,喜神,忌神,仇神,闲神
		Zodiac         string   `json:"zodiac"`         // 生肖
	}
	YunshiRequest struct {
		Birthtime   string   `json:"birthtime" binding:"required" example:"2006-01-02 15:04:05"` // 生日
		Gender      string   `json:"gender" binding:"required,oneof=男 女" example:"男"`
		CurrentYear int      `json:"currentYear" binding:"required" example:"2021"` // 当前年份
		Coefficient float32  `json:"-"`                                             // 系数
		Name        string   `json:"name" example:"张三"`                             // 姓名
		Birthplace  []string `json:"birthplace"`                                    // 出生地
		User        string   `json:"-"`
		UserAgent   string   `json:"-"`
		IP          string   `json:"-"`
	}
	YunshiResponseData struct {
		ID          int64           `json:"id"`
		Mingzhu     *YunshiMinzhu   `json:"mingzhu"`     // 命主信息
		Guaxiang    *model.Guaxiang `json:"guaxiang"`    // 卦象
		Score       *YunshiScore    `json:"score"`       // 评分
		Suggestions []string        `json:"suggestions"` // 建议
	}
	YunshiResponse struct {
		Response
		Data YunshiResponseData `json:"data"`
	}
)
