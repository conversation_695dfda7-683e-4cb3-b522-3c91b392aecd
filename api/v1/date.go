package v1

import (
	"time"
	"zodiacus/pkg/jwthub"
)

type (
	// CalendarShichen 时辰
	CalendarShichen struct {
		Time        string `json:"time,omitempty" example:"23:00-00:59"` // 时辰
		Chong       string `json:"chong,omitempty" example:"虎"`          // 冲
		Sha         string `json:"sha,omitempty" example:"南"`            // 煞
		Bazi        string `json:"bazi,omitempty" example:"丙子"`          // 八字
		Jixiong     string `json:"jixiong,omitempty" example:"吉" `       // 吉
		CaiLocation string `json:"caiLocation,omitempty" example:"财神东北"` // 财位
		FuLocation  string `json:"fuLocation,omitempty" example:"福神西南"`  // 福位
		XiLocation  string `json:"xiLocation,omitempty" example:"喜神西北"`  // 喜位
		LuLocation  string `json:"luLocation,omitempty" example:"阳贵东南"`  // 禄位
		Yi          string `json:"yi,omitempty" example:"宜"`             // 宜
		Ji          string `json:"ji,omitempty" example:"忌"`             // 忌
	}
	CalendarDayRequest struct {
		Date  string       `json:"date" example:"2020-01-01"` // 公历日期
		User  *jwthub.Auth `json:"-"`
		AppID int64        `json:"appID" example:"3"` // 应用：2-排盘、3-万年历、4-运势、5-论财
	}
	CalendarDayResponseData struct {
		Date                string             `json:"date,omitempty" example:"2099-03-12"`                 // 公历日期
		LunarDate           string             `json:"lunarDate,omitempty" example:"二月廿一"`                  // 农历日期
		Zodiac              string             `json:"zodiac,omitempty" example:"鼠"`                        // 生肖
		Weekday             string             `json:"weekday,omitempty" example:"星期四"`                     // 星期
		Constellation       string             `json:"constellation,omitempty" example:"双鱼座"`               // 星座
		Festival            []string           `json:"festival,omitempty" example:"北方小年,南方小年"`              // 节日
		Yi                  []string           `json:"yi,omitempty" example:"祭祀,打扫,破屋坏垣"`                   // 宜
		Ji                  []string           `json:"ji,omitempty" example:"阴宅破土,安葬,启攒,探亲访友"`              // 忌
		Bazi1               string             `json:"bazi1,omitempty" example:"庚申"`                        // 八字1（年份干支）
		Bazi2               string             `json:"bazi2,omitempty" example:"壬午"`                        // 八字2（月份干支）
		Bazi2Next           string             `json:"bazi2Next,omitempty" example:"壬午"`                    // 八字2（下个月份干支）
		Bazi3               string             `json:"bazi3,omitempty" example:"辛巳"`                        // 八字3（日期干支）
		Zeri                string             `json:"zeri,omitempty" example:"大吉"`                         // 择日
		PengzubaijiOverview string             `json:"pengzubaijiOverview,omitempty" example:"猴日冲虎煞南"`      // 彭祖百忌概述
		YellowYears         int                `json:"yellowYears,omitempty" example:"4721"`                // 黄帝纪年：公元年+2697
		YellowYearsZh       string             `json:"yellowYearsZh,omitempty" example:"四千七百二十一"`           // 黄帝纪年
		Wuxing              string             `json:"wuxing,omitempty" example:"山下火"`                      // 五行
		Huangdao            string             `json:"huangdao,omitempty" example:"青龙"`                     // 黄道
		Heidao              string             `json:"heidao,omitempty" example:"白虎"`                       // 黑道
		Hou                 string             `json:"hou,omitempty" example:"半夏生"`                         // 七十二侯
		CaiLocation         string             `json:"caiLocation,omitempty" example:"东北"`                  // 财位
		FuLocation          string             `json:"fuLocation,omitempty" example:"西南"`                   // 福位
		XiLocation          string             `json:"xiLocation,omitempty" example:"西北"`                   // 喜位
		LuLocation          string             `json:"luLocation,omitempty" example:"东南"`                   // 禄位
		Shierjianri         string             `json:"shierjianri,omitempty" example:"定日"`                  // 十二建日
		Jishen              []string           `json:"jishen,omitempty" example:"天德合,月德合"`                  // 吉神
		Xiongshen           []string           `json:"xiongshen,omitempty" example:"月破,大耗,四击,九空"`           // 凶神
		Taishen             string             `json:"taishen,omitempty" example:"房床厕外"`                    // 胎神
		TaishenLocation     string             `json:"taishenLocation,omitempty" example:"西北"`              // 胎神位置
		Xingxiu             string             `json:"xingxiu,omitempty" example:"张月鹿"`                     // 星宿
		Pengzubaiji         []string           `json:"pengzubaiji,omitempty" example:"乙不栽植 千株不长,未不服药 毒气入肠"` // 彭祖百忌
		Jieqi               string             `json:"jieqi,omitempty" example:"大雪"`                        // 节气（今日或之前的最后一个节气）
		JieqiDate           string             `json:"jieqiDate,omitempty" example:"2006-01-02"`            // 节气日期
		JieqiTime           string             `json:"jieqiTime,omitempty" example:"2006-01-02 15:00:59"`   // 节气时间
		Times               []*CalendarShichen `json:"times,omitempty"`                                     // 时辰（共13个，包含早子时与晚子时）
	}
	CalendarDayResponse struct {
		Response
		Data CalendarDayResponseData `json:"data"`
	}
)

type (
	CalendarEachDayOfMonth struct {
		Date         string    `json:"date" bun:"date" example:"2020-01-01"`               // 公历日期
		LunarYear    string    `json:"-" bun:"lunar_year" example:"庚子"`                    // 农历年份
		LunarDate    string    `json:"lunarDate" bun:"lunar_date" example:"二月廿一"`          // 农历日期
		Weekday      string    `json:"weekday" bun:"xingqi" example:"星期四"`                 // 星期
		Yi           []string  `json:"yi" bun:"-" example:"祭祀,打扫,破屋坏垣"`                    // 宜
		YiStr        string    `json:"-" bun:"yi"`                                         // 宜
		Ji           []string  `json:"ji" bun:"-" example:"阴宅破土,安葬,启攒,探亲访友"`               // 忌
		JiStr        string    `json:"-" bun:"ji"`                                         // 忌
		Bazi1        string    `json:"-" bun:"bazi_1" example:"庚申"`                        // 八字
		Bazi2        string    `json:"-" bun:"bazi_2" example:"壬午"`                        // 八字
		Bazi2Next    string    `json:"-" bun:"bazi_2_next" example:"壬午"`                   // 八字
		Bazi3        string    `json:"bazi" bun:"bazi_3" example:"庚申"`                     // 八字
		Jieri        []string  `json:"jieri,omitempty" bun:"-" example:"北方小年,南方小年"`        // 节日
		JieriStr     string    `json:"-" bun:"jieri"`                                      // 节日
		Jieqi        string    `json:"jieqi,omitempty" bun:"jieqi" example:"大雪"`           // 节气
		JieqiTime    time.Time `json:"-" bun:"jieqi_time" example:"2006-01-02 15:00:59"`   // 节气时间
		JieqiTimeStr string    `json:"jieqiTime,omitempty" bun:"-"`                        // 节气时间
		CurrentMonth bool      `json:"currentMonth,omitempty" bun:"-" example:"true"`      // 是否为当前月份
		HolidayOff   int       `json:"holidayOff,omitempty" bun:"holiday_off" example:"1"` // 节假日调休：1休，2班
		LiuriShensha []string  `json:"liuriShensha,omitempty" bun:"-"`                     // 流日神煞
		VipShishen   [2]string `json:"vipShishen,omitempty" bun:"-"`                       // VIP的干十神与支十神
	}
	CalendarMonthRequest struct {
		Month string       `json:"month" example:"2020-01"` // 公历月份
		User  *jwthub.Auth `json:"-"`
		AppID int64        `json:"appID"` // 应用：2-排盘、3-万年历、4-运势、5-论财
	}
	CalendarMonthResponseData = [][]*CalendarEachDayOfMonth
	CalendarMonthResponse     struct {
		Response
		Data CalendarMonthResponseData `json:"data"`
	}
)

type (
	CalendarDayDoWhatRequest struct {
		MingliID *int64       `json:"mingliID" example:"1"`               // 命例ID
		Time     string       `json:"time" example:"2021-01-01 12:00:00"` // 时间
		User     *jwthub.Auth `json:"-"`
		AppID    int64        `json:"appID"` // 应用：2-排盘、3-万年历、4-运势、5-论财
	}

	CalendarDayDoWhatResponseData struct {
		LunarDay             string             `json:"lunarDay"`                                  // 1.0 农历日期
		FateScore            float64            `json:"fateScore" example:"75"`                    // 1.1 今日运势分数
		TenTianganPower      map[string]float32 `json:"tenTianganPower"`                           // 1.2.1 流日十天干能量
		TenGodPower          map[string]float32 `json:"tenGodPower"`                               // 1.2.1 流日十神能量
		PropertyScore        float64            `json:"propertyScore" example:"75"`                // 1.2.2 今日财运分数
		LoveScore            float64            `json:"loveScore" example:"75"`                    // 1.2.3 桃花分数
		HealthScore          float64            `json:"healthScore" example:"75"`                  // 1.2.4 健康分数
		HealthWarning        bool               `json:"healthWarning" example:"true"`              // 1.2.4 健康预警
		Zhishen              string             `json:"zhishen"`                                   // 1.3 每日建议（根据值神匹配）
		YiWhat               []string           `json:"yiWhat" example:"祭祀,打扫,破屋坏垣"`               // 1.4 每日宜what
		JiWhat               []string           `json:"jiWhat" example:"阴宅破土,安葬,启攒,探亲访友"`          // 每日忌what
		Dressing             []string           `json:"dressing"`                                  // 1.5 每日穿衣五行
		LuckyNum             int                `json:"luckyNum" example:"8"`                      // 1.6 幸运数字
		PropertyLocation     []string           `json:"propertyLocation" example:"东南"`             // 1.7 财位
		PianPropertyLocation []string           `json:"pianPropertyLocation" example:"东南"`         //  偏财位
		PianPropertyClock    []int              `json:"pianPropertyClock" example:"1,2,3"`         //  偏财时辰
		LoveLocation         []string           `json:"loveLocation" example:"东南"`                 // 1.7 桃花位
		Guiren               string             `json:"guiren,omitempty" example:"天乙贵人"`           // 1.8 贵人
		JiaoyunTime          string             `json:"jiaoyunTime" example:"2021-01-01 12:00:00"` // 1.9 交运时间
		HasJiaoyun           bool               `json:"hasJiaoyun" example:"true"`                 // 是否已交运
	}
	CalendarDayDoWhatResponse struct {
		Response
		Data CalendarDayDoWhatResponseData `json:"data"`
	}
)

type (
	GetJiaoyunRequest struct {
		Birthtime  string   `json:"birthtime" binding:"required" example:"2006-01-02 15:04:05"` // 生日
		Gender     string   `json:"gender" binding:"required,oneof=男 女" example:"男"`
		Birthplace []string `json:"birthplace"` // 出生地
	}
	GetJiaoyunResponseData struct {
		JiaoyunTime string `json:"jiaoyunTime" example:"2021-01-01 12:00:00"` // 交运时间
	}
	GetJiaoyunResponse struct {
		Response
		Data GetJiaoyunResponseData `json:"data"`
	}
)
