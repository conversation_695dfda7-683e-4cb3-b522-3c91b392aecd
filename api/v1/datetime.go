package v1

import "zodiacus/third_party/corona"

type (
	GetDatetimeBySizhuRequest        = corona.GetDatetimeBySizhuRequest
	GetDatetimeFromSizhuResponseData = corona.GetDatetimeBySizhuResponse
	GetDatetimeBySizhuResponse       struct {
		Response
		Data GetDatetimeFromSizhuResponseData `json:"data"`
	}
)

type (
	GetBaziRequest struct {
		Birthtime string `json:"birthtime" binding:"required" example:"2006-01-02 15:03:04"` // 出生时间：2006-01-02 15:03:04
		Gender    int    `json:"gender" binding:"required" example:"1"`                      // 性别：1-男、2-女
	}
	GetBaziResponseData struct {
		Bazi []string `json:"bazi"`
	}
	GetBaziResponse struct {
		Response
		Data GetBaziResponseData `json:"data"`
	}
)
