package v1

import (
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"net/http"
	"strings"
	"time"
)

type Media struct {
	Data        []byte
	ContentType string
}

// Response is the wrapper of response data.
type Response struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data"`
}

// PagerIn is the wrapper of request data with pagination.
type PagerIn[T any] struct {
	PageNum  int `json:"pageNum" binding:"required,gte=1" example:"1"`
	PageSize int `json:"pageSize" binding:"required,gte=1" example:"10"`
	Param    T   `json:"param"`
}

func (slf *PagerIn[T]) Limit() int {
	return slf.PageSize
}

func (slf *PagerIn[T]) Offset() int {
	return (slf.PageNum - 1) * slf.PageSize
}

// PagerOut is the wrapper of response data with pagination.
type PagerOut[T any] struct {
	Total int `json:"total"`
	List  []T `json:"list"`
}

type TimeRange struct {
	StartAt time.Time `json:"startAt" binding:"required"` // 开始时间：RFC3339 / ISO8601 标准格式
	EndAt   time.Time `json:"endAt" binding:"required"`   // 结束时间：RFC3339 / ISO8601 标准格式
}

type HTTPError struct {
	Code     int
	Message  string
	internal error
	basic    bool
}

// Wrap an error to HTTPError, only support basic HTTPError.
func (slf *HTTPError) Wrap(err error) *HTTPError {
	if slf.basic {
		slf.internal = err
	}
	return slf
}

func (slf *HTTPError) Unwrap() error {
	return slf.internal
}

func (slf *HTTPError) Error() string {
	return slf.Message
}

func (slf *HTTPError) IsBasic() bool {
	return slf.basic
}

func (slf *HTTPError) CustomMessage(str string) *HTTPError {
	slf.Message = str
	return slf
}

// newHTTPError create a new HTTPError instance, if code is http status code, default message will be http status text.
func newHTTPError(code int, msg ...string) *HTTPError {
	err := &HTTPError{Code: code}
	errMsg := http.StatusText(code)
	if errMsg != "" {
		err.basic = true
		if code == http.StatusOK {
			err.Code = 0
		}
		err.Message = errMsg
	}
	if len(msg) != 0 {
		err.Message = msg[0]
	}
	return err
}

func HandleSuccess(ctx *gin.Context, data any, httpStatusCode ...int) {
	var httpCode int
	if data == nil {
		data = nil
	}
	if len(httpStatusCode) != 0 {
		httpCode = httpStatusCode[0]
	} else {
		httpCode = http.StatusOK
	}

	switch v := data.(type) {
	case Media:
		ctx.Data(httpCode, v.ContentType, v.Data)
	case *Media:
		ctx.Data(httpCode, v.ContentType, v.Data)
	default:
		resp := Response{Code: ErrNop.Code, Message: ErrNop.Error(), Data: data}
		ctx.JSON(httpCode, resp)
	}
}

func HandleError(ctx *gin.Context, err error, httpStatusCode ...int) {
	var (
		httpErr      *HTTPError
		validatorErr validator.ValidationErrors
		httpCode     int
		resp         Response
	)
	if len(httpStatusCode) != 0 {
		httpCode = httpStatusCode[0]
	}

	if errors.As(err, &httpErr) {
		if httpErr.Unwrap() != nil {
			err = httpErr.Unwrap()
		}
		if httpCode == 0 {
			if httpErr.IsBasic() && !errors.Is(httpErr, ErrNop) {
				httpCode = httpErr.Code
			} else {
				httpCode = http.StatusOK
			}
		}
	}
	switch {
	case errors.As(err, &httpErr):
		resp.Code = httpErr.Code
		resp.Message = httpErr.Error()
	case errors.As(err, &validatorErr):
		if httpCode == 0 {
			httpCode = http.StatusBadRequest
		}
		resp.Code = http.StatusBadRequest
		var tips []string
		for _, e := range validatorErr {
			tips = append(tips, e.Error())
		}
		resp.Message = "invalid params: " + strings.Join(tips, ", ")
	default:
		if httpCode == 0 {
			httpCode = http.StatusOK
		}
		resp.Code = http.StatusOK
		resp.Message = http.StatusText(httpCode)
	}
	ctx.JSON(httpCode, resp)
}
