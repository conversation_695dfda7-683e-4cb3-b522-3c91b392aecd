package v1

import (
	"zodiacus/pkg/jwthub"
	"zodiacus/third_party/casdoor"
	"zodiacus/third_party/corona"
)

type (
	GaoKaoRequest struct {
		Name       string       `json:"name" example:"张三"`                                          // 姓名
		Gender     string       `json:"gender" binding:"required,oneof=男 女" example:"男"`            // 性别
		Birthtime  string       `json:"birthtime" binding:"required" example:"2006-01-02 15:04:05"` // 生日
		Birthplace []string     `json:"birthplace"`                                                 // 出生地
		StartYear  int          `json:"startYear" binding:"required" example:"2025"`                // 开始年份
		User       *jwthub.Auth `json:"-"`                                                          // 用户信息
		UserAgent  string       `json:"-"`                                                          // UA
		IP         string       `json:"-"`                                                          // IP
	}
	GaoKaoResponseDataBasicInfo struct {
		Name           string                           `json:"name"`           // 姓名
		Gender         string                           `json:"gender"`         // 性别
		Birthtime      string                           `json:"birthtime"`      // 出生时间（用户输入）
		BirthtimeSolar string                           `json:"birthtimeSolar"` // 出生时间公历（真太阳时）
		BirthtimeLunar string                           `json:"birthtimeLunar"` // 出生时间农历（真太阳时）
		Birthplace     []string                         `json:"birthplace"`     // 出生地
		Bazi           []string                         `json:"bazi"`           // 八字（四柱）
		Xiyong         []string                         `json:"xiyong"`         // 五行喜用：用喜仇忌闲
		Power          []*corona.PaipanShishenPowerItem `json:"power"`          // 五行/十神/十天干能量
	} // 基本信息
	GaoKaoResponseDataWuxingXiyong struct {
		SystemTip      string    `json:"systemTip"`      // 默认提示
		SystemSay      string    `json:"systemSay"`      // 论玄说
		Occupation     [2]string `json:"occupation"`     // 行业：1-用、2-喜
		MoreSuggestion []string  `json:"moreSuggestion"` // 更多建议
	} // 2.2 五行喜用——选行业
	GaoKaoResponseDataShishenTianfu struct {
		SystemTip          string   `json:"systemTip"`          // 默认提示
		SystemSay          string   `json:"systemSay"`          // 论玄说
		StrongestShishen   []string `json:"strongestShishen"`   // 最强十神
		StrongestAbility   []string `json:"strongestAbility"`   // 最强十神能力
		SuggestedCareer    string   `json:"suggestedCareer"`    // 建议职业
		WeakestShishen     []string `json:"weakestShishen"`     // 最弱十神
		WeakestSuperiority []string `json:"weakestSuperiority"` // 最弱十神弱势
	} // 2.3 十神天赋——看方向
	GaoKaoResponseDataFavorableChoice struct {
		SystemTip       string `json:"systemTip"`       // 默认提示
		SystemSay       string `json:"systemSay"`       // 论玄说
		ChoiceDirection string `json:"choiceDirection"` // 选择方向
		ChoiceArea      string `json:"choiceArea"`      // 选择区域
	} // 2.4 有利提升——选城市

	GaoKaoResponseDataFutureDayunStage struct {
		StateNum  int                                          `json:"stateNum"`  // 阶段编号（1开始）
		StartYear int                                          `json:"startYear"` // 开始年份
		EndYear   int                                          `json:"endYear"`   // 结束年份
		Shishen   []*GaoKaoResponseDataFutureDayunStageShishen `json:"shishen"`   // 十神（一个或两个）
	}
	GaoKaoResponseDataFutureDayunStageShishen struct {
		DayunNum  int                                              `json:"dayunNum"`  // 大运编号（1开始）
		StartYear int                                              `json:"startYear"` // 开始年份
		EndYear   int                                              `json:"endYear"`   // 结束年份
		Name      string                                           `json:"name"`      // 十神名称
		Items     []*GaoKaoResponseDataFutureDayunStageShishenItem `json:"items"`     // 特征与建议
		SystemSay string                                           `json:"systemSay"` // 论玄说
	}
	GaoKaoResponseDataFutureDayunStageShishenItem struct {
		TypeName string `json:"typeName"`          // 类型名称
		Type     int    `json:"type,omitempty"`    // 类型：1-特征、2-建议
		Content  string `json:"content,omitempty"` // 内容
	}
	GaokaoResponseDataFutureDayun struct {
		SystemTip string                                `json:"systemTip"` // 默认提示
		Stages    []*GaoKaoResponseDataFutureDayunStage `json:"stages"`    // 阶段（共四个）
	} // 2.5 未来大运——看发展
	GaoKaoResponseData struct {
		StartYear       int                                `json:"startYear" example:"2025"` // 开始年份
		BasicInfo       *GaoKaoResponseDataBasicInfo       `json:"basicInfo"`                // 基本信息
		WuxingXiyong    *GaoKaoResponseDataWuxingXiyong    `json:"wuxingXiyong"`             // 五行喜用-选行业
		ShishenTianfu   *GaoKaoResponseDataShishenTianfu   `json:"shishenTianfu"`            // 十神天赋-看方向
		FavorableChoice *GaoKaoResponseDataFavorableChoice `json:"favorableChoice"`          // 有利选择
		FutureDayun     *GaokaoResponseDataFutureDayun     `json:"futureDayun"`              // 未来大运
	}
	GaoKaoResponse struct {
		Response
		Data *GaoKaoResponseData `json:"data"`
	}
)

type (
	GaoKaoPreferenceRequest struct {
		Name       string        `json:"name" example:"张三"`                                          // 姓名
		Gender     string        `json:"gender" binding:"required,oneof=男 女" example:"男"`            // 性别
		Birthtime  string        `json:"birthtime" binding:"required" example:"2006-01-02 15:04:05"` // 生日
		Birthplace []string      `json:"birthplace"`                                                 // 出生地
		StartTime  string        `json:"startTime" example:"2025-07-07 08:00:00"`                    // 选择时间开始
		EndTime    string        `json:"endTime" example:"2025-07-07 18:00:00"`                      // 选择时间结束
		User       *casdoor.User `json:"-"`                                                          // 用户信息
		UserAgent  string        `json:"-"`                                                          // UA
		IP         string        `json:"-"`                                                          // IP

	}
	GaoKaoPreferenceResponseData struct {
		SystemTip  string      `json:"systemTip"`  // 默认提示
		SystemSay  string      `json:"systemSay"`  // 论玄说
		Times      []int       `json:"times"`      // 小时序号
		TimeRanges [][2]string `json:"timeRanges"` // 时间段（开始时间与结束时间）
	}
	GaoKaoPreferenceResponse struct {
		Response
		Data *GaoKaoPreferenceResponseData `json:"data"`
	}
)
