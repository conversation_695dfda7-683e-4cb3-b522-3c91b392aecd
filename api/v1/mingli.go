package v1

import (
	"time"
	"zodiacus/internal/model"
	"zodiacus/pkg/jwthub"
	"zodiacus/third_party/corona"
)

type UserMingli struct {
	ID              int64                    `json:"id" bun:"id" example:"1"`                                         // ID
	UserID          string                   `json:"userID" bun:"user_id" example:"1"`                                // 用户ID
	Name            string                   `json:"name" bun:"name" example:"张三"`                                    // 姓名
	Gender          int                      `json:"gender" bun:"gender" example:"1"`                                 // 性别：1-男，2-女
	Birthtime       time.Time                `json:"-" bun:"birthtime" example:"2021-01-01 12:00:00"`                 // 出生时间（公历）
	BirthtimeStr    string                   `json:"birthtime" bun:"-" example:"2021-01-01 12:00:00"`                 // 出生时间（公历）
	BirthtimeSun    time.Time                `json:"-" bun:"birthtime_sun" example:"2021-01-01 12:00:00"`             // 真太阳时
	BirthtimeSunStr string                   `json:"birthtimeSun" bun:"-" example:"2021-01-01 12:00:00"`              // 真太阳时
	LunarBirthtime  string                   `json:"lunarBirthtime" bun:"-" example:"2021年闰四月十一子时"`                   // 出生时间（农历）
	BirthtimeLunar  string                   `json:"birthtimeLunar" bun:"birthtime_lunar" example:"2021年闰四月十一子时"`     // 出生时间（农历）
	Address         []string                 `json:"address" bun:"-" example:"北京市,市辖区,东城区"`                           // 地址
	Birthplace      []string                 `json:"birthplace" bun:"birthplace" example:"[\"北京市\",\"市辖区\",\"东城区\"]"` // 出生地
	Bazi            []string                 `json:"bazi" bun:"bazi"`                                                 // 八字：年份干支,月份干支,日期干支,时辰干支
	IsDefault       bool                     `json:"isDefault" bun:"is_default" example:"true"`                       // 是否默认
	AppID           int64                    `json:"appID" bun:"app_id" example:"2"`                                  // 应用：2-排盘、3-万年历、4-运势、5-论财
	AppName         string                   `json:"appName,omitempty" bun:"app_name"`                                // 应用名称
	GroupID         int64                    `json:"groupID" bun:"group_id" example:"1"`                              // 分组ID
	GroupName       string                   `json:"groupName,omitempty" bun:"group_name"`                            // 分组名称
	Wuxing          []string                 `json:"wuxing,omitempty" bun:"wuxing"`                                   // 五行：用神,喜神,忌神,仇神,闲神
	Xiaoyun         *model.UserMingliXiaoyun `json:"xiaoyun,omitempty" bun:"xiaoyun"`                                 // 小运
	Dayun           *model.UserMingliDayun   `json:"dayun,omitempty" bun:"dayun"`                                     // 大运
}

type (
	CreateUserMingliRequest struct {
		User      *jwthub.Auth `json:"-"`
		Name      string       `json:"name" binding:"required" example:"张三"`                       // 姓名
		Gender    int          `json:"gender" binding:"required" example:"1"`                      // 性别：1-男，2-女
		Birthtime string       `json:"birthtime" binding:"required" example:"2021-01-01 12:00:00"` // 出生时间（公历）
		Address   []string     `json:"address" example:"北京市,市辖区,东城区"`                              // 地址
		IsDefault *bool        `json:"isDefault" example:"true"`                                   // 是否默认
		AppID     int64        `json:"appID" example:"2"`                                          // 应用：2-排盘、3-万年历、4-运势、5-论财
		GroupID   int64        `json:"groupID" example:"1"`                                        // 分组ID
	}
	CreateUserMingliResponseData struct {
		ID int64 `json:"id" example:"1"` // ID
	}
	CreateUserMingliResponse struct {
		Response
	}
)

type (
	UpdateUserMingliRequest struct {
		ID        int64        `json:"id" binding:"required" example:"1"` // ID
		User      *jwthub.Auth `json:"-"`
		Name      string       `json:"name" binding:"required" example:"张三"`                       // 姓名
		Gender    int          `json:"gender" binding:"required" example:"1"`                      // 性别：1-男，2-女
		Birthtime string       `json:"birthtime" binding:"required" example:"2021-01-01 12:00:00"` // 出生时间（公历）
		Address   []string     `json:"address" example:"[\"北京市\",\"市辖区\",\"东城区\"]"`                // 地址
		IsDefault *bool        `json:"isDefault" example:"true"`                                   // 是否默认
	}
	UpdateUserMingliResponse struct {
		Response
	}
)

type (
	DeleteUserMingliRequest struct {
		ID   int64        `json:"id" binding:"required" example:"1"` // ID
		User *jwthub.Auth `json:"-"`
	}
	DeleteUserMingliResponse struct {
		Response
	}
)

type (
	SetDefaultUserMingliRequest struct {
		ID   int64        `json:"id" binding:"required" example:"1"` // ID
		User *jwthub.Auth `json:"-"`
	}
	SetDefaultUserMingliResponse struct {
		Response
	}
)

type (
	SetMingliWuxingRequest struct {
		ID     int64        `json:"id" binding:"required" example:"1"` // ID
		User   *jwthub.Auth `json:"-"`
		Wuxing []string     `json:"wuxing"` // 五行：用神,喜神,忌神,仇神,闲神
	}
	SetMingliWuxingResponse struct {
		Response
	}
)

type (
	ListUserMingliRequest struct {
		User    *jwthub.Auth `json:"-"`
		AppID   int64        `json:"appID"`
		GroupID int64        `json:"groupID"`
	}
	ListUserMingliResponseData []*UserMingli
	ListUserMingliResponse     struct {
		Response
		Data ListUserMingliResponseData `json:"data"`
	}
)

type (
	PageListUserMingliRequestParam struct {
		Name              *string `json:"name"`              // 命例名称
		Gender            []int   `json:"gender"`            // 性别：1-男、2-女
		BirthTimeStart    *string `json:"birthTimeStart"`    // 出生日期开始
		BirthTimeEnd      *string `json:"birthTimeEnd"`      // 出生日期结束
		Bazi              *string `json:"bazi"`              // 八字
		Birthplace        *string `json:"birthplace"`        // 出生地
		BirthTimeSunStart *string `json:"birthTimeSunStart"` // 真太阳时开始
		BirthTimeSunEnd   *string `json:"birthTimeSunEnd"`   // 真太阳时结束
		AppIDs            []int   `json:"appIDs"`            // 应用ID：2-排盘、3-万年历、4-运势、5-论财
	}
	PageListUserMingliRequest          = PagerIn[PageListUserMingliRequestParam]
	PageListUserMingliResponseDataItem = UserMingli
	PageListUserMingliResponseData     = PagerOut[*PageListUserMingliResponseDataItem]
	PageListUserMingliResponse         struct {
		Response
		Data PageListUserMingliResponseData `json:"data"`
	}
)

type (
	PaipanRequest struct {
		User      *jwthub.Auth `json:"-"`
		MingliID  int64        `json:"mingliID" example:"1"`               // 命例ID
		Time      string       `json:"time" example:"2021-01-01 12:00:00"` // 时间
		UserAgent string       `json:"-"`
		IP        string       `json:"-"`
	}
	PaipanResponseData = corona.GetPaipanJiuzhuResponseData
	PaipanResponse     struct {
		Response
		Data PaipanResponseData `json:"data"`
	}
)

type (
	MingliDayunliulianScoreRequest struct {
		User     *jwthub.Auth `json:"-"`
		MingliID int64        `json:"mingliID" example:"1"` // 命例ID
	}
	MingliDayunliulianScoreResponseData = corona.GetDayunLiunianScoreResponse
	MingliDayunliulianScoreResponse     struct {
		Response
		Data MingliDayunliulianScoreResponseData `json:"data"`
	}
)

type (
	MingliJieqiScoreRequest struct {
		User     *jwthub.Auth `json:"-"`
		MingliID int64        `json:"mingliID" example:"1"`               // 命例ID
		Time     string       `json:"time" example:"2021-01-01 12:00:00"` // 时间
	}
	MingliJieqiScoreResponseData = corona.GetJieqiScoreResponse
	MingliJieqiScoreResponse     struct {
		Response
		Data MingliJieqiScoreResponseData `json:"data"`
	}
)
