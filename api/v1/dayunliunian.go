package v1

type (
	DayunliunianRequest struct {
		Birthtime  string   `json:"birthtime" binding:"required" example:"2020-01-01 12:00:00"` // 出生时间
		Birthplace []string `json:"birthplace"`                                                 // 出生地
		Gender     int      `json:"gender" binding:"required" example:"1"`                      // 性别：1-男，2-女
	}
	DayunliunianResponse struct {
		Response
		Data *DayunliunianResponseData `json:"data"`
	}
	DayunliunianResponseData struct {
		Birthtime                string   `json:"birthtime"`                // 出生时间（太阳时）
		BirthtimeLunar           string   `json:"birthtimeLunar"`           // 出生时间（农历）
		JiaoyunTime              string   `json:"jiaoyunTime"`              // 交运时间（公历）
		XiaoyunYears             int      `json:"xiaoyunYears"`             // 小运年数
		XiaoyunQishi             int      `json:"xiaoyunQishi"`             // 小运起始年份（农历）
		XiaoyunJiezhi            int      `json:"xiaoyunJiezhi"`            // 小运结束年份（农历）
		DayunQishi               int      `json:"dayunQishi"`               // 大运起始年份（农历）
		DayunJiezhi              int      `json:"dayunJiezhi"`              // 大运结束年份（农历）
		DayunScoreList           []int    `json:"dayunScoreList"`           // 大运评分：24个，每两个组成一个大运
		LiunianScoreList         []int    `json:"liunianScoreList"`         // 流年评分：n+120个，小限流年n+大运流年120
		XiaoyunScoreList         []int    `json:"xiaoxianScoreList"`        // 小运评分：n个
		ZongheScoreList          []int    `json:"zongheScoreList"`          // 综合评分：n+120个，小限流年n+大运流年120
		DayunGanzhiList          []string `json:"dayunGanzhiList"`          // 大运干支列表：12个
		DayunLiunianGanzhiList   []string `json:"dayunLiunianGanzhiList"`   // 大运流年干支列表：120个
		XiaoyunGanzhiList        []string `json:"xiaoyunGanzhiList"`        // 小运干支列表：n个
		XiaoyunLiunianGanzhiList []string `json:"xiaoyunLiunianGanzhiList"` // 小运流年干支列表：n个
	}
)

type (
	DayunAnalysisRequest struct {
		Birthtime  string   `json:"birthtime" binding:"required" example:"2020-01-01 12:00:00"` // 出生时间
		Birthplace []string `json:"birthplace"`                                                 // 出生地
		Gender     int      `json:"gender" binding:"required" example:"1"`                      // 性别：1-男，2-女
		Dayun      string   `json:"dayun" binding:"required"`                                   // 所选大运
	}
	DayunAnalysisResponse struct {
		Response
		Data *DayunAnalysisResponseData `json:"data"`
	}
	DayunAnalysisResponseData = MasterDayunAnalysis
)

type (
	LiunianAnalysisRequest struct {
		Birthtime  string   `json:"birthtime" binding:"required" example:"2020-01-01 12:00:00"` // 出生时间
		Birthplace []string `json:"birthplace"`                                                 // 出生地
		Gender     int      `json:"gender" binding:"required" example:"1"`                      // 性别：1-男，2-女
		Dayun      string   `json:"dayun,omitempty"`                                            // 所选大运（小运时为空，大运流年时必须）
		Liunian    string   `json:"liunian" binding:"required"`                                 // 所选流年（小运流年/大运流年）
	}
	LiunianAnalysisResponse struct {
		Response
		Data *LiunianAnalysisResponseData `json:"data"`
	}
	LiunianAnalysisResponseData = MasterLiunianAnalysis
)
