package v1

import "net/http"

var (
	ErrNop                 = newHTTPError(http.StatusOK)
	ErrBadRequest          = newHTTPError(http.StatusBadRequest)
	ErrUnauthorized        = newHTTPError(http.StatusUnauthorized)
	ErrNotFound            = newHTTPError(http.StatusNotFound)
	ErrInternalServerError = newHTTPError(http.StatusInternalServerError)

	ErrUnsupportedFileType = newHTTPError(1005, "不支持的文件类型")
	ErrInputYearOutOfRange = newHTTPError(2001, "年份超出范围")

	ErrUserMingliNotFound = newHTTPError(3001, "命例不存在")

	ErrMingliRuleNoAlreadyTaken          = newHTTPError(4001, "命理规则编号已被使用")
	ErrMingliRuleNotFound                = newHTTPError(4002, "命理规则不存在")
	ErrMingliRuleConditionNoAlreadyTaken = newHTTPError(4003, "命理规则条件编号已被使用")
	ErrMingliRuleConditionNotFound       = newHTTPError(4004, "命理规则条件不存在")

	ErrQwContactWayAlreadyTaken = newHTTPError(5001, "联系我已存在")
	ErrQwContactWayNotFound     = newHTTPError(5002, "联系我不存在")

	ErrOaQrcodeSceneStrAlreadyTaken = newHTTPError(6001, "场景值已被使用")

	ErrAppVersionNameAlreadyTaken = newHTTPError(7001, "版本名称已被使用")
	ErrAppVersionNotFound         = newHTTPError(7002, "版本不存在")
	ErrAppVersionIsPublished      = newHTTPError(7003, "版本已发布，无法修改")
	ErrAppVersionIsNotPublished   = newHTTPError(7003, "版本尚未发布")

	ErrPortalSectionHasArticle       = newHTTPError(8001, "栏目下存在文章，无法删除")
	ErrPortalSectionNameAlreadyTaken = newHTTPError(8002, "栏目已存在")
	ErrPortalSectionNotFound         = newHTTPError(8003, "栏目不存在")
	ErrPortalArticleNotFound         = newHTTPError(8004, "文章不存在")

	ErrInvitationOnlyOnce    = newHTTPError(9001, "您已领过了该福利。\n可邀请他人继续获取福利。")
	ErrInvitationCodeInvalid = newHTTPError(9002, "邀请码无效")
	ErrInvitationSameUser    = newHTTPError(9003, "不能邀请自己")
)
