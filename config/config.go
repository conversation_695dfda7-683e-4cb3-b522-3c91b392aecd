package config

import (
	"github.com/spf13/viper"
	"os"
)

// ENV is the environment of the application; options are: local, test, prod; default is local.
var (
	ENV     = "local"
	AppName string
)

func NewConfig(path string) *viper.Viper {
	envConf := os.Getenv("APP_CONF")
	if envConf == "" {
		envConf = path
	}
	return newConfig(envConf)
}

func newConfig(path string) *viper.Viper {
	conf := viper.New()
	conf.SetConfigFile(path)
	err := conf.ReadInConfig()
	if err != nil {
		panic(err)
	}
	ENV = conf.GetString("env")
	return conf
}
